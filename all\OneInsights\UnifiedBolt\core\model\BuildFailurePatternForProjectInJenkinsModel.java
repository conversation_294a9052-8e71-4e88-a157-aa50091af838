/**
 * 
 */
package com.bolt.dashboard.core.model;

import java.util.ArrayList;
import java.util.List;

import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 *
 */
@Document(collection = "BuildFailurePatternForProject")
public class BuildFailurePatternForProjectInJenkinsModel extends BaseModel {

    private String projectName;
    private String userName;
    private List<BuildFailurePatternMetrics> patternMetrics = new ArrayList();
    private long timestampOfCreation;

    /**
     * 
     */
    public BuildFailurePatternForProjectInJenkinsModel() {

    }

    /**
     * @return the projectName
     */
    public String getProjectName() {
        return projectName;
    }

    /**
     * @param projectName
     *            the projectName to set
     */
    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    /**
     * @return the patternDefined
     */

    /**
     * @return the patternMetrics
     */
    public List<BuildFailurePatternMetrics> getPatternMetrics() {
        return patternMetrics;
    }

    /**
     * @param patternMetrics
     *            the patternMetrics to set
     */
    public void setPatternMetrics(List<BuildFailurePatternMetrics> patternMetrics) {
        this.patternMetrics = patternMetrics;
    }

    /**
     * @return the timestampOfCreation
     */
    public long getTimestampOfCreation() {
        return timestampOfCreation;
    }

    /**
     * @param timestampOfCreation
     *            the timestampOfCreation to set
     */
    public void setTimestampOfCreation(long timestampOfCreation) {
        this.timestampOfCreation = timestampOfCreation;
    }

    /**
     * The method to assign the string from the request to Model.
     * 
     * @param patternDefined
     * @param patternDisplayed
     */
    public void addPatternMetric(String patternDefined, String patternDisplayed, long patternCount) {
        BuildFailurePatternMetrics buildFailurePatternMetrics = new BuildFailurePatternMetrics();
        buildFailurePatternMetrics.setPatternDefined(patternDefined);
        buildFailurePatternMetrics.setPatternDisplayed(patternDisplayed);
        buildFailurePatternMetrics.setPatternCount(patternCount);
        this.patternMetrics.add(buildFailurePatternMetrics);
    }

    public void addPatternMetric(String patternDefined, String patternDisplayed) {
        BuildFailurePatternMetrics buildFailurePatternMetrics = new BuildFailurePatternMetrics();
        buildFailurePatternMetrics.setPatternDefined(patternDefined);
        buildFailurePatternMetrics.setPatternDisplayed(patternDisplayed);
        this.patternMetrics.add(buildFailurePatternMetrics);
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

}
