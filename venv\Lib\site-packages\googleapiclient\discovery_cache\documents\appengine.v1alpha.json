{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/appengine.admin": {"description": "View and manage your applications deployed on Google App Engine"}, "https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}, "https://www.googleapis.com/auth/cloud-platform.read-only": {"description": "View your data across Google Cloud services and see the email address of your Google Account"}}}}, "basePath": "", "baseUrl": "https://appengine.googleapis.com/", "batchPath": "batch", "description": "Provisions and manages developers' App Engine applications.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/appengine/docs/admin-api/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "appengine:v1alpha", "kind": "discovery#restDescription", "mtlsRootUrl": "https://appengine.mtls.googleapis.com/", "name": "appengine", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"apps": {"resources": {"authorizedCertificates": {"methods": {"create": {"description": "Uploads the specified SSL certificate.", "flatPath": "v1alpha/apps/{appsId}/authorizedCertificates", "httpMethod": "POST", "id": "appengine.apps.authorizedCertificates.create", "parameterOrder": ["appsId"], "parameters": {"appsId": {"description": "Part of `parent`. Name of the parent Application resource. Example: apps/myapp.", "location": "path", "required": true, "type": "string"}}, "path": "v1alpha/apps/{appsId}/authorizedCertificates", "request": {"$ref": "AuthorizedCertificate"}, "response": {"$ref": "AuthorizedCertificate"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes the specified SSL certificate.", "flatPath": "v1alpha/apps/{appsId}/authorizedCertificates/{authorizedCertificatesId}", "httpMethod": "DELETE", "id": "appengine.apps.authorizedCertificates.delete", "parameterOrder": ["appsId", "authorizedCertificatesId"], "parameters": {"appsId": {"description": "Part of `name`. Name of the resource to delete. Example: apps/myapp/authorizedCertificates/12345.", "location": "path", "required": true, "type": "string"}, "authorizedCertificatesId": {"description": "Part of `name`. See documentation of `appsId`.", "location": "path", "required": true, "type": "string"}}, "path": "v1alpha/apps/{appsId}/authorizedCertificates/{authorizedCertificatesId}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the specified SSL certificate.", "flatPath": "v1alpha/apps/{appsId}/authorizedCertificates/{authorizedCertificatesId}", "httpMethod": "GET", "id": "appengine.apps.authorizedCertificates.get", "parameterOrder": ["appsId", "authorizedCertificatesId"], "parameters": {"appsId": {"description": "Part of `name`. Name of the resource requested. Example: apps/myapp/authorizedCertificates/12345.", "location": "path", "required": true, "type": "string"}, "authorizedCertificatesId": {"description": "Part of `name`. See documentation of `appsId`.", "location": "path", "required": true, "type": "string"}, "view": {"description": "Controls the set of fields returned in the GET response.", "enum": ["BASIC_CERTIFICATE", "FULL_CERTIFICATE"], "enumDescriptions": ["Basic certificate information, including applicable domains and expiration date.", "The information from BASIC_CERTIFICATE, plus detailed information on the domain mappings that have this certificate mapped."], "location": "query", "type": "string"}}, "path": "v1alpha/apps/{appsId}/authorizedCertificates/{authorizedCertificatesId}", "response": {"$ref": "AuthorizedCertificate"}, "scopes": ["https://www.googleapis.com/auth/appengine.admin", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "list": {"description": "Lists all SSL certificates the user is authorized to administer.", "flatPath": "v1alpha/apps/{appsId}/authorizedCertificates", "httpMethod": "GET", "id": "appengine.apps.authorizedCertificates.list", "parameterOrder": ["appsId"], "parameters": {"appsId": {"description": "Part of `parent`. Name of the parent Application resource. Example: apps/myapp.", "location": "path", "required": true, "type": "string"}, "pageSize": {"description": "Maximum results to return per page.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Continuation token for fetching the next page of results.", "location": "query", "type": "string"}, "view": {"description": "Controls the set of fields returned in the LIST response.", "enum": ["BASIC_CERTIFICATE", "FULL_CERTIFICATE"], "enumDescriptions": ["Basic certificate information, including applicable domains and expiration date.", "The information from BASIC_CERTIFICATE, plus detailed information on the domain mappings that have this certificate mapped."], "location": "query", "type": "string"}}, "path": "v1alpha/apps/{appsId}/authorizedCertificates", "response": {"$ref": "ListAuthorizedCertificatesResponse"}, "scopes": ["https://www.googleapis.com/auth/appengine.admin", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "patch": {"description": "Updates the specified SSL certificate. To renew a certificate and maintain its existing domain mappings, update certificate_data with a new certificate. The new certificate must be applicable to the same domains as the original certificate. The certificate display_name may also be updated.", "flatPath": "v1alpha/apps/{appsId}/authorizedCertificates/{authorizedCertificatesId}", "httpMethod": "PATCH", "id": "appengine.apps.authorizedCertificates.patch", "parameterOrder": ["appsId", "authorizedCertificatesId"], "parameters": {"appsId": {"description": "Part of `name`. Name of the resource to update. Example: apps/myapp/authorizedCertificates/12345.", "location": "path", "required": true, "type": "string"}, "authorizedCertificatesId": {"description": "Part of `name`. See documentation of `appsId`.", "location": "path", "required": true, "type": "string"}, "updateMask": {"description": "Standard field mask for the set of fields to be updated. Updates are only supported on the certificate_raw_data and display_name fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/apps/{appsId}/authorizedCertificates/{authorizedCertificatesId}", "request": {"$ref": "AuthorizedCertificate"}, "response": {"$ref": "AuthorizedCertificate"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "authorizedDomains": {"methods": {"list": {"description": "Lists all domains the user is authorized to administer.", "flatPath": "v1alpha/apps/{appsId}/authorizedDomains", "httpMethod": "GET", "id": "appengine.apps.authorizedDomains.list", "parameterOrder": ["appsId"], "parameters": {"appsId": {"description": "Part of `parent`. Name of the parent Application resource. Example: apps/myapp.", "location": "path", "required": true, "type": "string"}, "pageSize": {"description": "Maximum results to return per page.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Continuation token for fetching the next page of results.", "location": "query", "type": "string"}}, "path": "v1alpha/apps/{appsId}/authorizedDomains", "response": {"$ref": "ListAuthorizedDomainsResponse"}, "scopes": ["https://www.googleapis.com/auth/appengine.admin", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}}}, "domainMappings": {"methods": {"create": {"description": "Maps a domain to an application. A user must be authorized to administer a domain in order to map it to an application. For a list of available authorized domains, see AuthorizedDomains.ListAuthorizedDomains.", "flatPath": "v1alpha/apps/{appsId}/domainMappings", "httpMethod": "POST", "id": "appengine.apps.domainMappings.create", "parameterOrder": ["appsId"], "parameters": {"appsId": {"description": "Part of `parent`. Name of the parent Application resource. Example: apps/myapp.", "location": "path", "required": true, "type": "string"}, "noManagedCertificate": {"description": "Whether a managed certificate should be provided by App Engine. If true, a certificate ID must be manaually set in the DomainMapping resource to configure SSL for this domain. If false, a managed certificate will be provisioned and a certificate ID will be automatically populated.", "location": "query", "type": "boolean"}, "overrideStrategy": {"description": "Whether the domain creation should override any existing mappings for this domain. By default, overrides are rejected.", "enum": ["UNSPECIFIED_DOMAIN_OVERRIDE_STRATEGY", "STRICT", "OVERRIDE"], "enumDescriptions": ["Strategy unspecified. Defaults to STRICT.", "Overrides not allowed. If a mapping already exists for the specified domain, the request will return an ALREADY_EXISTS (409).", "Overrides allowed. If a mapping already exists for the specified domain, the request will overwrite it. Note that this might stop another Google product from serving. For example, if the domain is mapped to another App Engine application, that app will no longer serve from that domain."], "location": "query", "type": "string"}}, "path": "v1alpha/apps/{appsId}/domainMappings", "request": {"$ref": "DomainMapping"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes the specified domain mapping. A user must be authorized to administer the associated domain in order to delete a DomainMapping resource.", "flatPath": "v1alpha/apps/{appsId}/domainMappings/{domainMappingsId}", "httpMethod": "DELETE", "id": "appengine.apps.domainMappings.delete", "parameterOrder": ["appsId", "domainMappingsId"], "parameters": {"appsId": {"description": "Part of `name`. Name of the resource to delete. Example: apps/myapp/domainMappings/example.com.", "location": "path", "required": true, "type": "string"}, "domainMappingsId": {"description": "Part of `name`. See documentation of `appsId`.", "location": "path", "required": true, "type": "string"}}, "path": "v1alpha/apps/{appsId}/domainMappings/{domainMappingsId}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the specified domain mapping.", "flatPath": "v1alpha/apps/{appsId}/domainMappings/{domainMappingsId}", "httpMethod": "GET", "id": "appengine.apps.domainMappings.get", "parameterOrder": ["appsId", "domainMappingsId"], "parameters": {"appsId": {"description": "Part of `name`. Name of the resource requested. Example: apps/myapp/domainMappings/example.com.", "location": "path", "required": true, "type": "string"}, "domainMappingsId": {"description": "Part of `name`. See documentation of `appsId`.", "location": "path", "required": true, "type": "string"}}, "path": "v1alpha/apps/{appsId}/domainMappings/{domainMappingsId}", "response": {"$ref": "DomainMapping"}, "scopes": ["https://www.googleapis.com/auth/appengine.admin", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "list": {"description": "Lists the domain mappings on an application.", "flatPath": "v1alpha/apps/{appsId}/domainMappings", "httpMethod": "GET", "id": "appengine.apps.domainMappings.list", "parameterOrder": ["appsId"], "parameters": {"appsId": {"description": "Part of `parent`. Name of the parent Application resource. Example: apps/myapp.", "location": "path", "required": true, "type": "string"}, "pageSize": {"description": "Maximum results to return per page.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Continuation token for fetching the next page of results.", "location": "query", "type": "string"}}, "path": "v1alpha/apps/{appsId}/domainMappings", "response": {"$ref": "ListDomainMappingsResponse"}, "scopes": ["https://www.googleapis.com/auth/appengine.admin", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "patch": {"description": "Updates the specified domain mapping. To map an SSL certificate to a domain mapping, update certificate_id to point to an AuthorizedCertificate resource. A user must be authorized to administer the associated domain in order to update a DomainMapping resource.", "flatPath": "v1alpha/apps/{appsId}/domainMappings/{domainMappingsId}", "httpMethod": "PATCH", "id": "appengine.apps.domainMappings.patch", "parameterOrder": ["appsId", "domainMappingsId"], "parameters": {"appsId": {"description": "Part of `name`. Name of the resource to update. Example: apps/myapp/domainMappings/example.com.", "location": "path", "required": true, "type": "string"}, "domainMappingsId": {"description": "Part of `name`. See documentation of `appsId`.", "location": "path", "required": true, "type": "string"}, "noManagedCertificate": {"description": "Whether a managed certificate should be provided by App Engine. If true, a certificate ID must be manually set in the DomainMapping resource to configure SSL for this domain. If false, a managed certificate will be provisioned and a certificate ID will be automatically populated. Only applicable if ssl_settings.certificate_id is specified in the update mask.", "location": "query", "type": "boolean"}, "updateMask": {"description": "Required. Standard field mask for the set of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/apps/{appsId}/domainMappings/{domainMappingsId}", "request": {"$ref": "DomainMapping"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "locations": {"methods": {"get": {"description": "Gets information about a location.", "flatPath": "v1alpha/apps/{appsId}/locations/{locationsId}", "httpMethod": "GET", "id": "appengine.apps.locations.get", "parameterOrder": ["appsId", "locationsId"], "parameters": {"appsId": {"description": "Part of `name`. Resource name for the location.", "location": "path", "required": true, "type": "string"}, "locationsId": {"description": "Part of `name`. See documentation of `appsId`.", "location": "path", "required": true, "type": "string"}}, "path": "v1alpha/apps/{appsId}/locations/{locationsId}", "response": {"$ref": "Location"}, "scopes": ["https://www.googleapis.com/auth/appengine.admin", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "list": {"description": "Lists information about the supported locations for this service.", "flatPath": "v1alpha/apps/{appsId}/locations", "httpMethod": "GET", "id": "appengine.apps.locations.list", "parameterOrder": ["appsId"], "parameters": {"appsId": {"description": "Part of `name`. The resource that owns the locations collection, if applicable.", "location": "path", "required": true, "type": "string"}, "extraLocationTypes": {"description": "Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.", "location": "query", "repeated": true, "type": "string"}, "filter": {"description": "A filter to narrow down results to a preferred subset. The filtering language accepts strings like \"displayName=tokyo\", and is documented in more detail in AIP-160 (https://google.aip.dev/160).", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from the next_page_token field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}}, "path": "v1alpha/apps/{appsId}/locations", "response": {"$ref": "ListLocationsResponse"}, "scopes": ["https://www.googleapis.com/auth/appengine.admin", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}}}, "operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1alpha/apps/{appsId}/operations/{operationsId}", "httpMethod": "GET", "id": "appengine.apps.operations.get", "parameterOrder": ["appsId", "operationsId"], "parameters": {"appsId": {"description": "Part of `name`. The name of the operation resource.", "location": "path", "required": true, "type": "string"}, "operationsId": {"description": "Part of `name`. See documentation of `appsId`.", "location": "path", "required": true, "type": "string"}}, "path": "v1alpha/apps/{appsId}/operations/{operationsId}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/appengine.admin", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns UNIMPLEMENTED.", "flatPath": "v1alpha/apps/{appsId}/operations", "httpMethod": "GET", "id": "appengine.apps.operations.list", "parameterOrder": ["appsId"], "parameters": {"appsId": {"description": "Part of `name`. The name of the operation's parent resource.", "location": "path", "required": true, "type": "string"}, "filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1alpha/apps/{appsId}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/appengine.admin", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}}}}}, "projects": {"resources": {"locations": {"methods": {"get": {"description": "Gets information about a location.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}", "httpMethod": "GET", "id": "appengine.projects.locations.get", "parameterOrder": ["projectsId", "locationsId"], "parameters": {"locationsId": {"description": "Part of `name`. See documentation of `projectsId`.", "location": "path", "required": true, "type": "string"}, "projectsId": {"description": "Part of `name`. Resource name for the location.", "location": "path", "required": true, "type": "string"}}, "path": "v1alpha/projects/{projectsId}/locations/{locationsId}", "response": {"$ref": "Location"}, "scopes": ["https://www.googleapis.com/auth/appengine.admin", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "list": {"description": "Lists information about the supported locations for this service.", "flatPath": "v1alpha/projects/{projectsId}/locations", "httpMethod": "GET", "id": "appengine.projects.locations.list", "parameterOrder": ["projectsId"], "parameters": {"extraLocationTypes": {"description": "Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.", "location": "query", "repeated": true, "type": "string"}, "filter": {"description": "A filter to narrow down results to a preferred subset. The filtering language accepts strings like \"displayName=tokyo\", and is documented in more detail in AIP-160 (https://google.aip.dev/160).", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from the next_page_token field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}, "projectsId": {"description": "Part of `name`. The resource that owns the locations collection, if applicable.", "location": "path", "required": true, "type": "string"}}, "path": "v1alpha/projects/{projectsId}/locations", "response": {"$ref": "ListLocationsResponse"}, "scopes": ["https://www.googleapis.com/auth/appengine.admin", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}}, "resources": {"applications": {"resources": {"authorizedCertificates": {"methods": {"create": {"description": "Uploads the specified SSL certificate.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/authorizedCertificates", "httpMethod": "POST", "id": "appengine.projects.locations.applications.authorizedCertificates.create", "parameterOrder": ["projectsId", "locationsId", "applicationsId"], "parameters": {"applicationsId": {"description": "Part of `parent`. See documentation of `projectsId`.", "location": "path", "required": true, "type": "string"}, "locationsId": {"description": "Part of `parent`. See documentation of `projectsId`.", "location": "path", "required": true, "type": "string"}, "projectsId": {"description": "Part of `parent`. Name of the parent Application resource. Example: apps/myapp.", "location": "path", "required": true, "type": "string"}}, "path": "v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/authorizedCertificates", "request": {"$ref": "AuthorizedCertificate"}, "response": {"$ref": "AuthorizedCertificate"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes the specified SSL certificate.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/authorizedCertificates/{authorizedCertificatesId}", "httpMethod": "DELETE", "id": "appengine.projects.locations.applications.authorizedCertificates.delete", "parameterOrder": ["projectsId", "locationsId", "applicationsId", "authorizedCertificatesId"], "parameters": {"applicationsId": {"description": "Part of `name`. See documentation of `projectsId`.", "location": "path", "required": true, "type": "string"}, "authorizedCertificatesId": {"description": "Part of `name`. See documentation of `projectsId`.", "location": "path", "required": true, "type": "string"}, "locationsId": {"description": "Part of `name`. See documentation of `projectsId`.", "location": "path", "required": true, "type": "string"}, "projectsId": {"description": "Part of `name`. Name of the resource to delete. Example: apps/myapp/authorizedCertificates/12345.", "location": "path", "required": true, "type": "string"}}, "path": "v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/authorizedCertificates/{authorizedCertificatesId}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the specified SSL certificate.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/authorizedCertificates/{authorizedCertificatesId}", "httpMethod": "GET", "id": "appengine.projects.locations.applications.authorizedCertificates.get", "parameterOrder": ["projectsId", "locationsId", "applicationsId", "authorizedCertificatesId"], "parameters": {"applicationsId": {"description": "Part of `name`. See documentation of `projectsId`.", "location": "path", "required": true, "type": "string"}, "authorizedCertificatesId": {"description": "Part of `name`. See documentation of `projectsId`.", "location": "path", "required": true, "type": "string"}, "locationsId": {"description": "Part of `name`. See documentation of `projectsId`.", "location": "path", "required": true, "type": "string"}, "projectsId": {"description": "Part of `name`. Name of the resource requested. Example: apps/myapp/authorizedCertificates/12345.", "location": "path", "required": true, "type": "string"}, "view": {"description": "Controls the set of fields returned in the GET response.", "enum": ["BASIC_CERTIFICATE", "FULL_CERTIFICATE"], "enumDescriptions": ["Basic certificate information, including applicable domains and expiration date.", "The information from BASIC_CERTIFICATE, plus detailed information on the domain mappings that have this certificate mapped."], "location": "query", "type": "string"}}, "path": "v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/authorizedCertificates/{authorizedCertificatesId}", "response": {"$ref": "AuthorizedCertificate"}, "scopes": ["https://www.googleapis.com/auth/appengine.admin", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "list": {"description": "Lists all SSL certificates the user is authorized to administer.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/authorizedCertificates", "httpMethod": "GET", "id": "appengine.projects.locations.applications.authorizedCertificates.list", "parameterOrder": ["projectsId", "locationsId", "applicationsId"], "parameters": {"applicationsId": {"description": "Part of `parent`. See documentation of `projectsId`.", "location": "path", "required": true, "type": "string"}, "locationsId": {"description": "Part of `parent`. See documentation of `projectsId`.", "location": "path", "required": true, "type": "string"}, "pageSize": {"description": "Maximum results to return per page.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Continuation token for fetching the next page of results.", "location": "query", "type": "string"}, "projectsId": {"description": "Part of `parent`. Name of the parent Application resource. Example: apps/myapp.", "location": "path", "required": true, "type": "string"}, "view": {"description": "Controls the set of fields returned in the LIST response.", "enum": ["BASIC_CERTIFICATE", "FULL_CERTIFICATE"], "enumDescriptions": ["Basic certificate information, including applicable domains and expiration date.", "The information from BASIC_CERTIFICATE, plus detailed information on the domain mappings that have this certificate mapped."], "location": "query", "type": "string"}}, "path": "v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/authorizedCertificates", "response": {"$ref": "ListAuthorizedCertificatesResponse"}, "scopes": ["https://www.googleapis.com/auth/appengine.admin", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "patch": {"description": "Updates the specified SSL certificate. To renew a certificate and maintain its existing domain mappings, update certificate_data with a new certificate. The new certificate must be applicable to the same domains as the original certificate. The certificate display_name may also be updated.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/authorizedCertificates/{authorizedCertificatesId}", "httpMethod": "PATCH", "id": "appengine.projects.locations.applications.authorizedCertificates.patch", "parameterOrder": ["projectsId", "locationsId", "applicationsId", "authorizedCertificatesId"], "parameters": {"applicationsId": {"description": "Part of `name`. See documentation of `projectsId`.", "location": "path", "required": true, "type": "string"}, "authorizedCertificatesId": {"description": "Part of `name`. See documentation of `projectsId`.", "location": "path", "required": true, "type": "string"}, "locationsId": {"description": "Part of `name`. See documentation of `projectsId`.", "location": "path", "required": true, "type": "string"}, "projectsId": {"description": "Part of `name`. Name of the resource to update. Example: apps/myapp/authorizedCertificates/12345.", "location": "path", "required": true, "type": "string"}, "updateMask": {"description": "Standard field mask for the set of fields to be updated. Updates are only supported on the certificate_raw_data and display_name fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/authorizedCertificates/{authorizedCertificatesId}", "request": {"$ref": "AuthorizedCertificate"}, "response": {"$ref": "AuthorizedCertificate"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "authorizedDomains": {"methods": {"list": {"description": "Lists all domains the user is authorized to administer.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/authorizedDomains", "httpMethod": "GET", "id": "appengine.projects.locations.applications.authorizedDomains.list", "parameterOrder": ["projectsId", "locationsId", "applicationsId"], "parameters": {"applicationsId": {"description": "Part of `parent`. See documentation of `projectsId`.", "location": "path", "required": true, "type": "string"}, "locationsId": {"description": "Part of `parent`. See documentation of `projectsId`.", "location": "path", "required": true, "type": "string"}, "pageSize": {"description": "Maximum results to return per page.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Continuation token for fetching the next page of results.", "location": "query", "type": "string"}, "projectsId": {"description": "Part of `parent`. Name of the parent Application resource. Example: apps/myapp.", "location": "path", "required": true, "type": "string"}}, "path": "v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/authorizedDomains", "response": {"$ref": "ListAuthorizedDomainsResponse"}, "scopes": ["https://www.googleapis.com/auth/appengine.admin", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}}}, "domainMappings": {"methods": {"create": {"description": "Maps a domain to an application. A user must be authorized to administer a domain in order to map it to an application. For a list of available authorized domains, see AuthorizedDomains.ListAuthorizedDomains.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/domainMappings", "httpMethod": "POST", "id": "appengine.projects.locations.applications.domainMappings.create", "parameterOrder": ["projectsId", "locationsId", "applicationsId"], "parameters": {"applicationsId": {"description": "Part of `parent`. See documentation of `projectsId`.", "location": "path", "required": true, "type": "string"}, "locationsId": {"description": "Part of `parent`. See documentation of `projectsId`.", "location": "path", "required": true, "type": "string"}, "noManagedCertificate": {"description": "Whether a managed certificate should be provided by App Engine. If true, a certificate ID must be manaually set in the DomainMapping resource to configure SSL for this domain. If false, a managed certificate will be provisioned and a certificate ID will be automatically populated.", "location": "query", "type": "boolean"}, "overrideStrategy": {"description": "Whether the domain creation should override any existing mappings for this domain. By default, overrides are rejected.", "enum": ["UNSPECIFIED_DOMAIN_OVERRIDE_STRATEGY", "STRICT", "OVERRIDE"], "enumDescriptions": ["Strategy unspecified. Defaults to STRICT.", "Overrides not allowed. If a mapping already exists for the specified domain, the request will return an ALREADY_EXISTS (409).", "Overrides allowed. If a mapping already exists for the specified domain, the request will overwrite it. Note that this might stop another Google product from serving. For example, if the domain is mapped to another App Engine application, that app will no longer serve from that domain."], "location": "query", "type": "string"}, "projectsId": {"description": "Part of `parent`. Name of the parent Application resource. Example: apps/myapp.", "location": "path", "required": true, "type": "string"}}, "path": "v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/domainMappings", "request": {"$ref": "DomainMapping"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the specified domain mapping.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/domainMappings/{domainMappingsId}", "httpMethod": "GET", "id": "appengine.projects.locations.applications.domainMappings.get", "parameterOrder": ["projectsId", "locationsId", "applicationsId", "domainMappingsId"], "parameters": {"applicationsId": {"description": "Part of `name`. See documentation of `projectsId`.", "location": "path", "required": true, "type": "string"}, "domainMappingsId": {"description": "Part of `name`. See documentation of `projectsId`.", "location": "path", "required": true, "type": "string"}, "locationsId": {"description": "Part of `name`. See documentation of `projectsId`.", "location": "path", "required": true, "type": "string"}, "projectsId": {"description": "Part of `name`. Name of the resource requested. Example: apps/myapp/domainMappings/example.com.", "location": "path", "required": true, "type": "string"}}, "path": "v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}/domainMappings/{domainMappingsId}", "response": {"$ref": "DomainMapping"}, "scopes": ["https://www.googleapis.com/auth/appengine.admin", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}}}}}, "operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "appengine.projects.locations.operations.get", "parameterOrder": ["projectsId", "locationsId", "operationsId"], "parameters": {"locationsId": {"description": "Part of `name`. See documentation of `projectsId`.", "location": "path", "required": true, "type": "string"}, "operationsId": {"description": "Part of `name`. See documentation of `projectsId`.", "location": "path", "required": true, "type": "string"}, "projectsId": {"description": "Part of `name`. The name of the operation resource.", "location": "path", "required": true, "type": "string"}}, "path": "v1alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/appengine.admin", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns UNIMPLEMENTED.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "appengine.projects.locations.operations.list", "parameterOrder": ["projectsId", "locationsId"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "locationsId": {"description": "Part of `name`. See documentation of `projectsId`.", "location": "path", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}, "projectsId": {"description": "Part of `name`. The name of the operation's parent resource.", "location": "path", "required": true, "type": "string"}}, "path": "v1alpha/projects/{projectsId}/locations/{locationsId}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/appengine.admin", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}}}}}}}}, "revision": "20250616", "rootUrl": "https://appengine.googleapis.com/", "schemas": {"AuthorizedCertificate": {"description": "An SSL certificate that a user has been authorized to administer. A user is authorized to administer any certificate that applies to one of their authorized domains.", "id": "AuthorizedCertificate", "properties": {"certificateRawData": {"$ref": "CertificateRawData", "description": "The SSL certificate serving the AuthorizedCertificate resource. This must be obtained independently from a certificate authority."}, "displayName": {"description": "The user-specified display name of the certificate. This is not guaranteed to be unique. Example: My Certificate.", "type": "string"}, "domainMappingsCount": {"description": "Aggregate count of the domain mappings with this certificate mapped. This count includes domain mappings on applications for which the user does not have VIEWER permissions.Only returned by GET or LIST requests when specifically requested by the view=FULL_CERTIFICATE option.@OutputOnly", "format": "int32", "type": "integer"}, "domainNames": {"description": "Topmost applicable domains of this certificate. This certificate applies to these domains and their subdomains. Example: example.com.@OutputOnly", "items": {"type": "string"}, "type": "array"}, "expireTime": {"description": "The time when this certificate expires. To update the renewal time on this certificate, upload an SSL certificate with a different expiration time using AuthorizedCertificates.UpdateAuthorizedCertificate.@OutputOnly", "format": "google-datetime", "type": "string"}, "id": {"description": "Relative name of the certificate. This is a unique value autogenerated on AuthorizedCertificate resource creation. Example: 12345.@OutputOnly", "type": "string"}, "managedCertificate": {"$ref": "ManagedCertificate", "description": "Only applicable if this certificate is managed by App Engine. Managed certificates are tied to the lifecycle of a DomainMapping and cannot be updated or deleted via the AuthorizedCertificates API. If this certificate is manually administered by the user, this field will be empty.@OutputOnly"}, "name": {"description": "Full path to the AuthorizedCertificate resource in the API. Example: apps/myapp/authorizedCertificates/12345.@OutputOnly", "type": "string"}, "visibleDomainMappings": {"description": "The full paths to user visible Domain Mapping resources that have this certificate mapped. Example: apps/myapp/domainMappings/example.com.This may not represent the full list of mapped domain mappings if the user does not have VIEWER permissions on all of the applications that have this certificate mapped. See domain_mappings_count for a complete count.Only returned by GET or LIST requests when specifically requested by the view=FULL_CERTIFICATE option.@OutputOnly", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "AuthorizedDomain": {"description": "A domain that a user has been authorized to administer. To authorize use of a domain, verify ownership via Search Console (https://search.google.com/search-console/welcome).", "id": "AuthorizedDomain", "properties": {"id": {"description": "Fully qualified domain name of the domain authorized for use. Example: example.com.", "type": "string"}, "name": {"description": "Full path to the AuthorizedDomain resource in the API. Example: apps/myapp/authorizedDomains/example.com.@OutputOnly", "type": "string"}}, "type": "object"}, "CertificateRawData": {"description": "An SSL certificate obtained from a certificate authority.", "id": "CertificateRawData", "properties": {"privateKey": {"description": "Unencrypted PEM encoded RSA private key. This field is set once on certificate creation and then encrypted. The key size must be 2048 bits or fewer. Must include the header and footer. Example: -----BEGIN RSA PRIVATE KEY----- -----END RSA PRIVATE KEY----- @InputOnly", "type": "string"}, "publicCertificate": {"description": "PEM encoded x.509 public key certificate. This field is set once on certificate creation. Must include the header and footer. Example: -----BEGIN CERTIFICATE----- -----END CERTIFICATE----- ", "type": "string"}}, "type": "object"}, "ContainerState": {"description": "ContainerState contains the externally-visible container state that is used to communicate the state and reasoning for that state to the CLH. This data is not persisted by CCFE, but is instead derived from CCFE's internal representation of the container state.", "id": "ContainerState", "properties": {"currentReasons": {"$ref": "Reasons"}, "previousReasons": {"$ref": "Reasons", "description": "The previous and current reasons for a container state will be sent for a container event. CLHs that need to know the signal that caused the container event to trigger (edges) as opposed to just knowing the state can act upon differences in the previous and current reasons.Reasons will be provided for every system: service management, data governance, abuse, and billing.If this is a CCFE-triggered event used for reconciliation then the current reasons will be set to their *_CONTROL_PLANE_SYNC state. The previous reasons will contain the last known set of non-unknown non-control_plane_sync reasons for the state."}, "state": {"description": "The current state of the container. This state is the culmination of all of the opinions from external systems that CCFE knows about of the container.", "enum": ["UNKNOWN_STATE", "ON", "OFF", "DELETED"], "enumDescriptions": ["A container should never be in an unknown state. Receipt of a container with this state is an error.", "CCFE considers the container to be serving or transitioning into serving.", "CCFE considers the container to be in an OFF state. This could occur due to various factors. The state could be triggered by Google-internal audits (ex. abuse suspension, billing closed) or cleanups trigged by compliance systems (ex. data governance hide). User-initiated events such as service management deactivation trigger a container to an OFF state.CLHs might choose to do nothing in this case or to turn off costly resources. CLHs need to consider the customer experience if an ON/OFF/ON sequence of state transitions occurs vs. the cost of deleting resources, keeping metadata about resources, or even keeping resources live for a period of time.CCFE will not send any new customer requests to the CLH when the container is in an OFF state. However, CCFE will allow all previous customer requests relayed to CLH to complete.", "This state indicates that the container has been (or is being) completely removed. This is often due to a data governance purge request and therefore resources should be deleted when this state is reached."], "type": "string"}}, "type": "object"}, "CreateVersionMetadataV1": {"description": "Metadata for the given google.longrunning.Operation during a google.appengine.v1.CreateVersionRequest.", "id": "CreateVersionMetadataV1", "properties": {"cloudBuildId": {"description": "The Cloud Build ID if one was created as part of the version create. @OutputOnly", "type": "string"}}, "type": "object"}, "CreateVersionMetadataV1Alpha": {"description": "Metadata for the given google.longrunning.Operation during a google.appengine.v1alpha.CreateVersionRequest.", "id": "CreateVersionMetadataV1Alpha", "properties": {"cloudBuildId": {"description": "The Cloud Build ID if one was created as part of the version create. @OutputOnly", "type": "string"}}, "type": "object"}, "CreateVersionMetadataV1Beta": {"description": "Metadata for the given google.longrunning.Operation during a google.appengine.v1beta.CreateVersionRequest.", "id": "CreateVersionMetadataV1Beta", "properties": {"cloudBuildId": {"description": "The Cloud Build ID if one was created as part of the version create. @OutputOnly", "type": "string"}}, "type": "object"}, "DomainMapping": {"description": "A domain serving an App Engine application.", "id": "DomainMapping", "properties": {"id": {"description": "Relative name of the domain serving the application. Example: example.com.", "type": "string"}, "name": {"description": "Full path to the DomainMapping resource in the API. Example: apps/myapp/domainMapping/example.com.@OutputOnly", "type": "string"}, "resourceRecords": {"description": "The resource records required to configure this domain mapping. These records must be added to the domain's DNS configuration in order to serve the application via this domain mapping.@OutputOnly", "items": {"$ref": "ResourceRecord"}, "type": "array"}, "sslSettings": {"$ref": "SslSettings", "description": "SSL configuration for this domain. If unconfigured, this domain will not serve with SSL."}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); } ", "id": "Empty", "properties": {}, "type": "object"}, "GceTag": {"description": "For use only by GCE. GceTag is a wrapper around the GCE administrative tag with parent info.", "id": "GceTag", "properties": {"parent": {"description": "The parents(s) of the tag. Eg. projects/123, folders/456 It usually contains only one parent. But, in some corner cases, it can contain multiple parents. Currently, organizations are not supported.", "items": {"type": "string"}, "type": "array"}, "tag": {"description": "The administrative_tag name.", "type": "string"}}, "type": "object"}, "GoogleAppengineV1betaLocationMetadata": {"description": "Metadata for the given google.cloud.location.Location.", "id": "GoogleAppengineV1betaLocationMetadata", "properties": {"flexibleEnvironmentAvailable": {"description": "App Engine flexible environment is available in the given location.@OutputOnly", "type": "boolean"}, "searchApiAvailable": {"description": "Output only. Search API (https://cloud.google.com/appengine/docs/standard/python/search) is available in the given location.", "readOnly": true, "type": "boolean"}, "standardEnvironmentAvailable": {"description": "App Engine standard environment is available in the given location.@OutputOnly", "type": "boolean"}}, "type": "object"}, "ListAuthorizedCertificatesResponse": {"description": "Response message for AuthorizedCertificates.ListAuthorizedCertificates.", "id": "ListAuthorizedCertificatesResponse", "properties": {"certificates": {"description": "The SSL certificates the user is authorized to administer.", "items": {"$ref": "AuthorizedCertificate"}, "type": "array"}, "nextPageToken": {"description": "Continuation token for fetching the next page of results.", "type": "string"}}, "type": "object"}, "ListAuthorizedDomainsResponse": {"description": "Response message for AuthorizedDomains.ListAuthorizedDomains.", "id": "ListAuthorizedDomainsResponse", "properties": {"domains": {"description": "The authorized domains belonging to the user.", "items": {"$ref": "AuthorizedDomain"}, "type": "array"}, "nextPageToken": {"description": "Continuation token for fetching the next page of results.", "type": "string"}}, "type": "object"}, "ListDomainMappingsResponse": {"description": "Response message for DomainMappings.ListDomainMappings.", "id": "ListDomainMappingsResponse", "properties": {"domainMappings": {"description": "The domain mappings for the application.", "items": {"$ref": "DomainMapping"}, "type": "array"}, "nextPageToken": {"description": "Continuation token for fetching the next page of results.", "type": "string"}}, "type": "object"}, "ListLocationsResponse": {"description": "The response message for Locations.ListLocations.", "id": "ListLocationsResponse", "properties": {"locations": {"description": "A list of locations that matches the specified filter in the request.", "items": {"$ref": "Location"}, "type": "array"}, "nextPageToken": {"description": "The standard List next-page token.", "type": "string"}}, "type": "object"}, "ListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "Location": {"description": "A resource that represents a Google Cloud location.", "id": "Location", "properties": {"displayName": {"description": "The friendly name for this location, typically a nearby city name. For example, \"Tokyo\".", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Cross-service attributes for the location. For example {\"cloud.googleapis.com/region\": \"us-east1\"} ", "type": "object"}, "locationId": {"description": "The canonical id for this location. For example: \"us-east1\".", "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata. For example the available capacity at the given location.", "type": "object"}, "name": {"description": "Resource name for the location, which may vary between implementations. For example: \"projects/example-project/locations/us-east1\"", "type": "string"}}, "type": "object"}, "LocationMetadata": {"description": "Metadata for the given google.cloud.location.Location.", "id": "LocationMetadata", "properties": {"flexibleEnvironmentAvailable": {"description": "App Engine flexible environment is available in the given location.@OutputOnly", "type": "boolean"}, "searchApiAvailable": {"description": "Output only. Search API (https://cloud.google.com/appengine/docs/standard/python/search) is available in the given location.", "readOnly": true, "type": "boolean"}, "standardEnvironmentAvailable": {"description": "App Engine standard environment is available in the given location.@OutputOnly", "type": "boolean"}}, "type": "object"}, "ManagedCertificate": {"description": "A certificate managed by App Engine.", "id": "ManagedCertificate", "properties": {"lastRenewalTime": {"description": "Time at which the certificate was last renewed. The renewal process is fully managed. Certificate renewal will automatically occur before the certificate expires. Renewal errors can be tracked via ManagementStatus.@OutputOnly", "format": "google-datetime", "type": "string"}, "status": {"description": "Status of certificate management. Refers to the most recent certificate acquisition or renewal attempt.@OutputOnly", "enum": ["UNSPECIFIED_STATUS", "OK", "PENDING", "FAILED_RETRYING_INTERNAL", "FAILED_RETRYING_NOT_VISIBLE", "FAILED_PERMANENTLY_NOT_VISIBLE", "FAILED_RETRYING_CAA_FORBIDDEN", "FAILED_RETRYING_CAA_CHECKING"], "enumDescriptions": ["", "Certificate was successfully obtained and inserted into the serving system.", "Certificate is under active attempts to acquire or renew.", "Most recent renewal failed due to a system failure and will be retried. System failure is likely transient, and subsequent renewal attempts may succeed. The last successfully provisioned certificate may still be serving.", "Most recent renewal failed due to an invalid DNS setup and will be retried. Renewal attempts will continue to fail until the certificate domain's DNS configuration is fixed. The last successfully provisioned certificate may still be serving.", "All renewal attempts have been exhausted. Most recent renewal failed due to an invalid DNS setup and will not be retried. The last successfully provisioned certificate may still be serving.", "Most recent renewal failed due to an explicit CAA record that does not include one of the in-use CAs (Google CA and Let's Encrypt). Renewals will continue to fail until the CAA is reconfigured. The last successfully provisioned certificate may still be serving.", "Most recent renewal failed due to a CAA retrieval failure. This means that the domain's DNS provider does not properly handle CAA records, failing requests for CAA records when no CAA records are defined. Renewals will continue to fail until the DNS provider is changed or a CAA record is added for the given domain. The last successfully provisioned certificate may still be serving."], "type": "string"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is false, it means the operation is still in progress. If true, the operation is completed, and either error or response is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the name should be a resource name ending with operations/{unique_id}.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as Delete, the response is google.protobuf.Empty. If the original method is standard Get/Create/Update, the response should be the resource. For other methods, the response should have the type XxxResponse, where Xxx is the original method name. For example, if the original method name is TakeSnapshot(), the inferred response type is TakeSnapshotResponse.", "type": "object"}}, "type": "object"}, "OperationMetadataV1": {"description": "Metadata for the given google.longrunning.Operation.", "id": "OperationMetadataV1", "properties": {"createVersionMetadata": {"$ref": "CreateVersionMetadataV1"}, "endTime": {"description": "Time that this operation completed.@OutputOnly", "format": "google-datetime", "type": "string"}, "ephemeralMessage": {"description": "Ephemeral message that may change every time the operation is polled. @OutputOnly", "type": "string"}, "insertTime": {"description": "Time that this operation was created.@OutputOnly", "format": "google-datetime", "type": "string"}, "method": {"description": "API method that initiated this operation. Example: google.appengine.v1.Versions.CreateVersion.@OutputOnly", "type": "string"}, "target": {"description": "Name of the resource that this operation is acting on. Example: apps/myapp/services/default.@OutputOnly", "type": "string"}, "user": {"description": "User who requested this operation.@OutputOnly", "type": "string"}, "warning": {"description": "Durable messages that persist on every operation poll. @OutputOnly", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "OperationMetadataV1Alpha": {"description": "Metadata for the given google.longrunning.Operation.", "id": "OperationMetadataV1Alpha", "properties": {"createVersionMetadata": {"$ref": "CreateVersionMetadataV1Alpha"}, "endTime": {"description": "Time that this operation completed.@OutputOnly", "format": "google-datetime", "type": "string"}, "ephemeralMessage": {"description": "Ephemeral message that may change every time the operation is polled. @OutputOnly", "type": "string"}, "insertTime": {"description": "Time that this operation was created.@OutputOnly", "format": "google-datetime", "type": "string"}, "method": {"description": "API method that initiated this operation. Example: google.appengine.v1alpha.Versions.CreateVersion.@OutputOnly", "type": "string"}, "target": {"description": "Name of the resource that this operation is acting on. Example: apps/myapp/services/default.@OutputOnly", "type": "string"}, "user": {"description": "User who requested this operation.@OutputOnly", "type": "string"}, "warning": {"description": "Durable messages that persist on every operation poll. @OutputOnly", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "OperationMetadataV1Beta": {"description": "Metadata for the given google.longrunning.Operation.", "id": "OperationMetadataV1Beta", "properties": {"createVersionMetadata": {"$ref": "CreateVersionMetadataV1Beta"}, "endTime": {"description": "Time that this operation completed.@OutputOnly", "format": "google-datetime", "type": "string"}, "ephemeralMessage": {"description": "Ephemeral message that may change every time the operation is polled. @OutputOnly", "type": "string"}, "insertTime": {"description": "Time that this operation was created.@OutputOnly", "format": "google-datetime", "type": "string"}, "method": {"description": "API method that initiated this operation. Example: google.appengine.v1beta.Versions.CreateVersion.@OutputOnly", "type": "string"}, "target": {"description": "Name of the resource that this operation is acting on. Example: apps/myapp/services/default.@OutputOnly", "type": "string"}, "user": {"description": "User who requested this operation.@OutputOnly", "type": "string"}, "warning": {"description": "Durable messages that persist on every operation poll. @OutputOnly", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ProjectEvent": {"description": "The request sent to CLHs during project events.", "id": "ProjectEvent", "properties": {"eventId": {"description": "The unique ID for this project event. CLHs can use this value to dedup repeated calls. required", "type": "string"}, "phase": {"description": "Phase indicates when in the container event propagation this event is being communicated. Events are sent before and after the per-resource events are propagated. required", "enum": ["CONTAINER_EVENT_PHASE_UNSPECIFIED", "BEFORE_RESOURCE_HANDLING", "AFTER_RESOURCE_HANDLING"], "enumDescriptions": ["", "", ""], "type": "string"}, "projectMetadata": {"$ref": "ProjectsMetadata", "description": "The projects metadata for this project. required"}, "state": {"$ref": "ContainerState", "description": "The state of the organization that led to this event."}}, "type": "object"}, "ProjectsMetadata": {"description": "ProjectsMetadata is the metadata CCFE stores about the all the relevant projects (tenant, consumer, producer).", "id": "ProjectsMetadata", "properties": {"consumerProjectId": {"description": "The consumer project id.", "type": "string"}, "consumerProjectNumber": {"description": "The consumer project number.", "format": "int64", "type": "string"}, "consumerProjectState": {"description": "The CCFE state of the consumer project. It is the same state that is communicated to the CLH during project events. Notice that this field is not set in the DB, it is only set in this proto when communicated to CLH in the side channel.", "enum": ["UNKNOWN_STATE", "ON", "OFF", "DELETED"], "enumDescriptions": ["A container should never be in an unknown state. Receipt of a container with this state is an error.", "CCFE considers the container to be serving or transitioning into serving.", "CCFE considers the container to be in an OFF state. This could occur due to various factors. The state could be triggered by Google-internal audits (ex. abuse suspension, billing closed) or cleanups trigged by compliance systems (ex. data governance hide). User-initiated events such as service management deactivation trigger a container to an OFF state.CLHs might choose to do nothing in this case or to turn off costly resources. CLHs need to consider the customer experience if an ON/OFF/ON sequence of state transitions occurs vs. the cost of deleting resources, keeping metadata about resources, or even keeping resources live for a period of time.CCFE will not send any new customer requests to the CLH when the container is in an OFF state. However, CCFE will allow all previous customer requests relayed to CLH to complete.", "This state indicates that the container has been (or is being) completely removed. This is often due to a data governance purge request and therefore resources should be deleted when this state is reached."], "type": "string"}, "gceTag": {"description": "The GCE tags associated with the consumer project and those inherited due to their ancestry, if any. Not supported by CCFE.", "items": {"$ref": "GceTag"}, "type": "array"}, "p4ServiceAccount": {"description": "The service account authorized to operate on the consumer project. Note: CCFE only propagates P4SA with default tag to CLH.", "type": "string"}, "producerProjectId": {"description": "The producer project id.", "type": "string"}, "producerProjectNumber": {"description": "The producer project number.", "format": "int64", "type": "string"}, "tenantProjectId": {"description": "The tenant project id.", "type": "string"}, "tenantProjectNumber": {"description": "The tenant project number.", "format": "int64", "type": "string"}}, "type": "object"}, "Reasons": {"description": "Containers transition between and within states based on reasons sent from various systems. CCFE will provide the CLH with reasons for the current state per system.The current systems that CCFE supports are: Service Management (Inception) Data Governance (Wipeout) Abuse (Ares) Billing (Internal Cloud Billing API) Service Activation (Service Controller)", "id": "Reasons", "properties": {"abuse": {"enum": ["ABUSE_UNKNOWN_REASON", "ABUSE_CONTROL_PLANE_SYNC", "SUSPEND", "REINSTATE"], "enumDescriptions": ["An unknown reason indicates that the abuse system has not sent a signal for this container.", "Due to various reasons CCFE might proactively restate a container state to a CLH to ensure that the CLH and CCFE are both aware of the container state. This reason can be tied to any of the states.", "If a container is deemed abusive we receive a suspend signal. Suspend is a reason to put the container into an INTERNAL_OFF state.", "Containers that were once considered abusive can later be deemed non-abusive. When this happens we must reinstate the container. Reinstate is a reason to put the container into an ON state."], "type": "string"}, "billing": {"enum": ["BILLING_UNKNOWN_REASON", "BILLING_CONTROL_PLANE_SYNC", "PROBATION", "CLOSE", "OPEN"], "enumDescriptions": ["An unknown reason indicates that the billing system has not sent a signal for this container.", "Due to various reasons CCFE might proactively restate a container state to a CLH to ensure that the CLH and CCFE are both aware of the container state. This reason can be tied to any of the states.", "Minor infractions cause a probation signal to be sent. Probation is a reason to put the container into a ON state even though it is a negative signal. CCFE will block mutations for this container while it is on billing probation, but the CLH is expected to serve non-mutation requests.", "When a billing account is closed, it is a stronger signal about non-payment. Close is a reason to put the container into an INTERNAL_OFF state.", "Consumers can re-open billing accounts and update accounts to pull them out of probation. When this happens, we get a signal that the account is open. Open is a reason to put the container into an ON state."], "type": "string"}, "dataGovernance": {"enum": ["DATA_GOVERNANCE_UNKNOWN_REASON", "DATA_GOVERNANCE_CONTROL_PLANE_SYNC", "HIDE", "UNHIDE", "PURGE"], "enumDescriptions": ["An unknown reason indicates that data governance has not sent a signal for this container.", "Due to various reasons CCFE might proactively restate a container state to a CLH to ensure that the CLH and CCFE are both aware of the container state. This reason can be tied to any of the states.", "When a container is deleted we retain some data for a period of time to allow the consumer to change their mind. Data governance sends a signal to hide the data when this occurs. Hide is a reason to put the container in an INTERNAL_OFF state.", "The decision to un-delete a container can be made. When this happens data governance tells us to unhide any hidden data. Unhide is a reason to put the container in an ON state.", "After a period of time data must be completely removed from our systems. When data governance sends a purge signal we need to remove data. Purge is a reason to put the container in a DELETED state. Purge is the only event that triggers a delete mutation. All other events have update semantics."], "type": "string"}, "serviceActivation": {"description": "Consumer Container denotes if the service is active within a project or not. This information could be used to clean up resources in case service in DISABLED_FULL i.e. Service is inactive > 30 days.", "enum": ["SERVICE_ACTIVATION_STATUS_UNSPECIFIED", "SERVICE_ACTIVATION_ENABLED", "SERVICE_ACTIVATION_DISABLED", "SERVICE_ACTIVATION_DISABLED_FULL", "SERVICE_ACTIVATION_UNKNOWN_REASON"], "enumDescriptions": ["Default Unspecified status", "Service is active in the project.", "Service is disabled in the project recently i.e., within last 24 hours.", "Service has been disabled for configured grace_period (default 30 days).", "Happens when PSM cannot determine the status of service in a project Could happen due to variety of reasons like PERMISSION_DENIED or Project got deleted etc."], "type": "string"}, "serviceManagement": {"enum": ["SERVICE_MANAGEMENT_UNKNOWN_REASON", "SERVICE_MANAGEMENT_CONTROL_PLANE_SYNC", "ACTIVATION", "PREPARE_DEACTIVATION", "ABORT_DEACTIVATION", "COMMIT_DEACTIVATION"], "enumDeprecated": [false, false, true, true, true, true], "enumDescriptions": ["An unknown reason indicates that we have not received a signal from service management about this container. Since containers are created by request of service management, this reason should never be set.", "Due to various reasons CCFE might proactively restate a container state to a CLH to ensure that the CLH and CCFE are both aware of the container state. This reason can be tied to any of the states.", "When a customer activates an API CCFE notifies the CLH and sets the container to the ON state.", "When a customer deactivates and API service management starts a two-step process to perform the deactivation. The first step is to prepare. Prepare is a reason to put the container in a EXTERNAL_OFF state.", "If the deactivation is cancelled, service managed needs to abort the deactivation. Abort is a reason to put the container in an ON state.", "If the deactivation is followed through with, service management needs to finish deactivation. Commit is a reason to put the container in a DELETED state."], "type": "string"}}, "type": "object"}, "ResourceEvent": {"description": "The request that is passed to CLH during per-resource events. The request will be sent with update semantics in all cases except for data governance purge events. These events will be sent with delete semantics and the CLH is expected to delete the resource receiving this event.", "id": "ResourceEvent", "properties": {"eventId": {"description": "The unique ID for this per-resource event. CLHs can use this value to dedup repeated calls. required", "type": "string"}, "name": {"description": "The name of the resource for which this event is. required", "type": "string"}, "state": {"$ref": "ContainerState", "description": "The state of the project that led to this event."}}, "type": "object"}, "ResourceRecord": {"description": "A DNS resource record.", "id": "ResourceRecord", "properties": {"name": {"description": "Relative name of the object affected by this record. Only applicable for CNAME records. Example: 'www'.", "type": "string"}, "rrdata": {"description": "Data for this record. Values vary by record type, as defined in RFC 1035 (section 5) and RFC 1034 (section 3.6.1).", "type": "string"}, "type": {"description": "Resource record type. Example: AAAA.", "enum": ["A", "AAAA", "CNAME"], "enumDescriptions": ["An A resource record. Data is an IPv4 address.", "An AAAA resource record. Data is an IPv6 address.", "A CNAME resource record. Data is a domain name to be aliased."], "type": "string"}}, "type": "object"}, "SslSettings": {"description": "SSL configuration for a DomainMapping resource.", "id": "SslSettings", "properties": {"certificateId": {"description": "ID of the AuthorizedCertificate resource configuring SSL for the application. Clearing this field will remove SSL support.By default, a managed certificate is automatically created for every domain mapping. To omit SSL support or to configure SSL manually, specify no_managed_certificate on a CREATE or UPDATE request. You must be authorized to administer the AuthorizedCertificate resource to manually map it to a DomainMapping resource. Example: 12345.", "type": "string"}, "isManagedCertificate": {"description": "Whether the mapped certificate is an App Engine managed certificate. Managed certificates are created by default with a domain mapping. To opt out, specify no_managed_certificate on a CREATE or UPDATE request.@OutputOnly", "type": "boolean"}}, "type": "object"}, "Status": {"description": "The Status type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by gRPC (https://github.com/grpc). Each Status message contains three pieces of data: error code, error message, and error details.You can find out more about this error model and how to work with it in the API Design Guide (https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "App Engine Admin API", "version": "v1alpha"}