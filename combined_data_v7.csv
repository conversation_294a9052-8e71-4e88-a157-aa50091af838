source_node,source_type,destination_node,destination_type,relationship,file_path
oneinsights,folder,servicebolt,folder,contains,
servicebolt,folder,api,folder,contains,
servicebolt,folder,service,folder,contains,
oneinsights,folder,unifiedbolt,folder,contains,
unifiedbolt,folder,core,folder,contains,
core,folder,model,folder,contains,
core,folder,repository,folder,contains,
unifiedbolt,folder,githubaction,folder,contains,
api,folder,buildtoolcontroller,file,contains,ServiceBolt\api\BuildToolController.java
service,folder,buildtoolservice,file,contains,ServiceBolt\service\BuildToolService.java
service,folder,buildtoolserviceimplemantation,file,contains,ServiceBolt\service\BuildToolServiceImplemantation.java
model,folder,basemodel,file,contains,UnifiedBolt\core\model\BaseModel.java
model,folder,buildfailurepatternforprojectinjenkinsmodel,file,contains,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java
model,folder,buildfailurepatternmetrics,file,contains,UnifiedBolt\core\model\BuildFailurePatternMetrics.java
model,folder,buildfileinfo,file,contains,UnifiedBolt\core\model\BuildFileInfo.java
model,folder,buildinfo,file,contains,UnifiedBolt\core\model\BuildInfo.java
model,folder,buildsteps,file,contains,UnifiedBolt\core\model\BuildSteps.java
model,folder,buildtool,file,contains,UnifiedBolt\core\model\BuildTool.java
model,folder,buildtoolmetric,file,contains,UnifiedBolt\core\model\BuildToolMetric.java
model,folder,configurationsetting,file,contains,UnifiedBolt\core\model\ConfigurationSetting.java
model,folder,configurationtoolinfometric,file,contains,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
repository,folder,buildfailurepatternforprojectrepo,file,contains,UnifiedBolt\core\repository\BuildFailurePatternForProjectRepo.java
repository,folder,buildtoolrep,file,contains,UnifiedBolt\core\repository\BuildToolRep.java
repository,folder,configurationsettingrep,file,contains,UnifiedBolt\core\repository\ConfigurationSettingRep.java
githubaction,folder,githubaction,file,contains,UnifiedBolt\githubaction\GithubAction.java
githubaction,folder,githubactionapplication,file,contains,UnifiedBolt\githubaction\GithubActionApplication.java
githubaction,folder,githubactionimplementation,file,contains,UnifiedBolt\githubaction\GithubActionImplementation.java
buildtoolcontroller,file,buildtoolcontroller,class,declares,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
buildtoolcontroller,class,buildtoolservice,variable,has_field,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
buildtoolcontroller,class,getonejob,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
buildtoolcontroller,class,getbuilddetailshome,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
buildtoolcontroller,class,builddata,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
buildtoolcontroller,class,joblist,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
joblist,method,buildtype,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
buildtoolcontroller,class,savebuildfailurepattern,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
savebuildfailurepattern,method,obj,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
buildtoolcontroller,class,fetchdata,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
buildtoolcontroller,class,fetchfailurepatterndata,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
fetchfailurepatterndata,method,obj,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
buildtoolcontroller,class,fetchfailurepatterndatacopy,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
fetchfailurepatterndatacopy,method,obj,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
buildtoolcontroller,class,gitlabbuilddata,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
buildtoolcontroller,class,getbuildtype,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
getbuildtype,method,buildtype,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
getbuildtype,method,context,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
getbuildtype,method,configurationrepo,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
getbuildtype,method,configurationcolection,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
getbuildtype,method,metric,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
getbuildtype,method,iter,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
getbuildtype,method,configuration1,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
getbuildtype,method,metric1,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
buildtoolcontroller,class,/buildjoblist,endpoint,declares,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
buildtoolcontroller,class,/builddetailshome,endpoint,declares,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
buildtoolcontroller,class,/builddetails,endpoint,declares,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
buildtoolcontroller,class,/jobslist,endpoint,declares,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
buildtoolcontroller,class,/jenkinsbuildfailure,endpoint,declares,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
buildtoolcontroller,class,/jenkinsbuildfailuredata,endpoint,declares,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
buildtoolcontroller,class,jenkinsbuildfailurepatternfetchdata,endpoint,declares,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
buildtoolcontroller,class,jenkinsbuildfailurepatternfetchdataconfig,endpoint,declares,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
buildtoolcontroller,class,getbuildvaluestream,endpoint,declares,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
buildtoolcontroller,class,getgitlabbuildvaluestream,endpoint,declares,C:\Shaik\sample\OneInsights\ServiceBolt\api\BuildToolController.java
buildtoolservice,file,buildtoolservice,interface,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolService.java
buildtoolservice,interface,searchfortest,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolService.java
buildtoolservice,interface,search,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolService.java
buildtoolservice,interface,searchjoblist,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolService.java
buildtoolservice,interface,buildfailurepattern,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolService.java
buildtoolservice,interface,fetchfailurepatterndata,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolService.java
buildtoolservice,interface,fetchbuilddata,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolService.java
buildtoolservice,interface,getonebyprojectname,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolService.java
buildtoolservice,interface,getbuilddetailshome,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolService.java
buildtoolservice,interface,getvaluestream,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolService.java
buildtoolservice,interface,getgitlabvaluestream,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolService.java
buildtoolserviceimplemantation,file,buildtoolserviceimplemantation,class,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
buildtoolserviceimplemantation,class,buildtoolrepository,variable,has_field,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
buildtoolserviceimplemantation,class,buildfailurepatternforprojectrepo,variable,has_field,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
buildtoolserviceimplemantation,class,log,variable,has_field,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
buildtoolserviceimplemantation,class,configsettingrepo,variable,has_field,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
buildtoolserviceimplemantation,class,metric,variable,has_field,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
buildtoolserviceimplemantation,class,nodataconst,variable,has_field,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
buildtoolserviceimplemantation,class,buildconst,variable,has_field,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
buildtoolserviceimplemantation,class,search,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
search,method,lastupdate,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
search,method,result,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
buildtoolserviceimplemantation,class,searchjoblist,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
searchjoblist,method,lastupdate,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
searchjoblist,method,result,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
buildtoolserviceimplemantation,class,searchfortest,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
searchfortest,method,result,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
search,method,flagnew,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
search,method,flag,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
search,method,toolname,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
search,method,config,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
search,method,metric,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
search,method,m,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
search,method,mongoaggr,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
buildtoolserviceimplemantation,class,buildfailurepattern,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
buildfailurepattern,method,patternforproject,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
buildfailurepattern,method,buildfailurepatternforprojectlist,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
buildfailurepattern,method,buildfailurepatternforproject,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
buildtoolserviceimplemantation,class,fetchbuilddata,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
fetchbuilddata,method,lastupdated,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
fetchbuilddata,method,result,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
buildtoolserviceimplemantation,class,fetchfailurepatterndata,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
fetchfailurepatterndata,method,lastupdated,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
fetchfailurepatterndata,method,response,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
buildtoolserviceimplemantation,class,getonebyprojectname,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
getonebyprojectname,method,result,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
buildtoolserviceimplemantation,class,getbuilddetailshome,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
getbuilddetailshome,method,projhomecalc,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
getbuilddetailshome,method,result,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
buildtoolserviceimplemantation,class,getvaluestream,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
getvaluestream,method,config,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
getvaluestream,method,metric,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
getvaluestream,method,m,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
getvaluestream,method,buildcalculations,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
buildtoolserviceimplemantation,class,getgitlabvaluestream,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
getgitlabvaluestream,method,config,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
getgitlabvaluestream,method,metric,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
getgitlabvaluestream,method,m,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
getgitlabvaluestream,method,buildcalculations,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
buildtoolserviceimplemantation,class,findbyname,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
buildtoolserviceimplemantation,class,findbynameandbuildtypeignorecase,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
buildtoolserviceimplemantation,class,findbynameandtimestampbetween,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
buildtoolserviceimplemantation,class,findbybuildtypeandname,method,declares,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
search,method,buildtoolrepository,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
searchjoblist,method,buildtoolrepository,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
searchfortest,method,buildtoolrepository,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
search,method,configurationtoolinfometric,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
fetchfailurepatterndata,method,result,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
buildtoolserviceimplemantation,class,buildfailurepatternforprojectrepo,table,maps_to,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
getvaluestream,method,proname,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
getgitlabvaluestream,method,proname,variable,uses,C:\Shaik\sample\OneInsights\ServiceBolt\service\BuildToolServiceImplemantation.java
basemodel,file,basemodel,class,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BaseModel.java
basemodel,class,id,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BaseModel.java
basemodel,class,getid,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BaseModel.java
basemodel,class,setid,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BaseModel.java
getid,method,id,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BaseModel.java
setid,method,id,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BaseModel.java
buildfailurepatternforprojectinjenkinsmodel,file,buildfailurepatternforprojectinjenkinsmodel,class,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java
buildfailurepatternforprojectinjenkinsmodel,class,projectname,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java
buildfailurepatternforprojectinjenkinsmodel,class,username,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java
buildfailurepatternforprojectinjenkinsmodel,class,patternmetrics,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java
buildfailurepatternforprojectinjenkinsmodel,class,timestampofcreation,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java
buildfailurepatternforprojectinjenkinsmodel,class,getprojectname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java
getprojectname,method,projectname,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java
buildfailurepatternforprojectinjenkinsmodel,class,setprojectname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java
setprojectname,method,projectname,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java
buildfailurepatternforprojectinjenkinsmodel,class,getpatternmetrics,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java
getpatternmetrics,method,patternmetrics,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java
buildfailurepatternforprojectinjenkinsmodel,class,setpatternmetrics,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java
setpatternmetrics,method,patternmetrics,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java
buildfailurepatternforprojectinjenkinsmodel,class,gettimestampofcreation,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java
gettimestampofcreation,method,timestampofcreation,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java
buildfailurepatternforprojectinjenkinsmodel,class,settimestampofcreation,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java
settimestampofcreation,method,timestampofcreation,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java
buildfailurepatternforprojectinjenkinsmodel,class,addpatternmetric,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java
addpatternmetric,method,patternmetrics,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java
buildfailurepatternforprojectinjenkinsmodel,class,getusername,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java
getusername,method,username,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java
buildfailurepatternforprojectinjenkinsmodel,class,setusername,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java
setusername,method,username,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java
buildfailurepatternforprojectinjenkinsmodel,class,buildfailurepatternforproject,table,maps_to,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java
buildfailurepatternforprojectinjenkinsmodel,class,basemodel,class,extends,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java
buildfailurepatternmetrics,file,buildfailurepatternmetrics,class,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java
buildfailurepatternmetrics,class,patterndefined,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java
buildfailurepatternmetrics,class,patterndisplayed,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java
buildfailurepatternmetrics,class,patterncount,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java
buildfailurepatternmetrics,class,reponame,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java
buildfailurepatternmetrics,class,getpatterndefined,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java
getpatterndefined,method,patterndefined,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java
buildfailurepatternmetrics,class,setpatterndefined,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java
setpatterndefined,method,patterndefined,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java
buildfailurepatternmetrics,class,getpatterndisplayed,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java
getpatterndisplayed,method,patterndisplayed,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java
buildfailurepatternmetrics,class,setpatterndisplayed,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java
setpatterndisplayed,method,patterndisplayed,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java
buildfailurepatternmetrics,class,getpatterncount,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java
getpatterncount,method,patterncount,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java
buildfailurepatternmetrics,class,setpatterncount,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java
setpatterncount,method,patterncount,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java
buildfailurepatternmetrics,class,getreponame,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java
getreponame,method,reponame,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java
buildfailurepatternmetrics,class,setreponame,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java
setreponame,method,reponame,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFailurePatternMetrics.java
buildfileinfo,file,buildfileinfo,class,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFileInfo.java
buildfileinfo,class,filenames,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFileInfo.java
buildfileinfo,class,edittype,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFileInfo.java
buildfileinfo,class,getfilenames,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFileInfo.java
buildfileinfo,class,setfilenames,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFileInfo.java
buildfileinfo,class,getedittype,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFileInfo.java
buildfileinfo,class,setedittype,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFileInfo.java
getfilenames,method,filenames,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFileInfo.java
setfilenames,method,filenames,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFileInfo.java
getedittype,method,edittype,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFileInfo.java
setedittype,method,edittype,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildFileInfo.java
buildinfo,file,buildinfo,class,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java
buildinfo,class,message,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java
buildinfo,class,committer,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java
buildinfo,class,buildfileinfolist,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java
buildinfo,class,getmessage,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java
getmessage,method,message,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java
buildinfo,class,setmessage,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java
setmessage,method,message,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java
buildinfo,class,getcommitter,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java
getcommitter,method,committer,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java
buildinfo,class,setcommitter,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java
setcommitter,method,committer,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java
buildinfo,class,getbuildfileinfolist,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java
getbuildfileinfolist,method,buildfileinfolist,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java
buildinfo,class,setbuildfileinfolist,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java
setbuildfileinfolist,method,buildfileinfolist,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildInfo.java
buildsteps,file,buildsteps,class,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java
buildsteps,class,stepname,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java
buildsteps,class,duration,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java
buildsteps,class,result,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java
buildsteps,class,startedtime,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java
buildsteps,class,completedtime,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java
buildsteps,class,getstepname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java
getstepname,method,stepname,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java
buildsteps,class,setstepname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java
setstepname,method,stepname,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java
buildsteps,class,getduration,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java
getduration,method,duration,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java
buildsteps,class,setduration,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java
setduration,method,duration,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java
buildsteps,class,getresult,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java
getresult,method,result,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java
buildsteps,class,setresult,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java
setresult,method,result,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java
buildsteps,class,getstartedtime,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java
getstartedtime,method,startedtime,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java
buildsteps,class,setstartedtime,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java
setstartedtime,method,startedtime,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java
buildsteps,class,getcompletedtime,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java
getcompletedtime,method,completedtime,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java
buildsteps,class,setcompletedtime,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java
setcompletedtime,method,completedtime,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildSteps.java
buildtool,class,build,table,maps_to,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,file,buildtool,class,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,basemodel,class,extends,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,comparable,interface,implements,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,collectoritemid,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,timestamp,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,timestring,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,name,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,jobname,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,url,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,version,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,buildtype,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,buildid,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,buildinfolist,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,metrics,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,stepslist,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,joblist,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,jobcount,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,createdby,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,branchname,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,reponame,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,groupname,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,triggertype,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,definitionid,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,patterndetails,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,gettimestring,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,settimestring,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
gettimestring,method,timestring,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
settimestring,method,timestring,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,getcollectoritemid,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,setcollectoritemid,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
getcollectoritemid,method,collectoritemid,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
setcollectoritemid,method,collectoritemid,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,getstepslist,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,setstepslist,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
getstepslist,method,stepslist,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
setstepslist,method,stepslist,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,getcreatedby,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,setcreatedby,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
getcreatedby,method,createdby,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
setcreatedby,method,createdby,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,getbranchname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,setbranchname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
getbranchname,method,branchname,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
setbranchname,method,branchname,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,getjobcount,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,setjobcount,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
getjobcount,method,jobcount,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
setjobcount,method,jobcount,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,getpatterndetails,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,setpatterndetails,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
getpatterndetails,method,patterndetails,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
setpatterndetails,method,patterndetails,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,compareto,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
compareto,method,buildid,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
compareto,method,o,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,gettriggertype,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,settriggertype,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
gettriggertype,method,triggertype,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
settriggertype,method,triggertype,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,getreponame,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,setreponame,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,getgroupname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,setgroupname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
getgroupname,method,groupname,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
setgroupname,method,groupname,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,getdefinitionid,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtool,class,setdefinitionid,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
getdefinitionid,method,definitionid,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
setdefinitionid,method,definitionid,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildTool.java
buildtoolmetric,file,buildtoolmetric,class,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java
buildtoolmetric,class,name,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java
buildtoolmetric,class,valuebuildtool,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java
buildtoolmetric,class,formattedvaluebuildtool,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java
buildtoolmetric,class,statusmessagebuildtool,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java
buildtoolmetric,class,getname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java
buildtoolmetric,class,setname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java
buildtoolmetric,class,getvalue,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java
buildtoolmetric,class,setvalue,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java
buildtoolmetric,class,getformattedvalue,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java
buildtoolmetric,class,setformattedvalue,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java
buildtoolmetric,class,getstatusmessage,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java
buildtoolmetric,class,setstatusmessage,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java
buildtoolmetric,class,equals,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java
buildtoolmetric,class,hashcode,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java
getname,method,name,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java
setname,method,name,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java
getvalue,method,valuebuildtool,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java
setvalue,method,value,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java
setvalue,method,valuebuildtool,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java
getformattedvalue,method,formattedvaluebuildtool,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java
setformattedvalue,method,formattedvalue,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java
setformattedvalue,method,formattedvaluebuildtool,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java
getstatusmessage,method,statusmessagebuildtool,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java
setstatusmessage,method,statusmessage,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java
setstatusmessage,method,statusmessagebuildtool,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java
equals,method,name,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\BuildToolMetric.java
configurationsetting,file,configurationsetting,class,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
configurationsetting,class,timestamp,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
configurationsetting,class,baseline,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
configurationsetting,class,projectname,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
configurationsetting,class,addflag,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
configurationsetting,class,projecttype,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
configurationsetting,class,manualdata,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
configurationsetting,class,metric,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
configurationsetting,class,ismanualdata,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
ismanualdata,method,manualdata,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
configurationsetting,class,setmanualdata,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
setmanualdata,method,manualdata,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
configurationsetting,class,gettimestamp,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
gettimestamp,method,timestamp,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
configurationsetting,class,settimestamp,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
settimestamp,method,timestamp,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
configurationsetting,class,getprojectname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
configurationsetting,class,setprojectname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
configurationsetting,class,getmetrics,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
getmetrics,method,metric,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
configurationsetting,class,isaddflag,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
isaddflag,method,addflag,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
configurationsetting,class,setaddflag,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
setaddflag,method,addflag,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
configurationsetting,class,isbaseline,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
isbaseline,method,baseline,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
configurationsetting,class,setbaseline,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
setbaseline,method,baseline,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
configurationsetting,class,getprojecttype,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
getprojecttype,method,projecttype,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
configurationsetting,class,setprojecttype,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
setprojecttype,method,projecttype,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
configurationsetting,class,configuration,table,maps_to,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationSetting.java
configurationtoolinfometric,class,selected,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,id,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,toolname,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,url,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,username,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,password,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,tooltype,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,widgetname,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,jobname,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,projectcode,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,domain,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,host,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,port,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,dbtype,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,schema,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,reponame,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,secret,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,getid,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,setid,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,gettoolname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,settoolname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
gettoolname,method,toolname,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
settoolname,method,toolname,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,geturl,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,seturl,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
geturl,method,url,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
seturl,method,url,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,getusername,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,setusername,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,getpassword,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,setpassword,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
getpassword,method,password,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
setpassword,method,password,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,gettooltype,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,settooltype,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
gettooltype,method,tooltype,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
settooltype,method,tooltype,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,getwidgetname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,setwidgetname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
getwidgetname,method,widgetname,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
setwidgetname,method,widgetname,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,getjobname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,setjobname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
getjobname,method,jobname,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
setjobname,method,jobname,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,getprojectcode,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,setprojectcode,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
getprojectcode,method,projectcode,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
setprojectcode,method,projectcode,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,getselected,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,setselected,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
getselected,method,selected,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
setselected,method,selected,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,getdomain,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,setdomain,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
getdomain,method,domain,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
setdomain,method,domain,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,gethost,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,sethost,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
gethost,method,host,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
sethost,method,host,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,getport,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,setport,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
getport,method,port,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
setport,method,port,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,getdbtype,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,setdbtype,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
getdbtype,method,dbtype,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
setdbtype,method,dbtype,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,getschema,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,setschema,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
getschema,method,schema,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
setschema,method,schema,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,getreponame,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,setreponame,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,getsecret,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
configurationtoolinfometric,class,setsecret,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
getsecret,method,secret,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
setsecret,method,secret,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\core\model\ConfigurationToolInfoMetric.java
buildfailurepatternforprojectrepo,file,buildfailurepatternforprojectrepo,interface,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\repository\BuildFailurePatternForProjectRepo.java
buildfailurepatternforprojectrepo,interface,findbyprojectname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\repository\BuildFailurePatternForProjectRepo.java
buildfailurepatternforprojectrepo,interface,findall,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\repository\BuildFailurePatternForProjectRepo.java
buildfailurepatternforprojectinjenkinsmodel,class,buildfailurepatternforprojectinjenkinsmodel,table,maps_to,C:\Shaik\sample\OneInsights\UnifiedBolt\core\repository\BuildFailurePatternForProjectRepo.java
buildtoolrep,file,buildtoolrep,interface,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\repository\BuildToolRep.java
buildtoolrep,interface,findbybuildtype,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\repository\BuildToolRep.java
buildtoolrep,interface,countbybuildtype,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\repository\BuildToolRep.java
buildtoolrep,interface,findbybuildtypeandname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\repository\BuildToolRep.java
buildtoolrep,interface,findbynameandjobname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\repository\BuildToolRep.java
buildtoolrep,interface,getidbybuildtype,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\repository\BuildToolRep.java
buildtoolrep,interface,findbybuildtypeandnameandtimestampbetween,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\repository\BuildToolRep.java
buildtoolrep,interface,findbynameandtimestampbetween,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\repository\BuildToolRep.java
buildtoolrep,interface,findbyname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\repository\BuildToolRep.java
buildtoolrep,interface,findonebyname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\repository\BuildToolRep.java
buildtoolrep,interface,findonebynameorderbybuildiddesc,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\repository\BuildToolRep.java
buildtoolrep,interface,findbybuildtypeandreponameandgroupnameorderbybuildiddesc,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\repository\BuildToolRep.java
buildtoolrep,interface,findbynameandbuildtypeignorecase,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\repository\BuildToolRep.java
buildtoolrep,interface,findbynameandreponameandbranchname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\repository\BuildToolRep.java
buildtoolrep,interface,findbynameanddefinitionid,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\repository\BuildToolRep.java
buildtoolrep,interface,findbybuildtypeandreponameandnameorderbybuildiddesc,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\repository\BuildToolRep.java
buildtoolrep,interface,findbybuildtypeandreponameandnameorderbytimestampdesc,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\repository\BuildToolRep.java
configurationsettingrep,file,configurationsettingrep,interface,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\repository\ConfigurationSettingRep.java
configurationsettingrep,interface,findbyprojectname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\repository\ConfigurationSettingRep.java
configurationsettingrep,interface,findbyprojectnamein,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\repository\ConfigurationSettingRep.java
configurationsettingrep,interface,deletebyprojectname,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\core\repository\ConfigurationSettingRep.java
githubaction,file,githubaction,interface,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubAction.java
githubaction,interface,getbuildtooldata,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubAction.java
getbuildtooldata,method,baseurl,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubAction.java
getbuildtooldata,method,repo,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubAction.java
getbuildtooldata,method,firstrun,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubAction.java
getbuildtooldata,method,branch,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubAction.java
getbuildtooldata,method,projectcode,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubAction.java
getbuildtooldata,method,user,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubAction.java
getbuildtooldata,method,pass,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubAction.java
getbuildtooldata,method,projectname,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubAction.java
getbuildtooldata,method,reponame,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubAction.java
githubactionapplication,file,githubactionapplication,class,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionapplication,class,githubactionmain,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionapplication,class,applicationcontext,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionapplication,class,repo,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionapplication,class,githubactionmetrics,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionapplication,class,result,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionapplication,class,buildtype,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionapplication,class,pagelimit,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionapplication,class,configurationrepo,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionapplication,class,configuration,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionapplication,class,metric,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionapplication,class,metric1,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionmain,method,applicationcontext,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionmain,method,repo,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionmain,method,instanceurl,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionmain,method,branch,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionmain,method,username,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionmain,method,password,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionmain,method,reponame,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionmain,method,firstrun,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionmain,method,apitoken,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionmain,method,configurationrepo,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionmain,method,configuration,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionmain,method,metric,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionmain,method,iter,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionmain,method,githubactionmetrics,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionmain,method,spliturl,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionmain,method,splitreponame,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionmain,method,configuration1,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionmain,method,metric1,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionmain,method,constantvariable,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionmain,method,secret_key,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionmain,method,projectname,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionmain,method,i,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionmain,method,length,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionmain,method,result,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionapplication,class,cleanobject,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
cleanobject,method,repo,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
cleanobject,method,githubactionmetrics,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionapplication,class,parseasarray,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionapplication,class,makerestcall,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
makerestcall,method,requestfactory,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
makerestcall,method,rest,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
makerestcall,method,response,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
makerestcall,method,httpmethod,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
makerestcall,method,get,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
makerestcall,method,httpstatus,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
makerestcall,method,not_found,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
makerestcall,method,unauthorized,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionapplication,class,logger,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionApplication.java
githubactionimplementation,file,githubactionimplementation,class,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,logger,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,segment_api,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,public_github_repo_host,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,public_github_host_name,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,ctx,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,projectname,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,username,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,password,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,time,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,buildrepo,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,jobcollection,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,pipelineurl,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,singlepipelineurl,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,githuburl,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,jobsurl,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,size,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,lastpage,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,lastbuildid,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,newbuildpipelinetimestamp,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,timestamp,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,page,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,per_page,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,totalpages,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,pipelineid,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,build,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,jobslist,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,failurepatternrepo,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,reponame,variable,has_field,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,getbuildtooldata,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,processpipelinedata,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,processfailure,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,gettimeinmiliseconds,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,makerestcall,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,createheaders,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,get,method,declares,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
getbuildtooldata,method,githuburl,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
getbuildtooldata,method,url,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
getbuildtooldata,method,hostname,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
getbuildtooldata,method,protocol,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
getbuildtooldata,method,hosturl,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
getbuildtooldata,method,tool,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
getbuildtooldata,method,timestring,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
getbuildtooldata,method,delta,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
getbuildtooldata,method,response,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
getbuildtooldata,method,runs,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
getbuildtooldata,method,totalruns,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
getbuildtooldata,method,page,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
getbuildtooldata,method,runscollected,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
getbuildtooldata,method,pipelineurl,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
getbuildtooldata,method,valuearray,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
getbuildtooldata,method,obj,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
getbuildtooldata,method,builds,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
getbuildtooldata,method,temp,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
getbuildtooldata,method,buildrepo,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
processpipelinedata,method,jobslist,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
processpipelinedata,method,pipeline_obj,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
processpipelinedata,method,timestamp,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
processfailure,method,ctx,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
processfailure,method,failurepatternrepo,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
processfailure,method,reponame,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
githubactionimplementation,class,buildfailurepatternforprojectrepo,table,maps_to,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
getbuildtooldata,method,port,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
getbuildtooldata,method,count,variable,uses,C:\Shaik\sample\OneInsights\UnifiedBolt\githubaction\GithubActionImplementation.java
