/**
 * 
 */
package com.bolt.dashboard.core.model;

/**
 * <AUTHOR>
 *
 */
public class BuildFailurePatternMetrics {

    private String patternDefined;
    private String patternDisplayed;
    private long patternCount;
    private String repoName;

    /**
     * 
     */
    public BuildFailurePatternMetrics() {

    }

    /**
     * @return the patternDefined
     */
    public String getPatternDefined() {
        return patternDefined;
    }

    /**
     * @param patternDefined
     *            the patternDefined to set
     */
    public void setPatternDefined(String patternDefined) {
        this.patternDefined = patternDefined;
    }

    /**
     * @return the patternDisplayed
     */
    public String getPatternDisplayed() {
        return patternDisplayed;
    }

    /**
     * @param patternDisplayed
     *            the patternDisplayed to set
     */
    public void setPatternDisplayed(String patternDisplayed) {
        this.patternDisplayed = patternDisplayed;
    }

    /**
     * @return the patternCount
     */
    public long getPatternCount() {
        return patternCount;
    }

    /**
     * @param patternCount
     *            the patternCount to set
     */
    public void setPatternCount(long patternCount) {
        this.patternCount = patternCount;
    }

	public String getRepoName() {
		return repoName;
	}

	public void setRepoName(String repoName) {
		this.repoName = repoName;
	}

}
