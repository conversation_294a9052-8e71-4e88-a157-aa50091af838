{"cells": [{"cell_type": "code", "execution_count": 57, "id": "setup", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Setup complete\n"]}], "source": ["import os\n", "import re\n", "import json\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "import pandas as pd\n", "from collections import defaultdict\n", "\n", "from tree_sitter import Language, Parser\n", "import tree_sitter_java as tsjava\n", "\n", "from langchain_community.document_loaders import TextLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language\n", "from langchain_google_genai import ChatGoogleGenerativeAI\n", "from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "from langchain_community.graphs import Neo4jGraph\n", "from langchain_openai import AzureChatOpenAI\n", "\n", "BASE_PATH = Path(r\"C:/Shaik/sample/OneInsights\")\n", "NEO4J_URI = \"bolt://localhost:7687\"\n", "NEO4J_USER = \"neo4j\"\n", "NEO4J_PASSWORD = \"Test@7889\"\n", "NEO4J_DB = \"final-v10\"\n", "\n", "graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)\n", "JAVA_LANGUAGE = Language(tsjava.language())\n", "parser = Parser(JAVA_LANGUAGE)\n", "\n", "llm = AzureChatOpenAI(\n", "    api_key=\"********************************\",\n", "    azure_endpoint=\"https://azureopenaibrsc.openai.azure.com/\",\n", "    azure_deployment=\"gpt-4o\",\n", "    api_version=\"2024-12-01-preview\"\n", ")\n", "\n", "all_relationships = []\n", "class_registry = {}\n", "application_mapping = {}\n", "\n", "print(\"✅ Setup complete\")"]}, {"cell_type": "code", "execution_count": 59, "id": "utilities", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Utilities loaded\n"]}], "source": ["def is_temp_variable(var_name):\n", "    if not var_name or len(var_name) == 0:\n", "        return True\n", "    \n", "    temp_patterns = [\n", "        r'^[ijklmnpqr]$', r'^temp\\w*$', r'^tmp\\w*$', r'^_\\w*$',\n", "        r'^\\w*temp$', r'^\\w*tmp$', r'^counter\\d*$', r'^index\\d*$',\n", "        r'^idx\\d*$', r'^\\$\\w*$', r'^this$', r'^super$'\n", "    ]\n", "    \n", "    for pattern in temp_patterns:\n", "        if re.match(pattern, var_name, re.IGNORECASE):\n", "            return True\n", "    return False\n", "\n", "def format_variable_name(method_name, var_name):\n", "    if not method_name or not var_name:\n", "        return var_name\n", "    return f\"{method_name}.{var_name}\"\n", "\n", "def get_application_from_path(file_path):\n", "    try:\n", "        rel_path = os.path.relpath(file_path, BASE_PATH)\n", "        path_parts = rel_path.split(os.sep)\n", "        if len(path_parts) > 0:\n", "            return path_parts[0]\n", "    except:\n", "        pass\n", "    return \"Unknown\"\n", "\n", "def clean_node_name(name):\n", "    if not name:\n", "        return name\n", "    prefixes = ['method:', 'class:', 'variable:', 'field:']\n", "    for prefix in prefixes:\n", "        if name.lower().startswith(prefix):\n", "            name = name[len(prefix):]\n", "    return re.sub(r'\\.(java|class)$', '', name, flags=re.IGNORECASE).strip()\n", "\n", "def normalize_node_types(df):\n", "    type_mapping = {\n", "        'Class': 'CLASS', 'class': 'CLASS', 'Method': 'METHOD', 'method': 'METHOD',\n", "        'Variable': 'VARIABLE', 'variable': 'VARIABLE', 'File': 'FILE', 'file': 'FILE',\n", "        'Interface': 'INTERFACE', 'interface': 'INTERFACE', 'Folder': 'FOLDERS', \n", "        'folder': 'FOLDERS', 'Folders': 'FOLDERS', 'Project': 'PROJECT', 'project': 'PROJECT'\n", "    }\n", "    df['source_type'] = df['source_type'].replace(type_mapping)\n", "    df['destination_type'] = df['destination_type'].replace(type_mapping)\n", "    return df\n", "\n", "print(\"✅ Utilities loaded\")"]}, {"cell_type": "code", "execution_count": 60, "id": "stage1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Stage 1: 27 relationships\n", "Node types: ['FILE', 'FOLDERS']\n"]}], "source": ["# STAGE 1: SIMPLIFIED FOLDER HIERARCHY\n", "all_relationships = []\n", "\n", "def extract_simplified_hierarchy():\n", "    project_name = BASE_PATH.name\n", "    \n", "    for root, dirs, files in os.walk(BASE_PATH):\n", "        rel_root = os.path.relpath(root, BASE_PATH)\n", "        \n", "        # Skip the root level project connections\n", "        if rel_root == '.':\n", "            # Direct connection: OneInsights -> ServiceBolt, UnifiedBolt (as folders)\n", "            for d in dirs:\n", "                all_relationships.append({\n", "                    'source_node': project_name,\n", "                    'source_type': 'FOLDERS',\n", "                    'destination_node': d,\n", "                    'destination_type': 'FOLDERS',\n", "                    'relationship': 'CONTAINS',\n", "                    'file_path': None,\n", "                    'application': d\n", "                })\n", "        else:\n", "            # Handle nested folder structure\n", "            path_parts = rel_root.split(os.sep)\n", "            current_app = path_parts[0]\n", "            \n", "            # Create folder-to-folder connections for nested paths\n", "            if len(path_parts) > 1:\n", "                for i in range(len(path_parts) - 1):\n", "                    parent = path_parts[i]\n", "                    child = path_parts[i + 1]\n", "                    \n", "                    all_relationships.append({\n", "                        'source_node': parent,\n", "                        'source_type': 'FOLDERS',\n", "                        'destination_node': child,\n", "                        'destination_type': 'FOLDERS',\n", "                        'relationship': 'CONTAINS',\n", "                        'file_path': None,\n", "                        'application': current_app\n", "                    })\n", "        \n", "        # Handle files\n", "        current_folder = os.path.basename(root) if rel_root != '.' else project_name\n", "        current_app = get_application_from_path(root)\n", "        \n", "        for file in files:\n", "            if file.endswith('.java'):\n", "                file_rel_path = os.path.relpath(os.path.join(root, file), BASE_PATH)\n", "                application_mapping[file_rel_path] = current_app\n", "                \n", "                all_relationships.append({\n", "                    'source_node': current_folder,\n", "                    'source_type': 'FOLDERS',\n", "                    'destination_node': file,\n", "                    'destination_type': 'FILE',\n", "                    'relationship': 'CONTAINS',\n", "                    'file_path': file_rel_path,\n", "                    'application': current_app\n", "                })\n", "\n", "def clean_folder_names(df):\n", "    \"\"\"Clean folder names to remove path prefixes\"\"\"\n", "    df_clean = df.copy()\n", "    \n", "    # Clean source_node - remove backslash paths, keep only folder name\n", "    def clean_name(name):\n", "        if pd.isna(name) or name is None:\n", "            return name\n", "        name_str = str(name)\n", "        if '\\\\' in name_str:\n", "            return name_str.split('\\\\')[-1]\n", "        return name_str\n", "    \n", "    df_clean['source_node'] = df_clean['source_node'].apply(clean_name)\n", "    df_clean['destination_node'] = df_clean['destination_node'].apply(clean_name)\n", "    \n", "    # Remove duplicates that might be created after cleaning\n", "    df_clean = df_clean.drop_duplicates(subset=['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship'])\n", "    \n", "    return df_clean.reset_index(drop=True)\n", "\n", "# Execute Stage 1\n", "extract_simplified_hierarchy()\n", "df_stage1 = pd.DataFrame(all_relationships)\n", "df_stage1 = normalize_node_types(df_stage1)\n", "df_stage1 = clean_folder_names(df_stage1)\n", "df_stage1.to_csv('stage1_hierarchy.csv', index=False)\n", "\n", "print(f\"Stage 1: {len(df_stage1)} relationships\")\n", "print(\"Node types:\", sorted(df_stage1['destination_type'].unique()))\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>source_node</th>\n", "      <th>source_type</th>\n", "      <th>destination_node</th>\n", "      <th>destination_type</th>\n", "      <th>relationship</th>\n", "      <th>file_path</th>\n", "      <th>application</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>OneInsights</td>\n", "      <td>FOLDERS</td>\n", "      <td>ServiceBolt</td>\n", "      <td>FOLDERS</td>\n", "      <td>CONTAINS</td>\n", "      <td>None</td>\n", "      <td>ServiceBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>OneInsights</td>\n", "      <td>FOLDERS</td>\n", "      <td>UnifiedBolt</td>\n", "      <td>FOLDERS</td>\n", "      <td>CONTAINS</td>\n", "      <td>None</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ServiceBolt</td>\n", "      <td>FOLDERS</td>\n", "      <td>api</td>\n", "      <td>FOLDERS</td>\n", "      <td>CONTAINS</td>\n", "      <td>None</td>\n", "      <td>ServiceBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>api</td>\n", "      <td>FOLDERS</td>\n", "      <td>BuildToolController.java</td>\n", "      <td>FILE</td>\n", "      <td>CONTAINS</td>\n", "      <td>ServiceBolt\\api\\BuildToolController.java</td>\n", "      <td>ServiceBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>ServiceBolt</td>\n", "      <td>FOLDERS</td>\n", "      <td>service</td>\n", "      <td>FOLDERS</td>\n", "      <td>CONTAINS</td>\n", "      <td>None</td>\n", "      <td>ServiceBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>service</td>\n", "      <td>FOLDERS</td>\n", "      <td>BuildToolService.java</td>\n", "      <td>FILE</td>\n", "      <td>CONTAINS</td>\n", "      <td>ServiceBolt\\service\\BuildToolService.java</td>\n", "      <td>ServiceBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>service</td>\n", "      <td>FOLDERS</td>\n", "      <td>BuildToolServiceImplemantation.java</td>\n", "      <td>FILE</td>\n", "      <td>CONTAINS</td>\n", "      <td>ServiceBolt\\service\\BuildToolServiceImplemanta...</td>\n", "      <td>ServiceBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>UnifiedBolt</td>\n", "      <td>FOLDERS</td>\n", "      <td>core</td>\n", "      <td>FOLDERS</td>\n", "      <td>CONTAINS</td>\n", "      <td>None</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>core</td>\n", "      <td>FOLDERS</td>\n", "      <td>model</td>\n", "      <td>FOLDERS</td>\n", "      <td>CONTAINS</td>\n", "      <td>None</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>model</td>\n", "      <td>FOLDERS</td>\n", "      <td>BaseModel.java</td>\n", "      <td>FILE</td>\n", "      <td>CONTAINS</td>\n", "      <td>UnifiedBolt\\core\\model\\BaseModel.java</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>model</td>\n", "      <td>FOLDERS</td>\n", "      <td>BuildFailurePatternForProjectInJenkinsModel.java</td>\n", "      <td>FILE</td>\n", "      <td>CONTAINS</td>\n", "      <td>UnifiedBolt\\core\\model\\BuildFailurePatternForP...</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>model</td>\n", "      <td>FOLDERS</td>\n", "      <td>BuildFailurePatternMetrics.java</td>\n", "      <td>FILE</td>\n", "      <td>CONTAINS</td>\n", "      <td>UnifiedBolt\\core\\model\\BuildFailurePatternMetr...</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>model</td>\n", "      <td>FOLDERS</td>\n", "      <td>BuildFileInfo.java</td>\n", "      <td>FILE</td>\n", "      <td>CONTAINS</td>\n", "      <td>UnifiedBolt\\core\\model\\BuildFileInfo.java</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>model</td>\n", "      <td>FOLDERS</td>\n", "      <td>BuildInfo.java</td>\n", "      <td>FILE</td>\n", "      <td>CONTAINS</td>\n", "      <td>UnifiedBolt\\core\\model\\BuildInfo.java</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>model</td>\n", "      <td>FOLDERS</td>\n", "      <td>BuildSteps.java</td>\n", "      <td>FILE</td>\n", "      <td>CONTAINS</td>\n", "      <td>UnifiedBolt\\core\\model\\BuildSteps.java</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>model</td>\n", "      <td>FOLDERS</td>\n", "      <td>BuildTool.java</td>\n", "      <td>FILE</td>\n", "      <td>CONTAINS</td>\n", "      <td>UnifiedBolt\\core\\model\\BuildTool.java</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>model</td>\n", "      <td>FOLDERS</td>\n", "      <td>BuildToolMetric.java</td>\n", "      <td>FILE</td>\n", "      <td>CONTAINS</td>\n", "      <td>UnifiedBolt\\core\\model\\BuildToolMetric.java</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>model</td>\n", "      <td>FOLDERS</td>\n", "      <td>ConfigurationSetting.java</td>\n", "      <td>FILE</td>\n", "      <td>CONTAINS</td>\n", "      <td>UnifiedBolt\\core\\model\\ConfigurationSetting.java</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>model</td>\n", "      <td>FOLDERS</td>\n", "      <td>ConfigurationToolInfoMetric.java</td>\n", "      <td>FILE</td>\n", "      <td>CONTAINS</td>\n", "      <td>UnifiedBolt\\core\\model\\ConfigurationToolInfoMe...</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>core</td>\n", "      <td>FOLDERS</td>\n", "      <td>repository</td>\n", "      <td>FOLDERS</td>\n", "      <td>CONTAINS</td>\n", "      <td>None</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>repository</td>\n", "      <td>FOLDERS</td>\n", "      <td>BuildFailurePatternForProjectRepo.java</td>\n", "      <td>FILE</td>\n", "      <td>CONTAINS</td>\n", "      <td>UnifiedBolt\\core\\repository\\BuildFailurePatter...</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>repository</td>\n", "      <td>FOLDERS</td>\n", "      <td>BuildToolRep.java</td>\n", "      <td>FILE</td>\n", "      <td>CONTAINS</td>\n", "      <td>UnifiedBolt\\core\\repository\\BuildToolRep.java</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>repository</td>\n", "      <td>FOLDERS</td>\n", "      <td>ConfigurationSettingRep.java</td>\n", "      <td>FILE</td>\n", "      <td>CONTAINS</td>\n", "      <td>UnifiedBolt\\core\\repository\\ConfigurationSetti...</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>UnifiedBolt</td>\n", "      <td>FOLDERS</td>\n", "      <td>githubaction</td>\n", "      <td>FOLDERS</td>\n", "      <td>CONTAINS</td>\n", "      <td>None</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>githubaction</td>\n", "      <td>FOLDERS</td>\n", "      <td>GithubAction.java</td>\n", "      <td>FILE</td>\n", "      <td>CONTAINS</td>\n", "      <td>UnifiedBolt\\githubaction\\GithubAction.java</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>githubaction</td>\n", "      <td>FOLDERS</td>\n", "      <td>GithubActionApplication.java</td>\n", "      <td>FILE</td>\n", "      <td>CONTAINS</td>\n", "      <td>UnifiedBolt\\githubaction\\GithubActionApplicati...</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>githubaction</td>\n", "      <td>FOLDERS</td>\n", "      <td>GithubActionImplementation.java</td>\n", "      <td>FILE</td>\n", "      <td>CONTAINS</td>\n", "      <td>UnifiedBolt\\githubaction\\GithubActionImplement...</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     source_node source_type  \\\n", "0    OneInsights     FOLDERS   \n", "1    OneInsights     FOLDERS   \n", "2    ServiceBolt     FOLDERS   \n", "3            api     FOLDERS   \n", "4    ServiceBolt     FOLDERS   \n", "5        service     FOLDERS   \n", "6        service     FOLDERS   \n", "7    UnifiedBolt     FOLDERS   \n", "8           core     FOLDERS   \n", "9          model     FOLDERS   \n", "10         model     FOLDERS   \n", "11         model     FOLDERS   \n", "12         model     FOLDERS   \n", "13         model     FOLDERS   \n", "14         model     FOLDERS   \n", "15         model     FOLDERS   \n", "16         model     FOLDERS   \n", "17         model     FOLDERS   \n", "18         model     FOLDERS   \n", "19          core     FOLDERS   \n", "20    repository     FOLDERS   \n", "21    repository     FOLDERS   \n", "22    repository     FOLDERS   \n", "23   UnifiedBolt     FOLDERS   \n", "24  githubaction     FOLDERS   \n", "25  githubaction     FOLDERS   \n", "26  githubaction     FOLDERS   \n", "\n", "                                    destination_node destination_type  \\\n", "0                                        ServiceBolt          FOLDERS   \n", "1                                        UnifiedBolt          FOLDERS   \n", "2                                                api          FOLDERS   \n", "3                           BuildToolController.java             FILE   \n", "4                                            service          FOLDERS   \n", "5                              BuildToolService.java             FILE   \n", "6                BuildToolServiceImplemantation.java             FILE   \n", "7                                               core          FOLDERS   \n", "8                                              model          FOLDERS   \n", "9                                     BaseModel.java             FILE   \n", "10  BuildFailurePatternForProjectInJenkinsModel.java             FILE   \n", "11                   BuildFailurePatternMetrics.java             FILE   \n", "12                                BuildFileInfo.java             FILE   \n", "13                                    BuildInfo.java             FILE   \n", "14                                   BuildSteps.java             FILE   \n", "15                                    BuildTool.java             FILE   \n", "16                              BuildToolMetric.java             FILE   \n", "17                         ConfigurationSetting.java             FILE   \n", "18                  ConfigurationToolInfoMetric.java             FILE   \n", "19                                        repository          FOLDERS   \n", "20            BuildFailurePatternForProjectRepo.java             FILE   \n", "21                                 BuildToolRep.java             FILE   \n", "22                      ConfigurationSettingRep.java             FILE   \n", "23                                      githubaction          FOLDERS   \n", "24                                 GithubAction.java             FILE   \n", "25                      GithubActionApplication.java             FILE   \n", "26                   GithubActionImplementation.java             FILE   \n", "\n", "   relationship                                          file_path  \\\n", "0      CONTAINS                                               None   \n", "1      CONTAINS                                               None   \n", "2      CONTAINS                                               None   \n", "3      CONTAINS           ServiceBolt\\api\\BuildToolController.java   \n", "4      CONTAINS                                               None   \n", "5      CONTAINS          ServiceBolt\\service\\BuildToolService.java   \n", "6      CONTAINS  ServiceBolt\\service\\BuildToolServiceImplemanta...   \n", "7      CONTAINS                                               None   \n", "8      CONTAINS                                               None   \n", "9      CONTAINS              UnifiedBolt\\core\\model\\BaseModel.java   \n", "10     CONTAINS  UnifiedBolt\\core\\model\\BuildFailurePatternForP...   \n", "11     CONTAINS  UnifiedBolt\\core\\model\\BuildFailurePatternMetr...   \n", "12     CONTAINS          UnifiedBolt\\core\\model\\BuildFileInfo.java   \n", "13     CONTAINS              UnifiedBolt\\core\\model\\BuildInfo.java   \n", "14     CONTAINS             UnifiedBolt\\core\\model\\BuildSteps.java   \n", "15     CONTAINS              UnifiedBolt\\core\\model\\BuildTool.java   \n", "16     CONTAINS        UnifiedBolt\\core\\model\\BuildToolMetric.java   \n", "17     CONTAINS   UnifiedBolt\\core\\model\\ConfigurationSetting.java   \n", "18     CONTAINS  UnifiedBolt\\core\\model\\ConfigurationToolInfoMe...   \n", "19     CONTAINS                                               None   \n", "20     CONTAINS  UnifiedBolt\\core\\repository\\BuildFailurePatter...   \n", "21     CONTAINS      UnifiedBolt\\core\\repository\\BuildToolRep.java   \n", "22     CONTAINS  UnifiedBolt\\core\\repository\\ConfigurationSetti...   \n", "23     CONTAINS                                               None   \n", "24     CONTAINS         UnifiedBolt\\githubaction\\GithubAction.java   \n", "25     CONTAINS  UnifiedBolt\\githubaction\\GithubActionApplicati...   \n", "26     CONTAINS  UnifiedBolt\\githubaction\\GithubActionImplement...   \n", "\n", "    application  \n", "0   ServiceBolt  \n", "1   UnifiedBolt  \n", "2   ServiceBolt  \n", "3   ServiceBolt  \n", "4   ServiceBolt  \n", "5   ServiceBolt  \n", "6   ServiceBolt  \n", "7   UnifiedBolt  \n", "8   UnifiedBolt  \n", "9   UnifiedBolt  \n", "10  UnifiedBolt  \n", "11  UnifiedBolt  \n", "12  UnifiedBolt  \n", "13  UnifiedBolt  \n", "14  UnifiedBolt  \n", "15  UnifiedBolt  \n", "16  UnifiedBolt  \n", "17  UnifiedBolt  \n", "18  UnifiedBolt  \n", "19  UnifiedBolt  \n", "20  UnifiedBolt  \n", "21  UnifiedBolt  \n", "22  UnifiedBolt  \n", "23  UnifiedBolt  \n", "24  UnifiedBolt  \n", "25  UnifiedBolt  \n", "26  UnifiedBolt  "]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["df_stage1"]}, {"cell_type": "code", "execution_count": 62, "id": "stage2_functions", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Stage 2 functions loaded\n"]}], "source": ["def extract_package_and_imports(source_code_str):\n", "    package_pattern = r'package\\s+([\\w\\.]+);'\n", "    import_pattern = r'import\\s+([\\w\\.]+);'\n", "    package_match = re.search(package_pattern, source_code_str)\n", "    package_name = package_match.group(1) if package_match else None\n", "    import_matches = re.findall(import_pattern, source_code_str)\n", "    return package_name, import_matches\n", "\n", "def extract_api_endpoints(source_code_str):\n", "    endpoints = []\n", "    mapping_patterns = {\n", "        'RequestMapping': [r'@RequestMapping\\s*\\(\\s*value\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']', r'@RequestMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']'],\n", "        'GetMapping': [r'@GetMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']'],\n", "        'PostMapping': [r'@PostMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']'],\n", "        'PutMapping': [r'@PutMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']'],\n", "        'DeleteMapping': [r'@DeleteMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']']\n", "    }\n", "    \n", "    for mapping_type, patterns in mapping_patterns.items():\n", "        for pattern in patterns:\n", "            matches = re.findall(pattern, source_code_str, re.MULTILINE | re.DOTALL)\n", "            for match in matches:\n", "                if match.strip():\n", "                    endpoints.append({\n", "                        'type': mapping_type,\n", "                        'path': match.strip(),\n", "                        'method': mapping_type.replace('Mapping', '').upper() if mapping_type != 'RequestMapping' else 'GET'\n", "                    })\n", "    return endpoints\n", "\n", "def extract_database_entities(source_code_str):\n", "    entities = []\n", "    entity_patterns = [r'@Entity\\s*(?:\\([^)]*\\))?', r'@Table\\s*\\(\\s*name\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']']\n", "    \n", "    for pattern in entity_patterns:\n", "        if re.search(pattern, source_code_str, re.MULTILINE):\n", "            table_matches = re.findall(r'@Table\\s*\\(\\s*name\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']', source_code_str)\n", "            for table_name in table_matches:\n", "                if table_name.strip():\n", "                    entities.append({'type': 'DATA', 'name': table_name.strip()})\n", "            \n", "            if not table_matches:\n", "                class_match = re.search(r'public\\s+class\\s+(\\w+)', source_code_str)\n", "                if class_match:\n", "                    class_name = class_match.group(1)\n", "                    table_name = re.sub('([a-z0-9])([A-Z])', r'\\1_\\2', class_name).lower()\n", "                    entities.append({'type': 'DATA', 'name': table_name})\n", "    return entities\n", "\n", "def extract_class_relationships(source_code_str):\n", "    relationships = []\n", "    \n", "    # Class extends\n", "    class_extends_pattern = r'class\\s+(\\w+)\\s+extends\\s+([\\w<>]+)'\n", "    class_matches = re.findall(class_extends_pattern, source_code_str)\n", "    for child_class, parent_class in class_matches:\n", "        parent_class = re.sub(r'<.*?>', '', parent_class).strip()\n", "        if parent_class:\n", "            relationships.append({'child': child_class, 'parent': parent_class, 'type': 'EXTENDS'})\n", "    \n", "    # Class implements\n", "    implements_pattern = r'class\\s+(\\w+)(?:\\s+extends\\s+\\w+)?\\s+implements\\s+([\\w<>,\\s]+)'\n", "    impl_matches = re.findall(implements_pattern, source_code_str)\n", "    for class_name, implements_clause in impl_matches:\n", "        interfaces = [part.strip().split('<')[0].strip() for part in implements_clause.split(',')]\n", "        for interface in interfaces:\n", "            if interface:\n", "                relationships.append({'child': class_name, 'parent': interface, 'type': 'IMPLEMENTS'})\n", "    \n", "    return relationships\n", "\n", "print(\"✅ Stage 2 functions loaded\")"]}, {"cell_type": "code", "execution_count": 63, "id": "stage2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Stage 2: 891 total relationships\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>source_node</th>\n", "      <th>source_type</th>\n", "      <th>destination_node</th>\n", "      <th>destination_type</th>\n", "      <th>relationship</th>\n", "      <th>file_path</th>\n", "      <th>application</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>886</th>\n", "      <td>createHeaders</td>\n", "      <td>METHOD</td>\n", "      <td>createHeaders.auth</td>\n", "      <td>VARIABLE</td>\n", "      <td>DECLARES_VARIABLE</td>\n", "      <td>UnifiedBolt\\githubaction\\GithubActionImplement...</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>887</th>\n", "      <td>createHeaders</td>\n", "      <td>METHOD</td>\n", "      <td>createHeaders.encodedAuth</td>\n", "      <td>VARIABLE</td>\n", "      <td>DECLARES_VARIABLE</td>\n", "      <td>UnifiedBolt\\githubaction\\GithubActionImplement...</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>888</th>\n", "      <td>createHeaders</td>\n", "      <td>METHOD</td>\n", "      <td>createHeaders.authHeader</td>\n", "      <td>VARIABLE</td>\n", "      <td>DECLARES_VARIABLE</td>\n", "      <td>UnifiedBolt\\githubaction\\GithubActionImplement...</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>889</th>\n", "      <td>createHeaders</td>\n", "      <td>METHOD</td>\n", "      <td>createHeaders.headers</td>\n", "      <td>VARIABLE</td>\n", "      <td>DECLARES_VARIABLE</td>\n", "      <td>UnifiedBolt\\githubaction\\GithubActionImplement...</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>890</th>\n", "      <td>get</td>\n", "      <td>METHOD</td>\n", "      <td>get.requestFactory</td>\n", "      <td>VARIABLE</td>\n", "      <td>DECLARES_VARIABLE</td>\n", "      <td>UnifiedBolt\\githubaction\\GithubActionImplement...</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       source_node source_type           destination_node destination_type  \\\n", "886  createHeaders      METHOD         createHeaders.auth         VARIABLE   \n", "887  createHeaders      METHOD  createHeaders.encodedAuth         VARIABLE   \n", "888  createHeaders      METHOD   createHeaders.authHeader         VARIABLE   \n", "889  createHeaders      METHOD      createHeaders.headers         VARIABLE   \n", "890            get      METHOD         get.requestFactory         VARIABLE   \n", "\n", "          relationship                                          file_path  \\\n", "886  DECLARES_VARIABLE  UnifiedBolt\\githubaction\\GithubActionImplement...   \n", "887  DECLARES_VARIABLE  UnifiedBolt\\githubaction\\GithubActionImplement...   \n", "888  DECLARES_VARIABLE  UnifiedBolt\\githubaction\\GithubActionImplement...   \n", "889  DECLARES_VARIABLE  UnifiedBolt\\githubaction\\GithubActionImplement...   \n", "890  DECLARES_VARIABLE  UnifiedBolt\\githubaction\\GithubActionImplement...   \n", "\n", "     application  \n", "886  UnifiedBolt  \n", "887  UnifiedBolt  \n", "888  UnifiedBolt  \n", "889  UnifiedBolt  \n", "890  UnifiedBolt  "]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["# STAGE 2: CLASS REGISTRY & AST EXTRACTION\n", "def build_class_registry():\n", "    global class_registry\n", "    \n", "    for root, _, files in os.walk(BASE_PATH):\n", "        for file in files:\n", "            if file.endswith('.java'):\n", "                file_path = os.path.join(root, file)\n", "                application = get_application_from_path(file_path)\n", "                \n", "                try:\n", "                    with open(file_path, 'r', encoding='utf-8') as f:\n", "                        source_code_str = f.read()\n", "                    \n", "                    class_name = os.path.splitext(file)[0]\n", "                    package_name, imports = extract_package_and_imports(source_code_str)\n", "                    fqcn = f\"{package_name}.{class_name}\" if package_name else class_name\n", "                    \n", "                    endpoints = extract_api_endpoints(source_code_str)\n", "                    db_entities = extract_database_entities(source_code_str)\n", "                    class_relationships = extract_class_relationships(source_code_str)\n", "                    \n", "                    class_registry[fqcn] = {\n", "                        'fqcn': fqcn, 'class_name': class_name, 'package': package_name,\n", "                        'file_path': file_path, 'application': application, 'imports': imports,\n", "                        'endpoints': endpoints, 'db_entities': db_entities, 'class_relationships': class_relationships\n", "                    }\n", "                    \n", "                    # Add relationships\n", "                    if endpoints:\n", "                        for ep in endpoints:\n", "                            all_relationships.append({\n", "                                'source_node': class_name, 'source_type': 'CLASS',\n", "                                'destination_node': f\"{ep['method']} {ep['path']}\", 'destination_type': 'ENDPOINT',\n", "                                'relationship': 'EXPOSES', 'file_path': os.path.relpath(file_path, BASE_PATH),\n", "                                'application': application\n", "                            })\n", "                    \n", "                    if db_entities:\n", "                        for entity in db_entities:\n", "                            all_relationships.append({\n", "                                'source_node': class_name, 'source_type': 'CLASS',\n", "                                'destination_node': entity['name'], 'destination_type': 'DATA',\n", "                                'relationship': 'DATA_FIND', 'file_path': os.path.relpath(file_path, BASE_PATH),\n", "                                'application': application\n", "                            })\n", "                    \n", "                    if class_relationships:\n", "                        for rel in class_relationships:\n", "                            all_relationships.append({\n", "                                'source_node': rel['child'], 'source_type': 'CLASS',\n", "                                'destination_node': rel['parent'], \n", "                                'destination_type': 'INTERFACE' if rel['type'] == 'IMPLEMENTS' else 'CLASS',\n", "                                'relationship': rel['type'], 'file_path': os.path.relpath(file_path, BASE_PATH),\n", "                                'application': application\n", "                            })\n", "                \n", "                except Exception as e:\n", "                    print(f\"❌ Error processing {file_path}: {e}\")\n", "                    continue\n", "    \n", "    return class_registry\n", "\n", "def read_source_code(file_path):\n", "    with open(file_path, 'rb') as f:\n", "        return f.read()\n", "\n", "def extract_ast_structure(file_path):\n", "    records = []\n", "    source_code = read_source_code(file_path)\n", "    tree = parser.parse(source_code)\n", "    root_node = tree.root_node\n", "    file_name = os.path.basename(file_path)\n", "    application = get_application_from_path(file_path)\n", "\n", "    def traverse(node, parent_type=None, parent_name=None, method_context=None):\n", "        if node.type == 'class_declaration':\n", "            class_name = None\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    class_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "                    records.append({\n", "                        'source_node': file_name, 'source_type': 'FILE',\n", "                        'destination_node': class_name, 'destination_type': 'CLASS',\n", "                        'relationship': 'DECLARES', 'file_path': os.path.relpath(file_path, BASE_PATH),\n", "                        'application': application\n", "                    })\n", "                    break\n", "            for child in node.children:\n", "                traverse(child, 'class', class_name, None)\n", "\n", "        elif node.type == 'interface_declaration':\n", "            interface_name = None\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    interface_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "                    records.append({\n", "                        'source_node': file_name, 'source_type': 'FILE',\n", "                        'destination_node': interface_name, 'destination_type': 'INTERFACE',\n", "                        'relationship': 'DECLARES', 'file_path': os.path.relpath(file_path, BASE_PATH),\n", "                        'application': application\n", "                    })\n", "                    break\n", "            for child in node.children:\n", "                traverse(child, 'interface', interface_name, None)\n", "\n", "        elif node.type == 'method_declaration':\n", "            method_name = None\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    method_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "                    if parent_name and parent_type in ['class', 'interface']:\n", "                        records.append({\n", "                            'source_node': parent_name, 'source_type': parent_type.upper(),\n", "                            'destination_node': method_name, 'destination_type': 'METHOD',\n", "                            'relationship': 'DECLARES', 'file_path': os.path.relpath(file_path, BASE_PATH),\n", "                            'application': application\n", "                        })\n", "                    break\n", "            for child in node.children:\n", "                traverse(child, 'method', method_name, method_name)\n", "\n", "        elif node.type == 'field_declaration':\n", "            for child in node.children:\n", "                if child.type == 'variable_declarator':\n", "                    for grandchild in child.children:\n", "                        if grandchild.type == 'identifier':\n", "                            field_name = clean_node_name(source_code[grandchild.start_byte:grandchild.end_byte].decode('utf-8'))\n", "                            if parent_name and parent_type == 'class' and not is_temp_variable(field_name):\n", "                                formatted_var_name = format_variable_name(parent_name, field_name)\n", "                                records.append({\n", "                                    'source_node': parent_name, 'source_type': 'CLASS',\n", "                                    'destination_node': formatted_var_name, 'destination_type': 'VARIABLE',\n", "                                    'relationship': 'HAS_FIELD', 'file_path': os.path.relpath(file_path, BASE_PATH),\n", "                                    'application': application\n", "                                })\n", "\n", "        elif node.type in ['assignment_expression', 'variable_declarator'] and parent_type == 'method':\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    var_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "                    if var_name and var_name != 'this' and not is_temp_variable(var_name) and method_context:\n", "                        formatted_var_name = format_variable_name(method_context, var_name)\n", "                        records.append({\n", "                            'source_node': parent_name, 'source_type': 'METHOD',\n", "                            'destination_node': formatted_var_name, 'destination_type': 'VARIABLE',\n", "                            'relationship': 'DECLARES_VARIABLE', 'file_path': os.path.relpath(file_path, BASE_PATH),\n", "                            'application': application\n", "                        })\n", "\n", "        for child in node.children:\n", "            traverse(child, parent_type, parent_name, method_context)\n", "\n", "    traverse(root_node)\n", "    return records\n", "\n", "# Execute Stage 2\n", "build_class_registry()\n", "ast_records = []\n", "for root, _, files in os.walk(BASE_PATH):\n", "    for file in files:\n", "        if file.endswith('.java'):\n", "            file_path = os.path.join(root, file)\n", "            try:\n", "                ast_records.extend(extract_ast_structure(file_path))\n", "            except Exception as e:\n", "                print(f'[Error] Failed to parse {file}: {e}')\n", "\n", "all_relationships.extend(ast_records)\n", "df_stage2 = pd.DataFrame(all_relationships)\n", "df_stage2 = normalize_node_types(df_stage2)\n", "df_stage2.to_csv('stage2_class_ast.csv', index=False)\n", "print(f\"Stage 2: {len(df_stage2)} total relationships\")\n", "df_stage2.tail()"]}, {"cell_type": "code", "execution_count": 64, "id": "stage3_functions", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Stage 3 functions loaded\n"]}], "source": ["def build_enhanced_system_prompt(file_path, ast_context, class_registry):\n", "    application = get_application_from_path(file_path)\n", "    \n", "    ast_context_str = \"\\n\".join([\n", "        f\"- {item['source_type']} '{item['source_node']}' {item['relationship']} {item['destination_type']} '{item['destination_node']}'\"\n", "        for item in ast_context[:20]\n", "    ])\n", "    \n", "    # Use class registry to get additional context\n", "    class_info = \"\"\n", "    file_name = os.path.basename(file_path).replace('.java', '')\n", "    for fqcn, info in class_registry.items():\n", "        if info['class_name'] == file_name:\n", "            if info.get('endpoints'):\n", "                class_info += f\"\\nEndpoints: {[ep['path'] for ep in info['endpoints']]}\"\n", "            if info.get('db_entities'):\n", "                class_info += f\"\\nDB Entities: {[e['name'] for e in info['db_entities']]}\"\n", "            break\n", "    \n", "    return f\"\"\"\n", "You are analyzing Java code for lineage extraction in application: {application}.\n", "\n", "CONTEXT FROM AST ANALYSIS:\n", "{ast_context_str}\n", "\n", "CLASS REGISTRY CONTEXT:\n", "{class_info}\n", "\n", "CRITICAL RULES - <PERSON><PERSON><PERSON><PERSON> EXACTLY:\n", "1. Use SIMPLE names only (remove prefixes like \"method:\", \"class:\", etc.)\n", "2. <PERSON><PERSON><PERSON><PERSON><PERSON> NODE TYPES: FOLDERS, FILE, CLASS, METHOD, INTERFACE, VARIABLE, ENDPOINT, DATA, OPERATION, VALUE\n", "3. <PERSON><PERSON><PERSON><PERSON><PERSON> RELATIONSHIPS: <PERSON><PERSON><PERSON><PERSON><PERSON>, DATA_FIND, DECLARES, DECLARES_VARIABLE, EXPOSES, EXTENDS, HAS_FIELD, IMPLEMENTS, TRA<PERSON><PERSON>ORMS_TO, USES, ASSIGNS_VALUE, UPDATED_BY\n", "4. VARIABLE NAMING: Format as method_name.variable_name (e.g., \"getUserData.userId\")\n", "5. FILTER OUT: Loop counters (i, j, k), temp variables, variables starting with _ or $\n", "6. RELATIONSHIP DIRECTIONS (DO NOT REVERSE):\n", "   - FILE -[DECLARES]-> CLASS\n", "   - CLASS -[DECLARES]-> METHOD  \n", "   - CLASS -[HAS_FIELD]-> VARIABLE\n", "   - METHOD -[DECLARES_VARIABLE]-> VARIABLE\n", "   - METHOD -[USES]-> VARIABLE\n", "   - VARIABLE -[TRANSFORMS_TO]-> VARIABLE\n", "   - OPERATION -[ASSIGNS_VALUE]-> VARIABLE\n", "   - VARIABLE -[UPDATED_BY]-> OPERATION\n", "   - CLASS -[EXTENDS]-> CLASS\n", "   - CLASS -[IMPLEMENTS]-> INTERFACE\n", "   - CLASS -[EXPOSES]-> ENDPOINT\n", "   - CLASS -[DAT<PERSON>_FIND]-> DATA\n", "\n", "7. FOCUS ON: Variable transformations, method calls, data operations, assignments\n", "8. Application context: {application}\n", "\n", "Extract ALL meaningful relationships following these exact patterns.\n", "\"\"\"\n", "\n", "def get_file_size_lines(file_path):\n", "    try:\n", "        with open(file_path, 'r', encoding='utf-8') as f:\n", "            return len(f.readlines())\n", "    except:\n", "        return 0\n", "\n", "def extract_class_metadata(file_path):\n", "    try:\n", "        with open(file_path, 'r', encoding='utf-8') as f:\n", "            content = f.read()\n", "        \n", "        package_match = re.search(r'package\\s+([\\w\\.]+);', content)\n", "        package = package_match.group(1) if package_match else None\n", "        \n", "        imports = re.findall(r'import\\s+([\\w\\.]+);', content)\n", "        class_names = re.findall(r'(?:public\\s+)?(?:class|interface)\\s+(\\w+)', content)\n", "        \n", "        return {\n", "            'package': package,\n", "            'imports': imports[:10],\n", "            'classes': class_names,\n", "            'file_name': os.path.basename(file_path)\n", "        }\n", "    except:\n", "        return {'package': None, 'imports': [], 'classes': [], 'file_name': os.path.basename(file_path)}\n", "\n", "def smart_chunk_by_logical_boundaries(file_path, max_lines=800):\n", "    try:\n", "        with open(file_path, 'r', encoding='utf-8') as f:\n", "            lines = f.readlines()\n", "        \n", "        if len(lines) <= max_lines:\n", "            return [{'content': ''.join(lines), 'start_line': 1, 'end_line': len(lines), 'chunk_id': 1}]\n", "        \n", "        chunks = []\n", "        current_chunk = []\n", "        current_line = 0\n", "        chunk_id = 1\n", "        start_line = 1\n", "        \n", "        brace_count = 0\n", "        in_method = False\n", "        \n", "        for i, line in enumerate(lines):\n", "            current_chunk.append(line)\n", "            current_line += 1\n", "            \n", "            brace_count += line.count('{') - line.count('}')\n", "            \n", "            if re.match(r'\\s*(public|private|protected|static).*\\{\\s*$', line.strip()):\n", "                in_method = True\n", "            \n", "            if (current_line >= max_lines and brace_count == 0 and not in_method) or i == len(lines) - 1:\n", "                chunks.append({\n", "                    'content': ''.join(current_chunk),\n", "                    'start_line': start_line,\n", "                    'end_line': start_line + len(current_chunk) - 1,\n", "                    'chunk_id': chunk_id\n", "                })\n", "                \n", "                current_chunk = []\n", "                current_line = 0\n", "                chunk_id += 1\n", "                start_line = i + 2\n", "                in_method = False\n", "        \n", "        return chunks if chunks else [{'content': ''.join(lines), 'start_line': 1, 'end_line': len(lines), 'chunk_id': 1}]\n", "    \n", "    except Exception as e:\n", "        with open(file_path, 'r', encoding='utf-8') as f:\n", "            content = f.read()\n", "        return [{'content': content, 'start_line': 1, 'end_line': len(content.split('\\n')), 'chunk_id': 1}]\n", "\n", "def process_chunk_with_llm(chunk_data, file_path, ast_context, class_metadata):\n", "    try:\n", "        from langchain.schema import Document\n", "        doc = Document(page_content=chunk_data['content'])\n", "        \n", "        application = get_application_from_path(file_path)\n", "        \n", "        ast_context_str = \"\\n\".join([\n", "            f\"- {item['source_type']} '{item['source_node']}' {item['relationship']} {item['destination_type']} '{item['destination_node']}'\"\n", "            for item in ast_context[:15]\n", "        ])\n", "        \n", "        class_info = f\"\"\"\n", "Package: {class_metadata.get('package', 'None')}\n", "Classes: {', '.join(class_metadata.get('classes', []))}\n", "Key Imports: {', '.join(class_metadata.get('imports', [])[:5])}\n", "Chunk: {chunk_data['chunk_id']} (Lines {chunk_data['start_line']}-{chunk_data['end_line']})\n", "\"\"\"\n", "        \n", "        system_prompt = f\"\"\"\n", "You are analyzing Java code chunk for lineage extraction in application: {application}.\n", "\n", "CONTEXT FROM AST ANALYSIS:\n", "{ast_context_str}\n", "\n", "CLASS METADATA CONTEXT:\n", "{class_info}\n", "\n", "CRITICAL RULES - <PERSON><PERSON><PERSON><PERSON> EXACTLY:\n", "1. Use SIMPLE names only (remove prefixes like \"method:\", \"class:\", etc.)\n", "2. <PERSON><PERSON><PERSON><PERSON><PERSON> NODE TYPES:FOLDERS, FILE, CLASS, METHOD, INTERFACE, VARIABLE, ENDPOINT, DATA, OPERATION, VALUE\n", "3. <PERSON><PERSON><PERSON><PERSON><PERSON> RELATIONSHIPS: <PERSON><PERSON><PERSON><PERSON><PERSON>, DATA_FIND, DECLARES, DECLARES_VARIABLE, EXPOSES, EXTENDS, HAS_FIELD, IMPLEMENTS, TRA<PERSON><PERSON>ORMS_TO, USES, ASSIGNS_VALUE, UPDATED_BY\n", "4. VARIABLE NAMING: Format as method_name.variable_name (e.g., \"getUserData.userId\")\n", "5. FILTER OUT: Loop counters (i, j, k), temp variables, variables starting with _ or $\n", "6. FOCUS ON: Variable transformations, method calls, data operations, assignments\n", "7. Application context: {application}\n", "\n", "Extract ALL meaningful relationships following these exact patterns.\n", "\"\"\"\n", "\n", "        transformer = LLMGraphTransformer(\n", "            llm=llm,\n", "            additional_instructions=system_prompt,\n", "            allowed_nodes=['FOLDERS', 'FILE', 'CLASS', 'METHOD', 'INTERFACE', 'VARIABLE', 'ENDPOINT', 'DATA', 'OPERATION', 'VALUE'],\n", "            allowed_relationships=[\n", "                ('FOLDERS', 'CONTAINS', 'FILE'), ('FILE', 'DECLARES', 'CLASS'), ('FILE', 'DECLARES', 'INTERFACE'),\n", "                ('CLASS', 'DECLARES', 'METHOD'), ('INTERFACE', 'DECLARES', 'METHOD'),\n", "                ('CLASS', 'HAS_FIELD', 'VARIABLE'), ('METHOD', 'DECLARES_VARIABLE', 'VARIABLE'),\n", "                ('METHOD', 'USES', 'VARIABLE'), ('VARIABLE', 'TRANSFORMS_TO', 'VARIABLE'),\n", "                ('OPERATION', 'ASSIGNS_VALUE', 'VARIABLE'), ('VARIABLE', 'UPDATED_BY', 'OPERATION'),\n", "                ('CLASS', 'EXTENDS', 'CLASS'), ('CLASS', 'IMPLEMENTS', 'INTERFACE'),\n", "                ('CLASS', 'EXPOSES', 'ENDPOINT'), ('CLASS', 'DATA_FIND', 'DATA')\n", "            ]\n", "        )\n", "        \n", "        graph_docs = transformer.convert_to_graph_documents([doc])\n", "        \n", "        chunk_relationships = []\n", "        for graph_doc in graph_docs:\n", "            for rel in graph_doc.relationships:\n", "                chunk_relationships.append({\n", "                    'source_node': rel.source.id, 'source_type': rel.source.type,\n", "                    'destination_node': rel.target.id, 'destination_type': rel.target.type,\n", "                    'relationship': rel.type, 'file_path': os.path.relpath(file_path, BASE_PATH),\n", "                    'application': application, 'chunk_id': chunk_data['chunk_id'],\n", "                    'chunk_lines': f\"{chunk_data['start_line']}-{chunk_data['end_line']}\"\n", "                })\n", "        \n", "        return chunk_relationships\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error processing chunk {chunk_data['chunk_id']}: {e}\")\n", "        return []\n", "\n", "def process_file_with_hybrid_approach(file_path, ast_context, max_lines_threshold=1000):\n", "    file_lines = get_file_size_lines(file_path)\n", "    file_name = os.path.basename(file_path)\n", "    \n", "    if file_lines <= max_lines_threshold:\n", "        # Small file: use original approach\n", "        try:\n", "            loader = TextLoader(file_path)\n", "            docs = loader.load()\n", "            \n", "            splitter = RecursiveCharacterTextSplitter.from_language(\n", "                language=LC_Language.JAVA, chunk_size=4000, chunk_overlap=200\n", "            )\n", "            split_docs = splitter.split_documents(docs)\n", "            \n", "            system_prompt = build_enhanced_system_prompt(file_path, ast_context, class_registry)\n", "            application = get_application_from_path(file_path)\n", "\n", "            transformer = LLMGraphTransformer(\n", "                llm=llm, additional_instructions=system_prompt,\n", "                allowed_nodes=['FOLDERS', 'FILE', 'CLASS', 'METHOD', 'INTERFACE', 'VARIABLE', 'ENDPOINT', 'DATA', 'OPERATION', 'VALUE'],\n", "                allowed_relationships=[\n", "                    ('FOLDERS', 'CONTAINS', 'FILE'), ('FILE', 'DECLARES', 'CLASS'), ('FILE', 'DECLARES', 'INTERFACE'),\n", "                    ('CLASS', 'DECLARES', 'METHOD'), ('INTERFACE', 'DECLARES', 'METHOD'),\n", "                    ('CLASS', 'HAS_FIELD', 'VARIABLE'), ('METHOD', 'DECLARES_VARIABLE', 'VARIABLE'),\n", "                    ('METHOD', 'USES', 'VARIABLE'), ('VARIABLE', 'TRANSFORMS_TO', 'VARIABLE'),\n", "                    ('OPERATION', 'ASSIGNS_VALUE', 'VARIABLE'), ('VARIABLE', 'UPDATED_BY', 'OPERATION'),\n", "                    ('CLASS', 'EXTENDS', 'CLASS'), ('CLASS', 'IMPLEMENTS', 'INTERFACE'),\n", "                    ('CLASS', 'EXPOSES', 'ENDPOINT'), ('CLASS', 'DATA_FIND', 'DATA')\n", "                ]\n", "            )\n", "            \n", "            graph_docs = transformer.convert_to_graph_documents(split_docs)\n", "            \n", "            file_relationships = []\n", "            for doc in graph_docs:\n", "                for rel in doc.relationships:\n", "                    file_relationships.append({\n", "                        'source_node': rel.source.id, 'source_type': rel.source.type,\n", "                        'destination_node': rel.target.id, 'destination_type': rel.target.type,\n", "                        'relationship': rel.type, 'file_path': os.path.relpath(file_path, BASE_PATH),\n", "                        'application': application\n", "                    })\n", "            \n", "            print(f\"✅ Processed {file_name} ({file_lines} lines): {len(file_relationships)} relationships\")\n", "            return file_relationships\n", "            \n", "        except Exception as e:\n", "            print(f\"❌ Error processing {file_name}: {e}\")\n", "            return []\n", "    \n", "    else:\n", "        # Large file: use chunk-based approach\n", "        print(f\"📄 Large file detected: {file_name} ({file_lines} lines) - Using chunk-based processing\")\n", "        \n", "        class_metadata = extract_class_metadata(file_path)\n", "        chunks = smart_chunk_by_logical_boundaries(file_path)\n", "        \n", "        all_file_relationships = []\n", "        \n", "        for chunk in tqdm(chunks, desc=f\"Processing {file_name}\", leave=False):\n", "            chunk_rels = process_chunk_with_llm(chunk, file_path, ast_context, class_metadata)\n", "            all_file_relationships.extend(chunk_rels)\n", "            print(f\"  ✅ Chunk {chunk['chunk_id']} (lines {chunk['start_line']}-{chunk['end_line']}): {len(chunk_rels)} relationships\")\n", "        \n", "        print(f\"✅ Completed {file_name}: {len(chunks)} chunks, {len(all_file_relationships)} total relationships\")\n", "        return all_file_relationships\n", "\n", "print(\"✅ Stage 3 functions loaded\")"]}, {"cell_type": "code", "execution_count": 65, "id": "stage3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processing 19 Java files with Hybrid LLM approach...\n", "📊 File size analysis:\n", "  📄 Small files (<1000 lines): 19\n", "  📚 Large files (>1000 lines): 0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing files:   5%|▌         | 1/19 [00:55<16:31, 55.06s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Processed BuildToolController.java (176 lines): 74 relationships\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing files:  11%|█         | 2/19 [01:01<07:26, 26.27s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Processed BuildToolService.java (42 lines): 11 relationships\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing files:  16%|█▌        | 3/19 [01:57<10:42, 40.16s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Processed BuildToolServiceImplemantation.java (267 lines): 77 relationships\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing files:  21%|██        | 4/19 [02:02<06:32, 26.15s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Processed BaseModel.java (21 lines): 6 relationships\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing files:  26%|██▋       | 5/19 [02:18<05:16, 22.61s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Processed BuildFailurePatternForProjectInJenkinsModel.java (108 lines): 18 relationships\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing files:  32%|███▏      | 6/19 [02:29<04:02, 18.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Processed BuildFailurePatternMetrics.java (77 lines): 17 relationships\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing files:  37%|███▋      | 7/19 [02:35<02:52, 14.34s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Processed BuildFileInfo.java (22 lines): 9 relationships\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing files:  42%|████▏     | 8/19 [02:41<02:10, 11.85s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Processed BuildInfo.java (34 lines): 13 relationships\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing files:  47%|████▋     | 9/19 [02:50<01:49, 10.90s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Processed BuildSteps.java (50 lines): 20 relationships\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing files:  53%|█████▎    | 10/19 [04:05<04:34, 30.52s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Processed BuildTool.java (282 lines): 105 relationships\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing files:  58%|█████▊    | 11/19 [04:18<03:22, 25.28s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Processed BuildToolMetric.java (94 lines): 19 relationships\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing files:  63%|██████▎   | 12/19 [04:28<02:25, 20.74s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Processed ConfigurationSetting.java (80 lines): 24 relationships\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing files:  68%|██████▊   | 13/19 [05:00<02:23, 23.99s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Processed ConfigurationToolInfoMetric.java (195 lines): 53 relationships\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing files:  74%|███████▎  | 14/19 [05:03<01:28, 17.79s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Processed BuildFailurePatternForProjectRepo.java (22 lines): 3 relationships\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing files:  79%|███████▉  | 15/19 [05:14<01:02, 15.68s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Processed BuildToolRep.java (54 lines): 17 relationships\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing files:  84%|████████▍ | 16/19 [05:19<00:36, 12.31s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Processed ConfigurationSettingRep.java (14 lines): 4 relationships\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing files:  89%|████████▉ | 17/19 [05:22<00:19,  9.55s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Processed GithubAction.java (13 lines): 2 relationships\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing files:  95%|█████████▍| 18/19 [06:03<00:19, 19.16s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Processed GithubActionApplication.java (152 lines): 60 relationships\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing files: 100%|██████████| 19/19 [07:31<00:00, 23.78s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Processed GithubActionImplementation.java (408 lines): 123 relationships\n", "\n", "📊 Stage 3 Complete:\n", "  • Total relationships: 655\n", "  • Files processed: 19\n", "  • Chunks processed: 0\n", "  • Large files handled: 0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>source_node</th>\n", "      <th>source_type</th>\n", "      <th>destination_node</th>\n", "      <th>destination_type</th>\n", "      <th>relationship</th>\n", "      <th>file_path</th>\n", "      <th>application</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Buildtoolcontroller.Java</td>\n", "      <td>FILE</td>\n", "      <td>Buildtoolcontroller</td>\n", "      <td>CLASS</td>\n", "      <td>DECLARES</td>\n", "      <td>ServiceBolt\\api\\BuildToolController.java</td>\n", "      <td>ServiceBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Buildtoolcontroller</td>\n", "      <td>CLASS</td>\n", "      <td>Buildtoolcontroller.Buildtoolservice</td>\n", "      <td>VARIABLE</td>\n", "      <td>HAS_FIELD</td>\n", "      <td>ServiceBolt\\api\\BuildToolController.java</td>\n", "      <td>ServiceBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Buildtoolcontroller</td>\n", "      <td>CLASS</td>\n", "      <td>Getonejob</td>\n", "      <td>METHOD</td>\n", "      <td>DECLARES</td>\n", "      <td>ServiceBolt\\api\\BuildToolController.java</td>\n", "      <td>ServiceBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Buildtoolcontroller</td>\n", "      <td>CLASS</td>\n", "      <td>Getbuilddetailshome</td>\n", "      <td>METHOD</td>\n", "      <td>DECLARES</td>\n", "      <td>ServiceBolt\\api\\BuildToolController.java</td>\n", "      <td>ServiceBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Buildtoolcontroller</td>\n", "      <td>CLASS</td>\n", "      <td>Builddata</td>\n", "      <td>METHOD</td>\n", "      <td>DECLARES</td>\n", "      <td>ServiceBolt\\api\\BuildToolController.java</td>\n", "      <td>ServiceBolt</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                source_node source_type                      destination_node  \\\n", "0  Buildtoolcontroller.Java        FILE                   Buildtoolcontroller   \n", "1       Buildtoolcontroller       CLASS  Buildtoolcontroller.Buildtoolservice   \n", "2       Buildtoolcontroller       CLASS                             Getonejob   \n", "3       Buildtoolcontroller       CLASS                   Getbuilddetailshome   \n", "4       Buildtoolcontroller       CLASS                             Builddata   \n", "\n", "  destination_type relationship                                 file_path  \\\n", "0            CLASS     DECLARES  ServiceBolt\\api\\BuildToolController.java   \n", "1         VARIABLE    HAS_FIELD  ServiceBolt\\api\\BuildToolController.java   \n", "2           METHOD     DECLARES  ServiceBolt\\api\\BuildToolController.java   \n", "3           METHOD     DECLARES  ServiceBolt\\api\\BuildToolController.java   \n", "4           METHOD     DECLARES  ServiceBolt\\api\\BuildToolController.java   \n", "\n", "   application  \n", "0  ServiceBolt  \n", "1  ServiceBolt  \n", "2  ServiceBolt  \n", "3  ServiceBolt  \n", "4  ServiceBolt  "]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["# STAGE 3: HYBRID LLM PROCESSING (Smart File/Chunk-based)\n", "llm_relationships = []\n", "java_files = []\n", "\n", "for root, _, files in os.walk(BASE_PATH):\n", "    for file in files:\n", "        if file.endswith('.java'):\n", "            java_files.append(os.path.join(root, file))\n", "\n", "print(f\"Processing {len(java_files)} Java files with Hybrid LLM approach...\")\n", "print(f\"📊 File size analysis:\")\n", "\n", "# Analyze file sizes first\n", "small_files = []\n", "large_files = []\n", "for file_path in java_files:\n", "    file_lines = get_file_size_lines(file_path)\n", "    if file_lines > 1000:\n", "        large_files.append((file_path, file_lines))\n", "    else:\n", "        small_files.append((file_path, file_lines))\n", "\n", "print(f\"  📄 Small files (<1000 lines): {len(small_files)}\")\n", "print(f\"  📚 Large files (>1000 lines): {len(large_files)}\")\n", "if large_files:\n", "    print(f\"  📈 Largest file: {max(large_files, key=lambda x: x[1])[1]} lines\")\n", "\n", "# Process all files with hybrid approach\n", "total_chunks_processed = 0\n", "for file_path in tqdm(java_files, desc=\"Processing files\"):\n", "    file_ast_context = [rel for rel in ast_records if rel.get('file_path') == os.path.relpath(file_path, BASE_PATH)]\n", "    file_llm_rels = process_file_with_hybrid_approach(file_path, file_ast_context)\n", "    llm_relationships.extend(file_llm_rels)\n", "    \n", "    # Count chunks if file was chunked\n", "    chunk_count = len([rel for rel in file_llm_rels if 'chunk_id' in rel])\n", "    if chunk_count > 0:\n", "        unique_chunks = len(set([rel['chunk_id'] for rel in file_llm_rels if 'chunk_id' in rel]))\n", "        total_chunks_processed += unique_chunks\n", "\n", "all_relationships.extend(llm_relationships)\n", "df_stage3 = pd.DataFrame(llm_relationships)\n", "df_stage3 = normalize_node_types(df_stage3)\n", "df_stage3.to_csv('stage3_llm.csv', index=False)\n", "\n", "print(f\"\\n📊 Stage 3 Complete:\")\n", "print(f\"  • Total relationships: {len(df_stage3)}\")\n", "print(f\"  • Files processed: {len(java_files)}\")\n", "print(f\"  • Chunks processed: {total_chunks_processed}\")\n", "print(f\"  • Large files handled: {len(large_files)}\")\n", "df_stage3.head()"]}, {"cell_type": "code", "execution_count": 66, "id": "final_processing", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Final: 456 unique relationships (Stage 1 + Stage 3 only)\n", "Node types: ['CLASS', 'Endpoint', 'FILE', 'FOLDERS', 'INTERFACE', 'METHOD', 'VARIABLE']\n", "Relationship types: ['CONTAINS', 'DECLARES', 'DECLARES_VARIABLE', 'EXPOSES', 'HAS_FIELD', 'TRANSFORMS_TO', 'USES']\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>source_node</th>\n", "      <th>source_type</th>\n", "      <th>destination_node</th>\n", "      <th>destination_type</th>\n", "      <th>relationship</th>\n", "      <th>file_path</th>\n", "      <th>application</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>358</th>\n", "      <td>Buildtool</td>\n", "      <td>CLASS</td>\n", "      <td>Getgroupname</td>\n", "      <td>METHOD</td>\n", "      <td>DECLARES</td>\n", "      <td>UnifiedBolt\\core\\model\\BuildTool.java</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>118</th>\n", "      <td>Buildtoolserviceimplemantation</td>\n", "      <td>CLASS</td>\n", "      <td>Buildtoolserviceimplemantation.Nodataconst</td>\n", "      <td>VARIABLE</td>\n", "      <td>HAS_FIELD</td>\n", "      <td>ServiceBolt\\service\\BuildToolServiceImplemanta...</td>\n", "      <td>ServiceBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>Buildtoolcontroller</td>\n", "      <td>CLASS</td>\n", "      <td>Getbuildtype</td>\n", "      <td>METHOD</td>\n", "      <td>DECLARES</td>\n", "      <td>ServiceBolt\\api\\BuildToolController.java</td>\n", "      <td>ServiceBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>352</th>\n", "      <td>Buildtool</td>\n", "      <td>CLASS</td>\n", "      <td>Getjobcount</td>\n", "      <td>METHOD</td>\n", "      <td>DECLARES</td>\n", "      <td>UnifiedBolt\\core\\model\\BuildTool.java</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>451</th>\n", "      <td>Configurationtoolinfometric</td>\n", "      <td>CLASS</td>\n", "      <td>Getwidgetname</td>\n", "      <td>METHOD</td>\n", "      <td>DECLARES</td>\n", "      <td>UnifiedBolt\\core\\model\\ConfigurationToolInfoMe...</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>653</th>\n", "      <td>Githubactionimplementation</td>\n", "      <td>CLASS</td>\n", "      <td>Lastbuildid</td>\n", "      <td>VARIABLE</td>\n", "      <td>HAS_FIELD</td>\n", "      <td>UnifiedBolt\\githubaction\\GithubActionImplement...</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>195</th>\n", "      <td>Buildfailurepatternforprojectinjenkinsmodel.Java</td>\n", "      <td>FILE</td>\n", "      <td>Buildfailurepatternforprojectinjenkinsmodel</td>\n", "      <td>CLASS</td>\n", "      <td>DECLARES</td>\n", "      <td>UnifiedBolt\\core\\model\\BuildFailurePatternForP...</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>387</th>\n", "      <td>Setvalue</td>\n", "      <td>METHOD</td>\n", "      <td>Setvalue.Value</td>\n", "      <td>VARIABLE</td>\n", "      <td>DECLARES_VARIABLE</td>\n", "      <td>UnifiedBolt\\core\\model\\BuildToolMetric.java</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>120</th>\n", "      <td>Buildtoolserviceimplemantation</td>\n", "      <td>CLASS</td>\n", "      <td>Search</td>\n", "      <td>METHOD</td>\n", "      <td>DECLARES</td>\n", "      <td>ServiceBolt\\service\\BuildToolServiceImplemanta...</td>\n", "      <td>ServiceBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>218</th>\n", "      <td>Buildfailurepatternmetrics</td>\n", "      <td>CLASS</td>\n", "      <td>Getpatterndefined</td>\n", "      <td>METHOD</td>\n", "      <td>DECLARES</td>\n", "      <td>UnifiedBolt\\core\\model\\BuildFailurePatternMetr...</td>\n", "      <td>UnifiedBolt</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                          source_node source_type  \\\n", "358                                         Buildtool       CLASS   \n", "118                    Buildtoolserviceimplemantation       CLASS   \n", "42                                Buildtoolcontroller       CLASS   \n", "352                                         Buildtool       CLASS   \n", "451                       Configurationtoolinfometric       CLASS   \n", "653                        Githubactionimplementation       CLASS   \n", "195  Buildfailurepatternforprojectinjenkinsmodel.Java        FILE   \n", "387                                          Setvalue      METHOD   \n", "120                    Buildtoolserviceimplemantation       CLASS   \n", "218                        Buildfailurepatternmetrics       CLASS   \n", "\n", "                                destination_node destination_type  \\\n", "358                                 Getgroupname           METHOD   \n", "118   Buildtoolserviceimplemantation.Nodataconst         VARIABLE   \n", "42                                  Getbuildtype           METHOD   \n", "352                                  Getjobcount           METHOD   \n", "451                                Getwidgetname           METHOD   \n", "653                                  Lastbuildid         VARIABLE   \n", "195  Buildfailurepatternforprojectinjenkinsmodel            CLASS   \n", "387                               Setvalue.Value         VARIABLE   \n", "120                                       Search           METHOD   \n", "218                            Getpatterndefined           METHOD   \n", "\n", "          relationship                                          file_path  \\\n", "358           DECLARES              UnifiedBolt\\core\\model\\BuildTool.java   \n", "118          HAS_FIELD  ServiceBolt\\service\\BuildToolServiceImplemanta...   \n", "42            DECLARES           ServiceBolt\\api\\BuildToolController.java   \n", "352           DECLARES              UnifiedBolt\\core\\model\\BuildTool.java   \n", "451           DECLARES  UnifiedBolt\\core\\model\\ConfigurationToolInfoMe...   \n", "653          HAS_FIELD  UnifiedBolt\\githubaction\\GithubActionImplement...   \n", "195           DECLARES  UnifiedBolt\\core\\model\\BuildFailurePatternForP...   \n", "387  DECLARES_VARIABLE        UnifiedBolt\\core\\model\\BuildToolMetric.java   \n", "120           DECLARES  ServiceBolt\\service\\BuildToolServiceImplemanta...   \n", "218           DECLARES  UnifiedBolt\\core\\model\\BuildFailurePatternMetr...   \n", "\n", "     application  \n", "358  UnifiedBolt  \n", "118  ServiceBolt  \n", "42   ServiceBolt  \n", "352  UnifiedBolt  \n", "451  UnifiedBolt  \n", "653  UnifiedBolt  \n", "195  UnifiedBolt  \n", "387  UnifiedBolt  \n", "120  ServiceBolt  \n", "218  UnifiedBolt  "]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["# FINAL: COMBINE ONLY STAGE 1 AND STAGE 3 FOR NEO4J\n", "stage1_rels = df_stage1.to_dict('records')\n", "stage3_rels = df_stage3.to_dict('records') if len(df_stage3) > 0 else []\n", "\n", "# Combine only Stage 1 and Stage 3\n", "final_relationships = stage1_rels + stage3_rels\n", "df_final = pd.DataFrame(final_relationships)\n", "df_final = normalize_node_types(df_final)\n", "\n", "# Remove duplicates\n", "df_final = df_final.drop_duplicates(subset=['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship'])\n", "\n", "# Additional normalization\n", "df_final['source_node'] = df_final['source_node'].str.strip()\n", "df_final['destination_node'] = df_final['destination_node'].str.strip()\n", "\n", "# Save final final data (Stage 1 + Stage 3 only)\n", "df_final.to_csv('final_final_v10.csv', index=False)\n", "\n", "print(f\"Final: {len(df_final)} unique relationships (Stage 1 + Stage 3 only)\")\n", "print(f\"Node types: {sorted(df_final['destination_type'].unique())}\")\n", "print(f\"Relationship types: {sorted(df_final['relationship'].unique())}\")\n", "df_final.sample(10)"]}, {"cell_type": "code", "execution_count": 67, "id": "neo4j_push", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🧹 Clearing existing Neo4j data...\n", "📤 Pushing relationships to Neo4j...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Creating nodes: 100%|██████████| 433/433 [00:01<00:00, 273.27it/s]\n", "Creating relationships: 100%|██████████| 456/456 [00:01<00:00, 228.59it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "✅ Neo4j push complete!\n", "📊 Created 433 nodes and 463 relationships\n", "🌐 Access Neo4j Browser at: http://localhost:7474\n", "🔍 Database: final-v10\n", "\n", "🎉 PIPELINE COMPLETE!\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# NEO4J PUSH\n", "print(\"🧹 Clearing existing Neo4j data...\")\n", "graph.query(\"MATCH (n) DETACH DELETE n\")\n", "\n", "print(\"📤 Pushing relationships to Neo4j...\")\n", "\n", "# Create unique nodes first\n", "nodes = set()\n", "for _, row in df_final.iterrows():\n", "    nodes.add((row['source_node'], row['source_type'], row.get('application', 'Unknown')))\n", "    nodes.add((row['destination_node'], row['destination_type'], row.get('application', 'Unknown')))\n", "\n", "# Create nodes\n", "for node_id, node_type, app in tqdm(nodes, desc=\"Creating nodes\"):\n", "    query = f\"\"\"\n", "    MERGE (n:{node_type} {{id: $node_id, application: $app}})\n", "    SET n.name = $node_id\n", "    \"\"\"\n", "    graph.query(query, {\"node_id\": node_id, \"app\": app})\n", "\n", "# Create relationships\n", "for _, row in tqdm(df_final.iterrows(), desc=\"Creating relationships\", total=len(df_final)):\n", "    query = f\"\"\"\n", "    MATCH (source:{row['source_type']} {{id: $source_id}})\n", "    MATCH (target:{row['destination_type']} {{id: $target_id}})\n", "    MERGE (source)-[r:{row['relationship']}]->(target)\n", "    SET r.file_path = $file_path, r.application = $application\n", "    \"\"\"\n", "    graph.query(query, {\n", "        \"source_id\": row['source_node'],\n", "        \"target_id\": row['destination_node'],\n", "        \"file_path\": row.get('file_path', ''),\n", "        \"application\": row.get('application', 'Unknown')\n", "    })\n", "\n", "# Verify data\n", "node_count = graph.query(\"MATCH (n) RETURN count(n) as count\")[0]['count']\n", "rel_count = graph.query(\"MATCH ()-[r]->() RETURN count(r) as count\")[0]['count']\n", "\n", "print(f\"\\n✅ Neo4j push complete!\")\n", "print(f\"📊 Created {node_count} nodes and {rel_count} relationships\")\n", "print(f\"🌐 Access Neo4j Browser at: http://localhost:7474\")\n", "print(f\"🔍 Database: {NEO4J_DB}\")\n", "print(f\"\\n🎉 PIPELINE COMPLETE!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}