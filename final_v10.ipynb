import os
import re
import json
from pathlib import Path
from tqdm import tqdm
import pandas as pd
from collections import defaultdict

from tree_sitter import Language, Parser
import tree_sitter_java as tsjava

from langchain_community.document_loaders import TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_experimental.graph_transformers import LLMGraphTransformer
from langchain_community.graphs import Neo4jGraph
from langchain_openai import AzureChatOpenAI

BASE_PATH = Path(r"C:/Shaik/sample/OneInsights")
NEO4J_URI = "bolt://localhost:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "Test@7889"
NEO4J_DB = "final-v10"

graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)
JAVA_LANGUAGE = Language(tsjava.language())
parser = Parser(JAVA_LANGUAGE)

llm = AzureChatOpenAI(
    api_key="********************************",
    azure_endpoint="https://azureopenaibrsc.openai.azure.com/",
    azure_deployment="gpt-4o",
    api_version="2024-12-01-preview"
)

all_relationships = []
class_registry = {}
application_mapping = {}

print("✅ Setup complete")

def is_temp_variable(var_name):
    if not var_name or len(var_name) == 0:
        return True
    
    temp_patterns = [
        r'^[ijklmnpqr]$', r'^temp\w*$', r'^tmp\w*$', r'^_\w*$',
        r'^\w*temp$', r'^\w*tmp$', r'^counter\d*$', r'^index\d*$',
        r'^idx\d*$', r'^\$\w*$', r'^this$', r'^super$'
    ]
    
    for pattern in temp_patterns:
        if re.match(pattern, var_name, re.IGNORECASE):
            return True
    return False

def format_variable_name(method_name, var_name):
    if not method_name or not var_name:
        return var_name
    return f"{method_name}.{var_name}"

def get_application_from_path(file_path):
    try:
        rel_path = os.path.relpath(file_path, BASE_PATH)
        path_parts = rel_path.split(os.sep)
        if len(path_parts) > 0:
            return path_parts[0]
    except:
        pass
    return "Unknown"

def clean_node_name(name):
    if not name:
        return name
    prefixes = ['method:', 'class:', 'variable:', 'field:']
    for prefix in prefixes:
        if name.lower().startswith(prefix):
            name = name[len(prefix):]
    return re.sub(r'\.(java|class)$', '', name, flags=re.IGNORECASE).strip()

def normalize_node_types(df):
    type_mapping = {
        'Class': 'CLASS', 'class': 'CLASS', 'Method': 'METHOD', 'method': 'METHOD',
        'Variable': 'VARIABLE', 'variable': 'VARIABLE', 'File': 'FILE', 'file': 'FILE',
        'Interface': 'INTERFACE', 'interface': 'INTERFACE', 'Folder': 'FOLDERS', 
        'folder': 'FOLDERS', 'Folders': 'FOLDERS', 'Project': 'PROJECT', 'project': 'PROJECT',
        'Application': 'APPLICATIONS', 'application': 'APPLICATIONS', 'Applications': 'APPLICATIONS'
    }
    df['source_type'] = df['source_type'].replace(type_mapping)
    df['destination_type'] = df['destination_type'].replace(type_mapping)
    return df

print("✅ Utilities loaded")

# STAGE 1: PROJECT HIERARCHY
all_relationships = []

def extract_project_hierarchy():
    project_name = BASE_PATH.name
    applications = set()
    
    all_relationships.append({
        'source_node': 'OneInsight',
        'source_type': 'PROJECT',
        'destination_node': project_name,
        'destination_type': 'PROJECT',
        'relationship': 'CONTAINS',
        'file_path': None,
        'application': 'OneInsight'
    })
    
    for root, dirs, files in os.walk(BASE_PATH):
        rel_root = os.path.relpath(root, BASE_PATH)
        
        if rel_root == '.':
            for d in dirs:
                applications.add(d)
                all_relationships.append({
                    'source_node': project_name,
                    'source_type': 'PROJECT',
                    'destination_node': d,
                    'destination_type': 'APPLICATIONS',
                    'relationship': 'CONTAINS',
                    'file_path': None,
                    'application': d
                })
        
        path_parts = rel_root.split(os.sep) if rel_root != '.' else []
        if len(path_parts) > 0:
            current_app = path_parts[0] if path_parts[0] in applications else 'Unknown'
            parent = path_parts[0] if len(path_parts) == 1 else os.path.dirname(rel_root)
            current = os.path.basename(rel_root)
            
            parent_type = 'APPLICATIONS' if parent in applications else 'FOLDERS'
            
            all_relationships.append({
                'source_node': parent,
                'source_type': parent_type,
                'destination_node': current,
                'destination_type': 'FOLDERS',
                'relationship': 'CONTAINS',
                'file_path': None,
                'application': current_app
            })
        
        current_folder = os.path.basename(root) if rel_root != '.' else project_name
        current_app = get_application_from_path(root)
        
        for file in files:
            if file.endswith('.java'):
                file_rel_path = os.path.relpath(os.path.join(root, file), BASE_PATH)
                application_mapping[file_rel_path] = current_app
                
                all_relationships.append({
                    'source_node': current_folder,
                    'source_type': 'FOLDERS' if rel_root != '.' else 'PROJECT',
                    'destination_node': file,
                    'destination_type': 'FILE',
                    'relationship': 'CONTAINS',
                    'file_path': file_rel_path,
                    'application': current_app
                })

extract_project_hierarchy()
df_stage1 = pd.DataFrame(all_relationships)
df_stage1 = normalize_node_types(df_stage1)
df_stage1.to_csv('stage1_hierarchy.csv', index=False)
print(f"Stage 1: {len(df_stage1)} relationships")
df_stage1.head()

def extract_package_and_imports(source_code_str):
    package_pattern = r'package\s+([\w\.]+);'
    import_pattern = r'import\s+([\w\.]+);'
    package_match = re.search(package_pattern, source_code_str)
    package_name = package_match.group(1) if package_match else None
    import_matches = re.findall(import_pattern, source_code_str)
    return package_name, import_matches

def extract_api_endpoints(source_code_str):
    endpoints = []
    mapping_patterns = {
        'RequestMapping': [r'@RequestMapping\s*\(\s*value\s*=\s*["\']([^"\']+)["\']', r'@RequestMapping\s*\(\s*["\']([^"\']+)["\']'],
        'GetMapping': [r'@GetMapping\s*\(\s*["\']([^"\']+)["\']'],
        'PostMapping': [r'@PostMapping\s*\(\s*["\']([^"\']+)["\']'],
        'PutMapping': [r'@PutMapping\s*\(\s*["\']([^"\']+)["\']'],
        'DeleteMapping': [r'@DeleteMapping\s*\(\s*["\']([^"\']+)["\']']
    }
    
    for mapping_type, patterns in mapping_patterns.items():
        for pattern in patterns:
            matches = re.findall(pattern, source_code_str, re.MULTILINE | re.DOTALL)
            for match in matches:
                if match.strip():
                    endpoints.append({
                        'type': mapping_type,
                        'path': match.strip(),
                        'method': mapping_type.replace('Mapping', '').upper() if mapping_type != 'RequestMapping' else 'GET'
                    })
    return endpoints

def extract_database_entities(source_code_str):
    entities = []
    entity_patterns = [r'@Entity\s*(?:\([^)]*\))?', r'@Table\s*\(\s*name\s*=\s*["\']([^"\']+)["\']']
    
    for pattern in entity_patterns:
        if re.search(pattern, source_code_str, re.MULTILINE):
            table_matches = re.findall(r'@Table\s*\(\s*name\s*=\s*["\']([^"\']+)["\']', source_code_str)
            for table_name in table_matches:
                if table_name.strip():
                    entities.append({'type': 'DATA', 'name': table_name.strip()})
            
            if not table_matches:
                class_match = re.search(r'public\s+class\s+(\w+)', source_code_str)
                if class_match:
                    class_name = class_match.group(1)
                    table_name = re.sub('([a-z0-9])([A-Z])', r'\1_\2', class_name).lower()
                    entities.append({'type': 'DATA', 'name': table_name})
    return entities

def extract_class_relationships(source_code_str):
    relationships = []
    
    # Class extends
    class_extends_pattern = r'class\s+(\w+)\s+extends\s+([\w<>]+)'
    class_matches = re.findall(class_extends_pattern, source_code_str)
    for child_class, parent_class in class_matches:
        parent_class = re.sub(r'<.*?>', '', parent_class).strip()
        if parent_class:
            relationships.append({'child': child_class, 'parent': parent_class, 'type': 'EXTENDS'})
    
    # Class implements
    implements_pattern = r'class\s+(\w+)(?:\s+extends\s+\w+)?\s+implements\s+([\w<>,\s]+)'
    impl_matches = re.findall(implements_pattern, source_code_str)
    for class_name, implements_clause in impl_matches:
        interfaces = [part.strip().split('<')[0].strip() for part in implements_clause.split(',')]
        for interface in interfaces:
            if interface:
                relationships.append({'child': class_name, 'parent': interface, 'type': 'IMPLEMENTS'})
    
    return relationships

print("✅ Stage 2 functions loaded")

# STAGE 2: CLASS REGISTRY & AST EXTRACTION
def build_class_registry():
    global class_registry
    
    for root, _, files in os.walk(BASE_PATH):
        for file in files:
            if file.endswith('.java'):
                file_path = os.path.join(root, file)
                application = get_application_from_path(file_path)
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        source_code_str = f.read()
                    
                    class_name = os.path.splitext(file)[0]
                    package_name, imports = extract_package_and_imports(source_code_str)
                    fqcn = f"{package_name}.{class_name}" if package_name else class_name
                    
                    endpoints = extract_api_endpoints(source_code_str)
                    db_entities = extract_database_entities(source_code_str)
                    class_relationships = extract_class_relationships(source_code_str)
                    
                    class_registry[fqcn] = {
                        'fqcn': fqcn, 'class_name': class_name, 'package': package_name,
                        'file_path': file_path, 'application': application, 'imports': imports,
                        'endpoints': endpoints, 'db_entities': db_entities, 'class_relationships': class_relationships
                    }
                    
                    # Add relationships
                    if endpoints:
                        for ep in endpoints:
                            all_relationships.append({
                                'source_node': class_name, 'source_type': 'CLASS',
                                'destination_node': f"{ep['method']} {ep['path']}", 'destination_type': 'ENDPOINT',
                                'relationship': 'EXPOSES', 'file_path': os.path.relpath(file_path, BASE_PATH),
                                'application': application
                            })
                    
                    if db_entities:
                        for entity in db_entities:
                            all_relationships.append({
                                'source_node': class_name, 'source_type': 'CLASS',
                                'destination_node': entity['name'], 'destination_type': 'DATA',
                                'relationship': 'DATA_FIND', 'file_path': os.path.relpath(file_path, BASE_PATH),
                                'application': application
                            })
                    
                    if class_relationships:
                        for rel in class_relationships:
                            all_relationships.append({
                                'source_node': rel['child'], 'source_type': 'CLASS',
                                'destination_node': rel['parent'], 
                                'destination_type': 'INTERFACE' if rel['type'] == 'IMPLEMENTS' else 'CLASS',
                                'relationship': rel['type'], 'file_path': os.path.relpath(file_path, BASE_PATH),
                                'application': application
                            })
                
                except Exception as e:
                    print(f"❌ Error processing {file_path}: {e}")
                    continue
    
    return class_registry

def read_source_code(file_path):
    with open(file_path, 'rb') as f:
        return f.read()

def extract_ast_structure(file_path):
    records = []
    source_code = read_source_code(file_path)
    tree = parser.parse(source_code)
    root_node = tree.root_node
    file_name = os.path.basename(file_path)
    application = get_application_from_path(file_path)

    def traverse(node, parent_type=None, parent_name=None, method_context=None):
        if node.type == 'class_declaration':
            class_name = None
            for child in node.children:
                if child.type == 'identifier':
                    class_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    records.append({
                        'source_node': file_name, 'source_type': 'FILE',
                        'destination_node': class_name, 'destination_type': 'CLASS',
                        'relationship': 'DECLARES', 'file_path': os.path.relpath(file_path, BASE_PATH),
                        'application': application
                    })
                    break
            for child in node.children:
                traverse(child, 'class', class_name, None)

        elif node.type == 'interface_declaration':
            interface_name = None
            for child in node.children:
                if child.type == 'identifier':
                    interface_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    records.append({
                        'source_node': file_name, 'source_type': 'FILE',
                        'destination_node': interface_name, 'destination_type': 'INTERFACE',
                        'relationship': 'DECLARES', 'file_path': os.path.relpath(file_path, BASE_PATH),
                        'application': application
                    })
                    break
            for child in node.children:
                traverse(child, 'interface', interface_name, None)

        elif node.type == 'method_declaration':
            method_name = None
            for child in node.children:
                if child.type == 'identifier':
                    method_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    if parent_name and parent_type in ['class', 'interface']:
                        records.append({
                            'source_node': parent_name, 'source_type': parent_type.upper(),
                            'destination_node': method_name, 'destination_type': 'METHOD',
                            'relationship': 'DECLARES', 'file_path': os.path.relpath(file_path, BASE_PATH),
                            'application': application
                        })
                    break
            for child in node.children:
                traverse(child, 'method', method_name, method_name)

        elif node.type == 'field_declaration':
            for child in node.children:
                if child.type == 'variable_declarator':
                    for grandchild in child.children:
                        if grandchild.type == 'identifier':
                            field_name = clean_node_name(source_code[grandchild.start_byte:grandchild.end_byte].decode('utf-8'))
                            if parent_name and parent_type == 'class' and not is_temp_variable(field_name):
                                formatted_var_name = format_variable_name(parent_name, field_name)
                                records.append({
                                    'source_node': parent_name, 'source_type': 'CLASS',
                                    'destination_node': formatted_var_name, 'destination_type': 'VARIABLE',
                                    'relationship': 'HAS_FIELD', 'file_path': os.path.relpath(file_path, BASE_PATH),
                                    'application': application
                                })

        elif node.type in ['assignment_expression', 'variable_declarator'] and parent_type == 'method':
            for child in node.children:
                if child.type == 'identifier':
                    var_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    if var_name and var_name != 'this' and not is_temp_variable(var_name) and method_context:
                        formatted_var_name = format_variable_name(method_context, var_name)
                        records.append({
                            'source_node': parent_name, 'source_type': 'METHOD',
                            'destination_node': formatted_var_name, 'destination_type': 'VARIABLE',
                            'relationship': 'DECLARES_VARIABLE', 'file_path': os.path.relpath(file_path, BASE_PATH),
                            'application': application
                        })

        for child in node.children:
            traverse(child, parent_type, parent_name, method_context)

    traverse(root_node)
    return records

# Execute Stage 2
build_class_registry()
ast_records = []
for root, _, files in os.walk(BASE_PATH):
    for file in files:
        if file.endswith('.java'):
            file_path = os.path.join(root, file)
            try:
                ast_records.extend(extract_ast_structure(file_path))
            except Exception as e:
                print(f'[Error] Failed to parse {file}: {e}')

all_relationships.extend(ast_records)
df_stage2 = pd.DataFrame(all_relationships)
df_stage2 = normalize_node_types(df_stage2)
df_stage2.to_csv('stage2_class_ast.csv', index=False)
print(f"Stage 2: {len(df_stage2)} total relationships")
df_stage2.tail()

def build_enhanced_system_prompt(file_path, ast_context, class_registry):
    application = get_application_from_path(file_path)
    
    ast_context_str = "\n".join([
        f"- {item['source_type']} '{item['source_node']}' {item['relationship']} {item['destination_type']} '{item['destination_node']}'"
        for item in ast_context[:20]
    ])
    
    # Use class registry to get additional context
    class_info = ""
    file_name = os.path.basename(file_path).replace('.java', '')
    for fqcn, info in class_registry.items():
        if info['class_name'] == file_name:
            if info.get('endpoints'):
                class_info += f"\nEndpoints: {[ep['path'] for ep in info['endpoints']]}"
            if info.get('db_entities'):
                class_info += f"\nDB Entities: {[e['name'] for e in info['db_entities']]}"
            break
    
    return f"""
You are analyzing Java code for lineage extraction in application: {application}.

CONTEXT FROM AST ANALYSIS:
{ast_context_str}

CLASS REGISTRY CONTEXT:
{class_info}

CRITICAL RULES - FOLLOW EXACTLY:
1. Use SIMPLE names only (remove prefixes like "method:", "class:", etc.)
2. MANDATORY NODE TYPES: PROJECT, APPLICATIONS, FOLDERS, FILE, CLASS, METHOD, INTERFACE, VARIABLE, ENDPOINT, DATA, OPERATION, VALUE
3. MANDATORY RELATIONSHIPS: CONTAINS, DATA_FIND, DECLARES, DECLARES_VARIABLE, EXPOSES, EXTENDS, HAS_FIELD, IMPLEMENTS, TRANSFORMS_TO, USES, ASSIGNS_VALUE, UPDATED_BY
4. VARIABLE NAMING: Format as method_name.variable_name (e.g., "getUserData.userId")
5. FILTER OUT: Loop counters (i, j, k), temp variables, variables starting with _ or $
6. RELATIONSHIP DIRECTIONS (DO NOT REVERSE):
   - FILE -[DECLARES]-> CLASS
   - CLASS -[DECLARES]-> METHOD  
   - CLASS -[HAS_FIELD]-> VARIABLE
   - METHOD -[DECLARES_VARIABLE]-> VARIABLE
   - METHOD -[USES]-> VARIABLE
   - VARIABLE -[TRANSFORMS_TO]-> VARIABLE
   - OPERATION -[ASSIGNS_VALUE]-> VARIABLE
   - VARIABLE -[UPDATED_BY]-> OPERATION
   - CLASS -[EXTENDS]-> CLASS
   - CLASS -[IMPLEMENTS]-> INTERFACE
   - CLASS -[EXPOSES]-> ENDPOINT
   - CLASS -[DATA_FIND]-> DATA

7. FOCUS ON: Variable transformations, method calls, data operations, assignments
8. Application context: {application}

Extract ALL meaningful relationships following these exact patterns.
"""

def get_file_size_lines(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return len(f.readlines())
    except:
        return 0

def extract_class_metadata(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        package_match = re.search(r'package\s+([\w\.]+);', content)
        package = package_match.group(1) if package_match else None
        
        imports = re.findall(r'import\s+([\w\.]+);', content)
        class_names = re.findall(r'(?:public\s+)?(?:class|interface)\s+(\w+)', content)
        
        return {
            'package': package,
            'imports': imports[:10],
            'classes': class_names,
            'file_name': os.path.basename(file_path)
        }
    except:
        return {'package': None, 'imports': [], 'classes': [], 'file_name': os.path.basename(file_path)}

def smart_chunk_by_logical_boundaries(file_path, max_lines=800):
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if len(lines) <= max_lines:
            return [{'content': ''.join(lines), 'start_line': 1, 'end_line': len(lines), 'chunk_id': 1}]
        
        chunks = []
        current_chunk = []
        current_line = 0
        chunk_id = 1
        start_line = 1
        
        brace_count = 0
        in_method = False
        
        for i, line in enumerate(lines):
            current_chunk.append(line)
            current_line += 1
            
            brace_count += line.count('{') - line.count('}')
            
            if re.match(r'\s*(public|private|protected|static).*\{\s*$', line.strip()):
                in_method = True
            
            if (current_line >= max_lines and brace_count == 0 and not in_method) or i == len(lines) - 1:
                chunks.append({
                    'content': ''.join(current_chunk),
                    'start_line': start_line,
                    'end_line': start_line + len(current_chunk) - 1,
                    'chunk_id': chunk_id
                })
                
                current_chunk = []
                current_line = 0
                chunk_id += 1
                start_line = i + 2
                in_method = False
        
        return chunks if chunks else [{'content': ''.join(lines), 'start_line': 1, 'end_line': len(lines), 'chunk_id': 1}]
    
    except Exception as e:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        return [{'content': content, 'start_line': 1, 'end_line': len(content.split('\n')), 'chunk_id': 1}]

def process_chunk_with_llm(chunk_data, file_path, ast_context, class_metadata):
    try:
        from langchain.schema import Document
        doc = Document(page_content=chunk_data['content'])
        
        application = get_application_from_path(file_path)
        
        ast_context_str = "\n".join([
            f"- {item['source_type']} '{item['source_node']}' {item['relationship']} {item['destination_type']} '{item['destination_node']}'"
            for item in ast_context[:15]
        ])
        
        class_info = f"""
Package: {class_metadata.get('package', 'None')}
Classes: {', '.join(class_metadata.get('classes', []))}
Key Imports: {', '.join(class_metadata.get('imports', [])[:5])}
Chunk: {chunk_data['chunk_id']} (Lines {chunk_data['start_line']}-{chunk_data['end_line']})
"""
        
        system_prompt = f"""
You are analyzing Java code chunk for lineage extraction in application: {application}.

CONTEXT FROM AST ANALYSIS:
{ast_context_str}

CLASS METADATA CONTEXT:
{class_info}

CRITICAL RULES - FOLLOW EXACTLY:
1. Use SIMPLE names only (remove prefixes like "method:", "class:", etc.)
2. MANDATORY NODE TYPES: PROJECT, APPLICATIONS, FOLDERS, FILE, CLASS, METHOD, INTERFACE, VARIABLE, ENDPOINT, DATA, OPERATION, VALUE
3. MANDATORY RELATIONSHIPS: CONTAINS, DATA_FIND, DECLARES, DECLARES_VARIABLE, EXPOSES, EXTENDS, HAS_FIELD, IMPLEMENTS, TRANSFORMS_TO, USES, ASSIGNS_VALUE, UPDATED_BY
4. VARIABLE NAMING: Format as method_name.variable_name (e.g., "getUserData.userId")
5. FILTER OUT: Loop counters (i, j, k), temp variables, variables starting with _ or $
6. FOCUS ON: Variable transformations, method calls, data operations, assignments
7. Application context: {application}

Extract ALL meaningful relationships following these exact patterns.
"""

        transformer = LLMGraphTransformer(
            llm=llm,
            additional_instructions=system_prompt,
            allowed_nodes=['PROJECT', 'APPLICATIONS', 'FOLDERS', 'FILE', 'CLASS', 'METHOD', 'INTERFACE', 'VARIABLE', 'ENDPOINT', 'DATA', 'OPERATION', 'VALUE'],
            allowed_relationships=[
                ('PROJECT', 'CONTAINS', 'APPLICATIONS'), ('APPLICATIONS', 'CONTAINS', 'FOLDERS'),
                ('FOLDERS', 'CONTAINS', 'FILE'), ('FILE', 'DECLARES', 'CLASS'), ('FILE', 'DECLARES', 'INTERFACE'),
                ('CLASS', 'DECLARES', 'METHOD'), ('INTERFACE', 'DECLARES', 'METHOD'),
                ('CLASS', 'HAS_FIELD', 'VARIABLE'), ('METHOD', 'DECLARES_VARIABLE', 'VARIABLE'),
                ('METHOD', 'USES', 'VARIABLE'), ('VARIABLE', 'TRANSFORMS_TO', 'VARIABLE'),
                ('OPERATION', 'ASSIGNS_VALUE', 'VARIABLE'), ('VARIABLE', 'UPDATED_BY', 'OPERATION'),
                ('CLASS', 'EXTENDS', 'CLASS'), ('CLASS', 'IMPLEMENTS', 'INTERFACE'),
                ('CLASS', 'EXPOSES', 'ENDPOINT'), ('CLASS', 'DATA_FIND', 'DATA')
            ]
        )
        
        graph_docs = transformer.convert_to_graph_documents([doc])
        
        chunk_relationships = []
        for graph_doc in graph_docs:
            for rel in graph_doc.relationships:
                chunk_relationships.append({
                    'source_node': rel.source.id, 'source_type': rel.source.type,
                    'destination_node': rel.target.id, 'destination_type': rel.target.type,
                    'relationship': rel.type, 'file_path': os.path.relpath(file_path, BASE_PATH),
                    'application': application, 'chunk_id': chunk_data['chunk_id'],
                    'chunk_lines': f"{chunk_data['start_line']}-{chunk_data['end_line']}"
                })
        
        return chunk_relationships
        
    except Exception as e:
        print(f"❌ Error processing chunk {chunk_data['chunk_id']}: {e}")
        return []

def process_file_with_hybrid_approach(file_path, ast_context, max_lines_threshold=1000):
    file_lines = get_file_size_lines(file_path)
    file_name = os.path.basename(file_path)
    
    if file_lines <= max_lines_threshold:
        # Small file: use original approach
        try:
            loader = TextLoader(file_path)
            docs = loader.load()
            
            splitter = RecursiveCharacterTextSplitter.from_language(
                language=LC_Language.JAVA, chunk_size=4000, chunk_overlap=200
            )
            split_docs = splitter.split_documents(docs)
            
            system_prompt = build_enhanced_system_prompt(file_path, ast_context, class_registry)
            application = get_application_from_path(file_path)

            transformer = LLMGraphTransformer(
                llm=llm, additional_instructions=system_prompt,
                allowed_nodes=['PROJECT', 'APPLICATIONS', 'FOLDERS', 'FILE', 'CLASS', 'METHOD', 'INTERFACE', 'VARIABLE', 'ENDPOINT', 'DATA', 'OPERATION', 'VALUE'],
                allowed_relationships=[
                    ('PROJECT', 'CONTAINS', 'APPLICATIONS'), ('APPLICATIONS', 'CONTAINS', 'FOLDERS'),
                    ('FOLDERS', 'CONTAINS', 'FILE'), ('FILE', 'DECLARES', 'CLASS'), ('FILE', 'DECLARES', 'INTERFACE'),
                    ('CLASS', 'DECLARES', 'METHOD'), ('INTERFACE', 'DECLARES', 'METHOD'),
                    ('CLASS', 'HAS_FIELD', 'VARIABLE'), ('METHOD', 'DECLARES_VARIABLE', 'VARIABLE'),
                    ('METHOD', 'USES', 'VARIABLE'), ('VARIABLE', 'TRANSFORMS_TO', 'VARIABLE'),
                    ('OPERATION', 'ASSIGNS_VALUE', 'VARIABLE'), ('VARIABLE', 'UPDATED_BY', 'OPERATION'),
                    ('CLASS', 'EXTENDS', 'CLASS'), ('CLASS', 'IMPLEMENTS', 'INTERFACE'),
                    ('CLASS', 'EXPOSES', 'ENDPOINT'), ('CLASS', 'DATA_FIND', 'DATA')
                ]
            )
            
            graph_docs = transformer.convert_to_graph_documents(split_docs)
            
            file_relationships = []
            for doc in graph_docs:
                for rel in doc.relationships:
                    file_relationships.append({
                        'source_node': rel.source.id, 'source_type': rel.source.type,
                        'destination_node': rel.target.id, 'destination_type': rel.target.type,
                        'relationship': rel.type, 'file_path': os.path.relpath(file_path, BASE_PATH),
                        'application': application
                    })
            
            print(f"✅ Processed {file_name} ({file_lines} lines): {len(file_relationships)} relationships")
            return file_relationships
            
        except Exception as e:
            print(f"❌ Error processing {file_name}: {e}")
            return []
    
    else:
        # Large file: use chunk-based approach
        print(f"📄 Large file detected: {file_name} ({file_lines} lines) - Using chunk-based processing")
        
        class_metadata = extract_class_metadata(file_path)
        chunks = smart_chunk_by_logical_boundaries(file_path)
        
        all_file_relationships = []
        
        for chunk in tqdm(chunks, desc=f"Processing {file_name}", leave=False):
            chunk_rels = process_chunk_with_llm(chunk, file_path, ast_context, class_metadata)
            all_file_relationships.extend(chunk_rels)
            print(f"  ✅ Chunk {chunk['chunk_id']} (lines {chunk['start_line']}-{chunk['end_line']}): {len(chunk_rels)} relationships")
        
        print(f"✅ Completed {file_name}: {len(chunks)} chunks, {len(all_file_relationships)} total relationships")
        return all_file_relationships

print("✅ Stage 3 functions loaded")

# STAGE 3: HYBRID LLM PROCESSING (Smart File/Chunk-based)
llm_relationships = []
java_files = []

for root, _, files in os.walk(BASE_PATH):
    for file in files:
        if file.endswith('.java'):
            java_files.append(os.path.join(root, file))

print(f"Processing {len(java_files)} Java files with Hybrid LLM approach...")
print(f"📊 File size analysis:")

# Analyze file sizes first
small_files = []
large_files = []
for file_path in java_files:
    file_lines = get_file_size_lines(file_path)
    if file_lines > 1000:
        large_files.append((file_path, file_lines))
    else:
        small_files.append((file_path, file_lines))

print(f"  📄 Small files (<1000 lines): {len(small_files)}")
print(f"  📚 Large files (>1000 lines): {len(large_files)}")
if large_files:
    print(f"  📈 Largest file: {max(large_files, key=lambda x: x[1])[1]} lines")

# Process all files with hybrid approach
total_chunks_processed = 0
for file_path in tqdm(java_files, desc="Processing files"):
    file_ast_context = [rel for rel in ast_records if rel.get('file_path') == os.path.relpath(file_path, BASE_PATH)]
    file_llm_rels = process_file_with_hybrid_approach(file_path, file_ast_context)
    llm_relationships.extend(file_llm_rels)
    
    # Count chunks if file was chunked
    chunk_count = len([rel for rel in file_llm_rels if 'chunk_id' in rel])
    if chunk_count > 0:
        unique_chunks = len(set([rel['chunk_id'] for rel in file_llm_rels if 'chunk_id' in rel]))
        total_chunks_processed += unique_chunks

all_relationships.extend(llm_relationships)
df_stage3 = pd.DataFrame(llm_relationships)
df_stage3 = normalize_node_types(df_stage3)
df_stage3.to_csv('stage3_llm.csv', index=False)

print(f"\n📊 Stage 3 Complete:")
print(f"  • Total relationships: {len(df_stage3)}")
print(f"  • Files processed: {len(java_files)}")
print(f"  • Chunks processed: {total_chunks_processed}")
print(f"  • Large files handled: {len(large_files)}")
df_stage3.head()

# FINAL: COMBINE ONLY STAGE 1 AND STAGE 3 FOR NEO4J
stage1_rels = df_stage1.to_dict('records')
stage3_rels = df_stage3.to_dict('records') if len(df_stage3) > 0 else []

# Combine only Stage 1 and Stage 3
final_relationships = stage1_rels + stage3_rels
df_final = pd.DataFrame(final_relationships)
df_final = normalize_node_types(df_final)

# Remove duplicates
df_final = df_final.drop_duplicates(subset=['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship'])

# Additional normalization
df_final['source_node'] = df_final['source_node'].str.strip()
df_final['destination_node'] = df_final['destination_node'].str.strip()

# Save final combined data (Stage 1 + Stage 3 only)
df_final.to_csv('final_combined_v10.csv', index=False)

print(f"Final: {len(df_final)} unique relationships (Stage 1 + Stage 3 only)")
print(f"Applications: {df_final['application'].nunique()}")
print(f"Node types: {sorted(df_final['destination_type'].unique())}")
print(f"Relationship types: {sorted(df_final['relationship'].unique())}")
df_final.sample(10)

# NEO4J PUSH
print("🧹 Clearing existing Neo4j data...")
graph.query("MATCH (n) DETACH DELETE n")

print("📤 Pushing relationships to Neo4j...")

# Create unique nodes first
nodes = set()
for _, row in df_final.iterrows():
    nodes.add((row['source_node'], row['source_type'], row.get('application', 'Unknown')))
    nodes.add((row['destination_node'], row['destination_type'], row.get('application', 'Unknown')))

# Create nodes
for node_id, node_type, app in tqdm(nodes, desc="Creating nodes"):
    query = f"""
    MERGE (n:{node_type} {{id: $node_id, application: $app}})
    SET n.name = $node_id
    """
    graph.query(query, {"node_id": node_id, "app": app})

# Create relationships
for _, row in tqdm(df_final.iterrows(), desc="Creating relationships", total=len(df_final)):
    query = f"""
    MATCH (source:{row['source_type']} {{id: $source_id}})
    MATCH (target:{row['destination_type']} {{id: $target_id}})
    MERGE (source)-[r:{row['relationship']}]->(target)
    SET r.file_path = $file_path, r.application = $application
    """
    graph.query(query, {
        "source_id": row['source_node'],
        "target_id": row['destination_node'],
        "file_path": row.get('file_path', ''),
        "application": row.get('application', 'Unknown')
    })

# Verify data
node_count = graph.query("MATCH (n) RETURN count(n) as count")[0]['count']
rel_count = graph.query("MATCH ()-[r]->() RETURN count(r) as count")[0]['count']

print(f"\n✅ Neo4j push complete!")
print(f"📊 Created {node_count} nodes and {rel_count} relationships")
print(f"🌐 Access Neo4j Browser at: http://localhost:7474")
print(f"🔍 Database: {NEO4J_DB}")
print(f"\n🎉 PIPELINE COMPLETE!")

df_sateg1 = pd.read_csv('stage1_hierarchy.csv')

df_stage1

def fix_stage1_data_normalization(df_stage1):
    """Fix data normalization issues in Stage 1 hierarchy"""
    df_fixed = df_stage1.copy()
    
    # Fix 1: Remove duplicate PROJECT->PROJECT relationship
    # Keep only the main project, remove the duplicate OneInsight->OneInsights
    df_fixed = df_fixed[~((df_fixed['source_node'] == 'OneInsight') & 
                         (df_fixed['destination_node'] == 'OneInsights') & 
                         (df_fixed['source_type'] == 'PROJECT') & 
                         (df_fixed['destination_type'] == 'PROJECT'))]
    
    # Fix 2: Remove APPLICATION->FOLDER relationships where source and destination are the same
    # Remove ServiceBolt->ServiceBolt and UnifiedBolt->UnifiedBolt
    df_fixed = df_fixed[~((df_fixed['source_node'] == df_fixed['destination_node']) & 
                         (df_fixed['source_type'] == 'APPLICATIONS') & 
                         (df_fixed['destination_type'] == 'FOLDERS'))]
    
    # Fix 3: Clean folder names - remove path prefixes, keep only the folder name
    def clean_folder_name(name):
        if pd.isna(name) or name is None:
            return name
        # Remove backslash paths, keep only the last part
        if '\\' in str(name):
            return str(name).split('\\')[-1]
        return name
    
    # Apply cleaning to source_node where source_type is FOLDERS
    mask_folders = df_fixed['source_type'] == 'FOLDERS'
    df_fixed.loc[mask_folders, 'source_node'] = df_fixed.loc[mask_folders, 'source_node'].apply(clean_folder_name)
    
    # Fix 4: Add missing intermediate folder connections
    # For nested paths like UnifiedBolt\core\model, we need UnifiedBolt->core->model chain
    missing_connections = []
    
    for idx, row in df_fixed.iterrows():
        if row['file_path'] and '\\' in row['file_path']:
            path_parts = row['file_path'].split('\\')
            application = path_parts[0]
            
            # Create chain: application -> folder1 -> folder2 -> ... -> file
            for i in range(1, len(path_parts) - 1):  # Skip application and file
                parent = path_parts[i-1] if i > 1 else application
                current = path_parts[i]
                
                parent_type = 'APPLICATIONS' if i == 1 else 'FOLDERS'
                
                # Check if this connection already exists
                connection_exists = any(
                    (df_fixed['source_node'] == parent) & 
                    (df_fixed['destination_node'] == current) & 
                    (df_fixed['source_type'] == parent_type) & 
                    (df_fixed['destination_type'] == 'FOLDERS') & 
                    (df_fixed['relationship'] == 'CONTAINS')
                )
                
                if not connection_exists:
                    missing_connections.append({
                        'source_node': parent,
                        'source_type': parent_type,
                        'destination_node': current,
                        'destination_type': 'FOLDERS',
                        'relationship': 'CONTAINS',
                        'file_path': None,
                        'application': application
                    })
    
    # Add missing connections
    if missing_connections:
        missing_df = pd.DataFrame(missing_connections)
        df_fixed = pd.concat([df_fixed, missing_df], ignore_index=True)
    
    # Fix 5: Ensure proper file connections
    # Make sure each file is connected to its immediate parent folder
    for idx, row in df_fixed.iterrows():
        if row['destination_type'] == 'FILE' and row['file_path']:
            path_parts = row['file_path'].split('\\')
            if len(path_parts) > 1:
                immediate_parent = path_parts[-2]  # Folder containing the file
                # Update the source_node to be the immediate parent folder
                df_fixed.at[idx, 'source_node'] = immediate_parent
                df_fixed.at[idx, 'source_type'] = 'FOLDERS'
    
    # Remove duplicates that might have been created
    df_fixed = df_fixed.drop_duplicates(subset=['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship'])
    
    # Sort for better readability
    df_fixed = df_fixed.sort_values(['application', 'source_type', 'destination_type']).reset_index(drop=True)
    
    return df_fixed

# Apply the fix
df_stage1_fixed = fix_stage1_data_normalization(df_stage1)

# Verify the fixes
print("🔧 Stage 1 Data Normalization Complete!")
print(f"Original rows: {len(df_stage1)}")
print(f"Fixed rows: {len(df_stage1_fixed)}")
print("\n📊 Fixed hierarchy sample:")
print(df_stage1_fixed[['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship', 'application']].head(15))

# Save the fixed version
df_stage1_fixed.to_csv('stage1_hierarchy_fixed.csv', index=False)
print("\n✅ Saved fixed data to 'stage1_hierarchy_fixed.csv'")

df_stage1_fixed 

# FINAL: COMBINE ONLY STAGE 1 AND STAGE 3 FOR NEO4J
stage1_rels = df_stage1_fixed.to_dict('records')
stage3_rels = df_stage3.to_dict('records') if len(df_stage3) > 0 else []

# Combine only Stage 1 and Stage 3
final_relationships = stage1_rels + stage3_rels
df_final = pd.DataFrame(final_relationships)
df_final = normalize_node_types(df_final)

# Remove duplicates
df_final = df_final.drop_duplicates(subset=['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship'])

# Additional normalization
df_final['source_node'] = df_final['source_node'].str.strip()
df_final['destination_node'] = df_final['destination_node'].str.strip()

# Save final combined data (Stage 1 + Stage 3 only)
df_final.to_csv('final_combined_v10.csv', index=False)

print(f"Final: {len(df_final)} unique relationships (Stage 1 + Stage 3 only)")
print(f"Applications: {df_final['application'].nunique()}")
print(f"Node types: {sorted(df_final['destination_type'].unique())}")
print(f"Relationship types: {sorted(df_final['relationship'].unique())}")
df_final.sample(10)

# NEO4J PUSH
print("🧹 Clearing existing Neo4j data...")
graph.query("MATCH (n) DETACH DELETE n")

print("📤 Pushing relationships to Neo4j...")

# Create unique nodes first
nodes = set()
for _, row in df_final.iterrows():
    nodes.add((row['source_node'], row['source_type'], row.get('application', 'Unknown')))
    nodes.add((row['destination_node'], row['destination_type'], row.get('application', 'Unknown')))

# Create nodes
for node_id, node_type, app in tqdm(nodes, desc="Creating nodes"):
    query = f"""
    MERGE (n:{node_type} {{id: $node_id, application: $app}})
    SET n.name = $node_id
    """
    graph.query(query, {"node_id": node_id, "app": app})

# Create relationships
for _, row in tqdm(df_final.iterrows(), desc="Creating relationships", total=len(df_final)):
    query = f"""
    MATCH (source:{row['source_type']} {{id: $source_id}})
    MATCH (target:{row['destination_type']} {{id: $target_id}})
    MERGE (source)-[r:{row['relationship']}]->(target)
    SET r.file_path = $file_path, r.application = $application
    """
    graph.query(query, {
        "source_id": row['source_node'],
        "target_id": row['destination_node'],
        "file_path": row.get('file_path', ''),
        "application": row.get('application', 'Unknown')
    })

# Verify data
node_count = graph.query("MATCH (n) RETURN count(n) as count")[0]['count']
rel_count = graph.query("MATCH ()-[r]->() RETURN count(r) as count")[0]['count']

print(f"\n✅ Neo4j push complete!")
print(f"📊 Created {node_count} nodes and {rel_count} relationships")
print(f"🌐 Access Neo4j Browser at: http://localhost:7474")
print(f"🔍 Database: {NEO4J_DB}")
print(f"\n🎉 PIPELINE COMPLETE!")