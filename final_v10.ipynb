import os
import re
import json
from pathlib import Path
from tqdm import tqdm
import pandas as pd
from collections import defaultdict

from tree_sitter import Language, Parser
import tree_sitter_java as tsjava

from langchain_community.document_loaders import TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_experimental.graph_transformers import LLMGraphTransformer
from langchain_community.graphs import Neo4jGraph
from langchain_openai import AzureChatOpenAI

# Configuration
BASE_PATH = Path(r"C:/Shaik/sample/OneInsights")
NEO4J_URI = "bolt://localhost:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "Test@7889"
NEO4J_DB = "final-v10"

# Initialize components
graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)
JAVA_LANGUAGE = Language(tsjava.language())
parser = Parser(JAVA_LANGUAGE)

llm = AzureChatOpenAI(
    api_key="********************************",
    azure_endpoint="https://azureopenaibrsc.openai.azure.com/",
    azure_deployment="gpt-4o",
    api_version="2024-12-01-preview"
)

print("✅ Configuration and imports completed successfully")

# Global variables
all_relationships = []
class_registry = {}
application_mapping = {}  # Maps file paths to applications

def is_temp_variable(var_name):
    """Filter out temporary variables and loop counters"""
    if not var_name or len(var_name) == 0:
        return True
    
    # Common loop counters and temporary variables
    temp_patterns = [
        r'^[ijklmnpqr]$',  # Single letter loop counters
        r'^temp\w*$',      # temp, tempVar, etc.
        r'^tmp\w*$',       # tmp, tmpVar, etc.
        r'^_\w*$',         # Variables starting with underscore
        r'^\w*temp$',      # Variables ending with temp
        r'^\w*tmp$',       # Variables ending with tmp
        r'^counter\d*$',   # counter, counter1, etc.
        r'^index\d*$',     # index, index1, etc.
        r'^idx\d*$',       # idx, idx1, etc.
        r'^\$\w*$',        # Variables starting with $
        r'^this$',         # 'this' keyword
        r'^super$',        # 'super' keyword
    ]
    
    for pattern in temp_patterns:
        if re.match(pattern, var_name, re.IGNORECASE):
            return True
    
    return False

def format_variable_name(method_name, var_name):
    """Format variable name as method_name.variable_name"""
    if not method_name or not var_name:
        return var_name
    return f"{method_name}.{var_name}"

def get_application_from_path(file_path):
    """Extract application name from file path"""
    try:
        rel_path = os.path.relpath(file_path, BASE_PATH)
        path_parts = rel_path.split(os.sep)
        if len(path_parts) > 0:
            return path_parts[0]  # First directory is the application
    except:
        pass
    return "Unknown"

def clean_node_name(name):
    """Clean node names by stripping prefixes, suffixes, and file extensions."""
    if not name:
        return name
    prefixes = ['method:', 'class:', 'variable:', 'field:']
    for prefix in prefixes:
        if name.lower().startswith(prefix):
            name = name[len(prefix):]
    return re.sub(r'\.(java|class)$', '', name, flags=re.IGNORECASE).strip()

print("✅ Utility functions loaded")

def extract_project_hierarchy():
    """Extract PROJECT -> APPLICATIONS -> FOLDERS -> FILES hierarchy with application tracking"""
    project_name = BASE_PATH.name
    applications = set()
    
    # Add project node
    all_relationships.append({
        'source_node': 'OneInsight',
        'source_type': 'PROJECT',
        'destination_node': project_name,
        'destination_type': 'PROJECT',
        'relationship': 'CONTAINS',
        'file_path': None,
        'application': 'OneInsight'
    })
    
    for root, dirs, files in os.walk(BASE_PATH):
        rel_root = os.path.relpath(root, BASE_PATH)
        
        # Identify applications (top-level directories)
        if rel_root == '.':
            for d in dirs:
                applications.add(d)
                all_relationships.append({
                    'source_node': project_name,
                    'source_type': 'PROJECT',
                    'destination_node': d,
                    'destination_type': 'APPLICATIONS',
                    'relationship': 'CONTAINS',
                    'file_path': None,
                    'application': d
                })
        
        # Handle folder hierarchy
        path_parts = rel_root.split(os.sep) if rel_root != '.' else []
        if len(path_parts) > 0:
            current_app = path_parts[0] if path_parts[0] in applications else 'Unknown'
            parent = path_parts[0] if len(path_parts) == 1 else os.path.dirname(rel_root)
            current = os.path.basename(rel_root)
            
            parent_type = 'APPLICATIONS' if parent in applications else 'FOLDERS'
            
            all_relationships.append({
                'source_node': parent,
                'source_type': parent_type,
                'destination_node': current,
                'destination_type': 'FOLDERS',
                'relationship': 'CONTAINS',
                'file_path': None,
                'application': current_app
            })
        
        # Handle files
        current_folder = os.path.basename(root) if rel_root != '.' else project_name
        current_app = get_application_from_path(root)
        
        for file in files:
            if file.endswith('.java'):
                file_rel_path = os.path.relpath(os.path.join(root, file), BASE_PATH)
                application_mapping[file_rel_path] = current_app
                
                all_relationships.append({
                    'source_node': current_folder,
                    'source_type': 'FOLDERS' if rel_root != '.' else 'PROJECT',
                    'destination_node': file,
                    'destination_type': 'FILE',
                    'relationship': 'CONTAINS',
                    'file_path': file_rel_path,
                    'application': current_app
                })
    
    return len([rel for rel in all_relationships if rel['relationship'] == 'CONTAINS'])

print("✅ Extraction functions loaded")

def extract_package_and_imports(source_code_str):
    """Extract package declaration and imports"""
    package_pattern = r'package\s+([\w\.]+);'
    import_pattern = r'import\s+([\w\.]+);'
    package_match = re.search(package_pattern, source_code_str)
    package_name = package_match.group(1) if package_match else None
    import_matches = re.findall(import_pattern, source_code_str)
    return package_name, import_matches

def extract_api_endpoints(source_code_str):
    """Extract REST API endpoints"""
    endpoints = []
    
    mapping_patterns = {
        'RequestMapping': [
            r'@RequestMapping\s*\(\s*value\s*=\s*["\']([^"\']+)["\']',
            r'@RequestMapping\s*\(\s*["\']([^"\']+)["\']',
            r'@RequestMapping\s*\(\s*path\s*=\s*["\']([^"\']+)["\']'
        ],
        'GetMapping': [
            r'@GetMapping\s*\(\s*["\']([^"\']+)["\']',
            r'@GetMapping\s*\(\s*value\s*=\s*["\']([^"\']+)["\']'
        ],
        'PostMapping': [
            r'@PostMapping\s*\(\s*["\']([^"\']+)["\']',
            r'@PostMapping\s*\(\s*value\s*=\s*["\']([^"\']+)["\']'
        ],
        'PutMapping': [
            r'@PutMapping\s*\(\s*["\']([^"\']+)["\']'
        ],
        'DeleteMapping': [
            r'@DeleteMapping\s*\(\s*["\']([^"\']+)["\']'
        ]
    }
    
    for mapping_type, patterns in mapping_patterns.items():
        for pattern in patterns:
            matches = re.findall(pattern, source_code_str, re.MULTILINE | re.DOTALL)
            for match in matches:
                if match.strip():
                    endpoints.append({
                        'type': mapping_type,
                        'path': match.strip(),
                        'method': mapping_type.replace('Mapping', '').upper() if mapping_type != 'RequestMapping' else 'GET'
                    })
    
    return endpoints

def extract_database_entities(source_code_str):
    """Extract database tables, columns, datasets"""
    entities = []
    
    # Entity/Table detection
    entity_patterns = [
        r'@Entity\s*(?:\([^)]*\))?',
        r'@Table\s*\(\s*name\s*=\s*["\']([^"\']+)["\']'
    ]
    
    for pattern in entity_patterns:
        if re.search(pattern, source_code_str, re.MULTILINE):
            table_matches = re.findall(r'@Table\s*\(\s*name\s*=\s*["\']([^"\']+)["\']', source_code_str)
            for table_name in table_matches:
                if table_name.strip():
                    entities.append({
                        'type': 'DATA',
                        'name': table_name.strip()
                    })
            
            if not table_matches:
                class_match = re.search(r'public\s+class\s+(\w+)', source_code_str)
                if class_match:
                    class_name = class_match.group(1)
                    table_name = re.sub('([a-z0-9])([A-Z])', r'\1_\2', class_name).lower()
                    entities.append({
                        'type': 'DATA',
                        'name': table_name
                    })
    
    return entities

def extract_class_relationships(source_code_str):
    """Extract extends and implements relationships"""
    relationships = []
    
    # Class extends
    class_extends_pattern = r'class\s+(\w+)\s+extends\s+([\w<>]+)'
    class_matches = re.findall(class_extends_pattern, source_code_str)
    for child_class, parent_class in class_matches:
        parent_class = re.sub(r'<.*?>', '', parent_class).strip()
        if parent_class:
            relationships.append({
                'child': child_class,
                'parent': parent_class,
                'type': 'EXTENDS'
            })
    
    # Class implements
    implements_pattern = r'class\s+(\w+)(?:\s+extends\s+\w+)?\s+implements\s+([\w<>,\s]+)'
    impl_matches = re.findall(implements_pattern, source_code_str)
    for class_name, implements_clause in impl_matches:
        interfaces = [part.strip().split('<')[0].strip() for part in implements_clause.split(',')]
        for interface in interfaces:
            if interface:
                relationships.append({
                    'child': class_name,
                    'parent': interface,
                    'type': 'IMPLEMENTS'
                })
    
    # Interface extends
    interface_extends_pattern = r'interface\s+(\w+)\s+extends\s+([\w<>,\s]+)'
    matches = re.findall(interface_extends_pattern, source_code_str)
    for interface_name, extends_clause in matches:
        parent_interfaces = [part.strip().split('<')[0].strip() for part in extends_clause.split(',')]
        for parent in parent_interfaces:
            if parent:
                relationships.append({
                    'child': interface_name,
                    'parent': parent,
                    'type': 'EXTENDS'
                })
    
    return relationships

print("✅ Code analysis functions loaded")

def build_class_registry():
    """Build comprehensive class registry with all metadata and application tracking"""
    global class_registry
    
    for root, _, files in os.walk(BASE_PATH):
        for file in files:
            if file.endswith('.java'):
                file_path = os.path.join(root, file)
                application = get_application_from_path(file_path)
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        source_code_str = f.read()
                    
                    # Extract class name and package
                    class_name = os.path.splitext(file)[0]
                    package_name, imports = extract_package_and_imports(source_code_str)
                    
                    # Fully qualified class name
                    fqcn = f"{package_name}.{class_name}" if package_name else class_name
                    
                    # Run extractors
                    endpoints = extract_api_endpoints(source_code_str)
                    db_entities = extract_database_entities(source_code_str)
                    class_relationships = extract_class_relationships(source_code_str)
                    
                    # Build class metadata
                    class_registry[fqcn] = {
                        'fqcn': fqcn,
                        'class_name': class_name,
                        'package': package_name,
                        'file_path': file_path,
                        'application': application,
                        'imports': imports,
                        'endpoints': endpoints,
                        'db_entities': db_entities,
                        'class_relationships': class_relationships
                    }
                    
                    # Add to relationships
                    if endpoints:
                        for ep in endpoints:
                            all_relationships.append({
                                'source_node': class_name,
                                'source_type': 'CLASS',
                                'destination_node': f"{ep['method']} {ep['path']}",
                                'destination_type': 'ENDPOINT',
                                'relationship': 'EXPOSES',
                                'file_path': os.path.relpath(file_path, BASE_PATH),
                                'application': application
                            })
                    
                    if db_entities:
                        for entity in db_entities:
                            all_relationships.append({
                                'source_node': class_name,
                                'source_type': 'CLASS',
                                'destination_node': entity['name'],
                                'destination_type': 'DATA',
                                'relationship': 'DATA_FIND',
                                'file_path': os.path.relpath(file_path, BASE_PATH),
                                'application': application
                            })
                    
                    if class_relationships:
                        for rel in class_relationships:
                            all_relationships.append({
                                'source_node': rel['child'],
                                'source_type': 'CLASS',
                                'destination_node': rel['parent'],
                                'destination_type': 'INTERFACE' if rel['type'] == 'IMPLEMENTS' else 'CLASS',
                                'relationship': rel['type'],
                                'file_path': os.path.relpath(file_path, BASE_PATH),
                                'application': application
                            })
                
                except Exception as e:
                    print(f"❌ Error processing {file_path}: {e}")
                    continue
    
    print(f"📦 Class registry built with {len(class_registry)} entries.")
    return class_registry

print("✅ Class registry functions loaded")

def read_source_code(file_path):
    """Read source code as bytes for tree-sitter parsing"""
    with open(file_path, 'rb') as f:
        return f.read()

def extract_ast_structure(file_path):
    """Extract AST structure with enhanced variable naming and filtering"""
    records = []
    source_code = read_source_code(file_path)
    tree = parser.parse(source_code)
    root_node = tree.root_node
    file_name = os.path.basename(file_path)
    application = get_application_from_path(file_path)

    def traverse(node, parent_type=None, parent_name=None, method_context=None):
        # ---- CLASS Declaration ----
        if node.type == 'class_declaration':
            class_name = None
            for child in node.children:
                if child.type == 'identifier':
                    class_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    records.append({
                        'source_node': file_name,
                        'source_type': 'FILE',
                        'destination_node': class_name,
                        'destination_type': 'CLASS',
                        'relationship': 'DECLARES',
                        'file_path': os.path.relpath(file_path, BASE_PATH),
                        'application': application
                    })
                    break
            for child in node.children:
                traverse(child, 'class', class_name, None)

        # ---- INTERFACE Declaration ----
        elif node.type == 'interface_declaration':
            interface_name = None
            for child in node.children:
                if child.type == 'identifier':
                    interface_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    records.append({
                        'source_node': file_name,
                        'source_type': 'FILE',
                        'destination_node': interface_name,
                        'destination_type': 'INTERFACE',
                        'relationship': 'DECLARES',
                        'file_path': os.path.relpath(file_path, BASE_PATH),
                        'application': application
                    })
                    break
            for child in node.children:
                traverse(child, 'interface', interface_name, None)

        # ---- METHOD Declaration ----
        elif node.type == 'method_declaration':
            method_name = None
            for child in node.children:
                if child.type == 'identifier':
                    method_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    if parent_name and parent_type in ['class', 'interface']:
                        records.append({
                            'source_node': parent_name,
                            'source_type': parent_type.upper(),
                            'destination_node': method_name,
                            'destination_type': 'METHOD',
                            'relationship': 'DECLARES',
                            'file_path': os.path.relpath(file_path, BASE_PATH),
                            'application': application
                        })
                    break
            for child in node.children:
                traverse(child, 'method', method_name, method_name)

        # ---- FIELD Declaration ----
        elif node.type == 'field_declaration':
            for child in node.children:
                if child.type == 'variable_declarator':
                    for grandchild in child.children:
                        if grandchild.type == 'identifier':
                            field_name = clean_node_name(source_code[grandchild.start_byte:grandchild.end_byte].decode('utf-8'))
                            if parent_name and parent_type == 'class' and not is_temp_variable(field_name):
                                formatted_var_name = format_variable_name(parent_name, field_name)
                                records.append({
                                    'source_node': parent_name,
                                    'source_type': 'CLASS',
                                    'destination_node': formatted_var_name,
                                    'destination_type': 'VARIABLE',
                                    'relationship': 'HAS_FIELD',
                                    'file_path': os.path.relpath(file_path, BASE_PATH),
                                    'application': application
                                })

        # ---- VARIABLE USE (in method) with enhanced naming ----
        elif node.type in ['assignment_expression', 'variable_declarator'] and parent_type == 'method':
            for child in node.children:
                if child.type == 'identifier':
                    var_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    if var_name and var_name != 'this' and not is_temp_variable(var_name) and method_context:
                        formatted_var_name = format_variable_name(method_context, var_name)
                        records.append({
                            'source_node': parent_name,
                            'source_type': 'METHOD',
                            'destination_node': formatted_var_name,
                            'destination_type': 'VARIABLE',
                            'relationship': 'DECLARES_VARIABLE',
                            'file_path': os.path.relpath(file_path, BASE_PATH),
                            'application': application
                        })

        # ---- Recurse for all children ----
        for child in node.children:
            traverse(child, parent_type, parent_name, method_context)

    traverse(root_node)
    return records

print("✅ AST extraction functions loaded")

def build_enhanced_system_prompt(file_path, ast_context, class_registry):
    """Build enhanced system prompt for LLM processing with application context"""
    application = get_application_from_path(file_path)
    
    ast_context_str = "\n".join([
        f"- {item['source_type']} '{item['source_node']}' {item['relationship']} {item['destination_type']} '{item['destination_node']}'"
        for item in ast_context[:20]  # Limit context for prompt size
    ])
    
    return f"""
You are analyzing Java code for lineage extraction in application: {application}.

CONTEXT FROM AST ANALYSIS:
{ast_context_str}

CRITICAL RULES - FOLLOW EXACTLY:
1. Use SIMPLE names only (remove prefixes like "method:", "class:", etc.)
2. MANDATORY NODE TYPES: PROJECT, APPLICATIONS, FOLDERS, FILE, CLASS, METHOD, INTERFACE, VARIABLE, ENDPOINT, DATA, OPERATION, VALUE
3. MANDATORY RELATIONSHIPS: CONTAINS, DATA_FIND, DECLARES, DECLARES_VARIABLE, EXPOSES, EXTENDS, HAS_FIELD, IMPLEMENTS, TRANSFORMS_TO, USES, ASSIGNS_VALUE, UPDATED_BY
4. VARIABLE NAMING: Format as method_name.variable_name (e.g., "getUserData.userId")
5. FILTER OUT: Loop counters (i, j, k), temp variables, variables starting with _ or $
6. RELATIONSHIP DIRECTIONS (DO NOT REVERSE):
   - FILE -[DECLARES]-> CLASS
   - CLASS -[DECLARES]-> METHOD  
   - CLASS -[HAS_FIELD]-> VARIABLE
   - METHOD -[DECLARES_VARIABLE]-> VARIABLE
   - METHOD -[USES]-> VARIABLE
   - VARIABLE -[TRANSFORMS_TO]-> VARIABLE
   - OPERATION -[ASSIGNS_VALUE]-> VARIABLE
   - VARIABLE -[UPDATED_BY]-> OPERATION
   - CLASS -[EXTENDS]-> CLASS
   - CLASS -[IMPLEMENTS]-> INTERFACE
   - CLASS -[EXPOSES]-> ENDPOINT
   - CLASS -[DATA_FIND]-> DATA

7. FOCUS ON: Variable transformations, method calls, data operations, assignments
8. Application context: {application}

Extract ALL meaningful relationships following these exact patterns.
"""

def process_with_llm(file_path, ast_context):
    """Process file with LLM using enhanced context"""
    try:
        # Load and split document
        loader = TextLoader(file_path)
        docs = loader.load()
        
        splitter = RecursiveCharacterTextSplitter.from_language(
            language=LC_Language.JAVA,
            chunk_size=4000,
            chunk_overlap=200
        )
        split_docs = splitter.split_documents(docs)
        
        # Build system prompt
        system_prompt = build_enhanced_system_prompt(file_path, ast_context, class_registry)
        application = get_application_from_path(file_path)

        transformer = LLMGraphTransformer(
            llm=llm,
            additional_instructions=system_prompt,
            allowed_nodes=['PROJECT', 'APPLICATIONS', 'FOLDERS', 'FILE', 'CLASS', 'METHOD', 'INTERFACE', 'VARIABLE', 'ENDPOINT', 'DATA', 'OPERATION', 'VALUE'],
            allowed_relationships=[
                ('PROJECT', 'CONTAINS', 'APPLICATIONS'),
                ('APPLICATIONS', 'CONTAINS', 'FOLDERS'),
                ('FOLDERS', 'CONTAINS', 'FILE'),
                ('FILE', 'DECLARES', 'CLASS'),
                ('FILE', 'DECLARES', 'INTERFACE'),
                ('CLASS', 'DECLARES', 'METHOD'),
                ('INTERFACE', 'DECLARES', 'METHOD'),
                ('CLASS', 'HAS_FIELD', 'VARIABLE'),
                ('METHOD', 'DECLARES_VARIABLE', 'VARIABLE'),
                ('METHOD', 'USES', 'VARIABLE'),
                ('VARIABLE', 'TRANSFORMS_TO', 'VARIABLE'),
                ('OPERATION', 'ASSIGNS_VALUE', 'VARIABLE'),
                ('VARIABLE', 'UPDATED_BY', 'OPERATION'),
                ('CLASS', 'EXTENDS', 'CLASS'),
                ('CLASS', 'IMPLEMENTS', 'INTERFACE'),
                ('CLASS', 'EXPOSES', 'ENDPOINT'),
                ('CLASS', 'DATA_FIND', 'DATA')
            ]
        )
        
        # Process documents
        graph_docs = transformer.convert_to_graph_documents(split_docs)
        
        # Convert to our format with application tracking
        llm_relationships = []
        for doc in graph_docs:
            for rel in doc.relationships:
                llm_relationships.append({
                    'source_node': rel.source.id,
                    'source_type': rel.source.type,
                    'destination_node': rel.target.id,
                    'destination_type': rel.target.type,
                    'relationship': rel.type,
                    'file_path': os.path.relpath(file_path, BASE_PATH),
                    'application': application
                })
        
        return llm_relationships
        
    except Exception as e:
        print(f"❌ Error processing {file_path} with LLM: {e}")
        return []

print("✅ LLM processing functions loaded")

# Clear previous relationships
all_relationships = []

# Extract project hierarchy
hierarchy_count = extract_project_hierarchy()

# Create DataFrame
df_stage1 = pd.DataFrame(all_relationships)

print(f"\n📈 STAGE 1 COMPLETE: {len(df_stage1)} relationships extracted")
print(f"\n🏗️ HIERARCHY BREAKDOWN:")
print(df_stage1['source_type'].value_counts())
print(f"\n📊 STAGE 1 SAMPLE DATA:")
df_stage1.head(10)

# Build class registry
class_registry = build_class_registry()

# Extract AST structure for all Java files
ast_records = []
for root, _, files in os.walk(BASE_PATH):
    for file in files:
        if file.endswith('.java'):
            file_path = os.path.join(root, file)
            try:
                ast_records.extend(extract_ast_structure(file_path))
            except Exception as e:
                print(f'[Error] Failed to parse {file}: {e}')

# Combine with existing relationships
all_relationships.extend(ast_records)
df_stage2 = pd.DataFrame(all_relationships)

print(f"\n📈 STAGE 2 COMPLETE: {len(ast_records)} AST relationships extracted")
print(f"Total relationships so far: {len(df_stage2)}")
print(f"\n🏷️ NODE TYPE BREAKDOWN:")
print("Source Types:")
print(df_stage2['source_type'].value_counts())
print("\nDestination Types:")
print(df_stage2['destination_type'].value_counts())
print(f"\n📊 STAGE 2 SAMPLE DATA:")
df_stage2.sample(10)

# Process files with LLM
llm_relationships = []
java_files = []

# Collect all Java files
for root, _, files in os.walk(BASE_PATH):
    for file in files:
        if file.endswith('.java'):
            java_files.append(os.path.join(root, file))

print(f"Processing {len(java_files)} Java files with LLM...")

# Process each file
for file_path in tqdm(java_files):  
    # Get AST context for this file
    file_ast_context = [rel for rel in ast_records if rel.get('file_path') == os.path.relpath(file_path, BASE_PATH)]
    
    # Process with LLM
    file_llm_rels = process_with_llm(file_path, file_ast_context)
    llm_relationships.extend(file_llm_rels)
    
    if file_llm_rels:
        print(f"✅ Processed {os.path.basename(file_path)}: {len(file_llm_rels)} relationships")

# Combine all relationships
all_relationships.extend(llm_relationships)
df_final = pd.DataFrame(all_relationships)

print(f"\n📈 STAGE 3 COMPLETE: {len(llm_relationships)} LLM relationships extracted")
print(f"Total final relationships: {len(df_final)}")
print(f"\n🏷️ FINAL NODE TYPE BREAKDOWN:")
print("Source Types:")
print(df_final['source_type'].value_counts())
print("\nDestination Types:")
print(df_final['destination_type'].value_counts())
print(f"\n📊 FINAL SAMPLE DATA:")
df_final.sample(10)



# Application-wise breakdown
print("📱 APPLICATION-WISE BREAKDOWN:")
print(df_final['application'].value_counts())

print("\n🔗 RELATIONSHIP TYPE BREAKDOWN:")
print(df_final['relationship'].value_counts())

# Variable analysis
variable_rels = df_final[df_final['destination_type'] == 'VARIABLE']
print(f"\n📊 VARIABLE RELATIONSHIPS: {len(variable_rels)}")
print("Variable relationship types:")
print(variable_rels['relationship'].value_counts())

# Show some enhanced variable names
print("\n🏷️ SAMPLE ENHANCED VARIABLE NAMES:")
enhanced_vars = variable_rels[variable_rels['destination_node'].str.contains('\.')]['destination_node'].head(10)
for var in enhanced_vars:
    print(f"  - {var}")

# Application-specific analysis
print("\n🏢 PER-APPLICATION ANALYSIS:")
for app in df_final['application'].unique():
    app_data = df_final[df_final['application'] == app]
    print(f"\n{app}:")
    print(f"  - Total relationships: {len(app_data)}")
    print(f"  - Node types: {app_data['destination_type'].nunique()}")
    print(f"  - Relationship types: {app_data['relationship'].nunique()}")
    print(f"  - Variables: {len(app_data[app_data['destination_type'] == 'VARIABLE'])}")

# UnifiedBolt\core analysis
core_data = df_final[df_final['file_path'].str.contains('UnifiedBolt.*core', na=False)]
print(f"\n🎯 UNIFIEDBOLT\\CORE ANALYSIS: {len(core_data)} relationships")
if len(core_data) > 0:
    print("Core relationship types:")
    print(core_data['relationship'].value_counts())

print("\n✅ Analysis complete!")

# Clear existing data
print("🧹 Clearing existing Neo4j data...")
graph.query("MATCH (n) DETACH DELETE n")

# Create nodes and relationships
print("📤 Pushing relationships to Neo4j...")

# Create unique nodes first
nodes = set()
for _, row in df_final.iterrows():
    nodes.add((row['source_node'], row['source_type'], row.get('application', 'Unknown')))
    nodes.add((row['destination_node'], row['destination_type'], row.get('application', 'Unknown')))

# Create nodes
for node_id, node_type, app in tqdm(nodes, desc="Creating nodes"):
    query = f"""
    MERGE (n:{node_type} {{id: $node_id, application: $app}})
    SET n.name = $node_id
    """
    graph.query(query, {"node_id": node_id, "app": app})

# Create relationships
for _, row in tqdm(df_final.iterrows(), desc="Creating relationships", total=len(df_final)):
    query = f"""
    MATCH (source:{row['source_type']} {{id: $source_id}})
    MATCH (target:{row['destination_type']} {{id: $target_id}})
    MERGE (source)-[r:{row['relationship']}]->(target)
    SET r.file_path = $file_path, r.application = $application
    """
    graph.query(query, {
        "source_id": row['source_node'],
        "target_id": row['destination_node'],
        "file_path": row.get('file_path', ''),
        "application": row.get('application', 'Unknown')
    })

# Verify data
node_count = graph.query("MATCH (n) RETURN count(n) as count")[0]['count']
rel_count = graph.query("MATCH ()-[r]->() RETURN count(r) as count")[0]['count']

print(f"\n✅ Neo4j push complete!")
print(f"📊 Created {node_count} nodes and {rel_count} relationships")
print(f"🌐 Access Neo4j Browser at: http://localhost:7474")
print(f"🔍 Database: {NEO4J_DB}")

# Sample queries
print("\n🔍 SAMPLE QUERIES TO TRY:")
print("1. Show all applications:")
print("   MATCH (n:APPLICATIONS) RETURN n.name")
print("\n2. Show variable transformations:")
print("   MATCH (v1:VARIABLE)-[r:TRANSFORMS_TO]->(v2:VARIABLE) RETURN v1.name, v2.name")
print("\n3. Show UnifiedBolt core structure:")
print("   MATCH (n) WHERE n.application = 'UnifiedBolt' RETURN n LIMIT 25")
print("\n4. Show enhanced variable names:")
print("   MATCH (v:VARIABLE) WHERE v.name CONTAINS '.' RETURN v.name LIMIT 10")

print("🎉 JAVA CODE LINEAGE ANALYSIS - VERSION 10 COMPLETE!")
print("=" * 60)

print(f"\n📊 FINAL STATISTICS:")
print(f"  • Total Relationships: {len(df_final):,}")
print(f"  • Applications Analyzed: {df_final['application'].nunique()}")
print(f"  • Java Files Processed: {len(java_files)}")
print(f"  • Classes in Registry: {len(class_registry)}")

print(f"\n🏗️ NODE TYPES EXTRACTED:")
node_types = sorted(df_final['destination_type'].unique())
for node_type in node_types:
    count = len(df_final[df_final['destination_type'] == node_type])
    print(f"  • {node_type}: {count:,}")

print(f"\n🔗 RELATIONSHIP TYPES:")
rel_types = sorted(df_final['relationship'].unique())
for rel_type in rel_types:
    count = len(df_final[df_final['relationship'] == rel_type])
    print(f"  • {rel_type}: {count:,}")

print(f"\n🎯 KEY FEATURES IMPLEMENTED:")
print(f"  ✅ Enhanced Variable Naming (method.variable format)")
print(f"  ✅ Smart Variable Filtering (removed temp/loop vars)")
print(f"  ✅ Application Tracking (each relationship tagged)")
print(f"  ✅ Complete Node Coverage (PROJECT → APPLICATIONS → ... → VALUE)")
print(f"  ✅ Comprehensive Relationships (all 13 types)")
print(f"  ✅ UnifiedBolt\\Core Structure Preserved")
print(f"  ✅ Neo4j Integration with Visualization")

print(f"\n🌟 ENHANCED VARIABLE EXAMPLES:")
enhanced_vars = df_final[df_final['destination_node'].str.contains('\.')]['destination_node'].unique()[:5]
for var in enhanced_vars:
    print(f"  • {var}")

print(f"\n🚀 NEXT STEPS:")
print(f"  1. Open Neo4j Browser: http://localhost:7474")
print(f"  2. Switch to database: {NEO4J_DB}")
print(f"  3. Run sample queries provided above")
print(f"  4. Explore variable transformations and lineage")
print(f"  5. Analyze application-specific patterns")

print(f"\n" + "=" * 60)
print(f"✨ Analysis Complete - Ready for Exploration! ✨")