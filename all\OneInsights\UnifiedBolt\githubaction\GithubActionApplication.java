package com.bolt.dashboard.githubaction;

import java.util.Date;
import java.util.Iterator;
import java.util.Set;
import java.util.regex.Pattern;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONTokener;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.repository.BuildToolRep;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.util.EncryptionDecryptionAES;

public class GithubActionApplication {

	
	private static final Logger LOGGER = LogManager.getLogger(GithubActionApplication.class.getName());
	AnnotationConfigApplicationContext applicationContext = null;
	BuildToolRep repo = null;
	GithubAction githubActionMetrics = null;
	String result = "SUCCESS";
	String buildType = "Github Action";
	int pageLimit = 100;
	ConfigurationSettingRep configurationRepo = null;
	ConfigurationSetting configuration = null;
	Set<ConfigurationToolInfoMetric> metric = null;
	ConfigurationToolInfoMetric metric1 = null;

//	public static void main(String[] args) {
//		new GithubActionApplication().githubActionMain("BrillioOne");
//	}

	public void githubActionMain(String projectName) throws RestClientException {
		
		LOGGER.info("Github Actions Collector started for {}",projectName);
		applicationContext = DataConfig.getContext();
		repo = applicationContext.getBean(BuildToolRep.class);
		String instanceURL = "";
		String branch = "";
		String username = "";
		String password = null;
		String repoName = null;
		boolean firstRun = false;
		String apiToken=null;
		configurationRepo = applicationContext.getBean(ConfigurationSettingRep.class);
		configuration = configurationRepo.findByProjectName(projectName).get(0);
		metric = configuration.getMetrics();
	
		Iterator<ConfigurationToolInfoMetric> iter = metric.iterator();
		LOGGER.info("Project name  {}",configuration.getProjectName());
		

		githubActionMetrics = new GithubActionImplementation();
         String[] splitUrl = null;
		 String[] splitRepoName = null;
		 
		 
			while (iter.hasNext()) {
								Object configuration1 = iter.next();
								metric1 = (ConfigurationToolInfoMetric) configuration1;
								
								if ("Github Action".equals(metric1.getToolName())) {
									LOGGER.info("Tool name  {}", metric1.getToolName());
									instanceURL = metric1.getUrl();
									username = metric1.getUserName();
									password=EncryptionDecryptionAES.decrypt(metric1.getPassword(), ConstantVariable.SECRET_KEY);
									repoName = metric1.getRepoName();
									break;
								}

							}

							if (repoName == null)
								repoName = projectName;
							if (instanceURL.contains(",")) {
								splitUrl = instanceURL.split(Pattern.quote(","));
								splitRepoName = repoName.split(",");
							} else {
								splitUrl = new String[1];
								splitUrl[0] = instanceURL;
								splitRepoName = new String[1];
								splitRepoName[0] = repoName;

							}

							for (int i = 0; i < splitUrl.length; i++) {
								try {
									
									githubActionMetrics.getBuildToolData(splitUrl[i], repo, firstRun, branch,
				String.valueOf(apiToken),username, password, projectName, splitRepoName[i]);							

								} catch (Exception e) {
									result="FAIL";
									ConstantVariable.getLastRun(projectName, buildType, new Date().getTime(), result);
									LOGGER.info(e.getMessage());
									e.printStackTrace();
								}
							}
							ConstantVariable.getLastRun(projectName, buildType, new Date().getTime(), result);

							LOGGER.info("GitHub Action Collector ended for {}", projectName);
							cleanObject();
	}

	public void cleanObject() {
		repo = null;
		githubActionMetrics = null;
	}

	private JSONArray parseAsArray(ResponseEntity<String> response) {
		return (JSONArray) new JSONTokener(response.getBody()).nextValue();
	}

   private ResponseEntity<String> makeRestCall(String url) {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(20000000);
		requestFactory.setReadTimeout(20000000);
		RestTemplate rest = new RestTemplate(requestFactory);
		
		try {
			ResponseEntity<String> response = rest.exchange(url, HttpMethod.GET, null, String.class);
			return response;
		} catch (HttpClientErrorException e) {
			
			LOGGER.error("Error in Github Action ", e);
			if (e.getMessage().contains("404"))
				return new ResponseEntity<>(HttpStatus.NOT_FOUND);
			else if (e.getMessage().contains("401"))
				return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
		} catch (Exception e) {
			LOGGER.info(e);
			return new ResponseEntity<>(HttpStatus.NOT_FOUND);
		}
		return null;

	}

}