source_node,source_type,destination_node,destination_type,relationship,file_path,application
Buildtoolcontroller,CLASS,Buildjoblist,ENDPOINT,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Builddetailshome,ENDPOINT,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Builddetails,ENDPOINT,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Jobslist,ENDPOINT,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Jenkinsbuildfailure,ENDPOINT,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Jenkinsbuildfailuredata,ENDPOINT,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Jenkinsbuildfailurepatternfetchdata,ENDPOINT,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Jenkinsbuildfailurepatternfetchdataconfig,ENDPOINT,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Getbuildvaluestream,ENDPOINT,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Getgitlabbuildvaluestream,ENDPOINT,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Getonejob,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Getbuilddetailshome,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Builddata,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Joblist,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Savebuildfailurepattern,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Fetchdata,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Fetchfailurepatterndata,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Fetchfailurepatterndatacopy,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Gitlabbuilddata,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Getbuildtype,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Search,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Searchjoblist,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Searchfortest,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildfailurepattern,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Fetchbuilddata,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Fetchfailurepatterndata,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Getonebyprojectname,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Getbuilddetailshome,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Getvaluestream,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Getgitlabvaluestream,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolcontroller,CLASS,BuildtoolcontrollerBuildtoolservice,VARIABLE,HAS_FIELD,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Buildtoolservice,VARIABLE,HAS_FIELD,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,BuildtoolserviceimplemantationBuildtoolrepository,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,BuildtoolserviceimplemantationBuildfailurepatternforprojectrepo,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,BuildtoolserviceimplemantationLog,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,BuildtoolserviceimplemantationConfigsettingrepo,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,BuildtoolserviceimplemantationMetric,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,BuildtoolserviceimplemantationNodataconst,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,BuildtoolserviceimplemantationBuildconst,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolcontroller.java,FILE,Buildtoolcontroller,CLASS,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolserviceimplemantation.java,FILE,Buildtoolserviceimplemantation,CLASS,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolservice.java,FILE,Buildtoolservice,INTERFACE,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Api,FOLDERS,BuildToolController.java,FILE,CONTAINS,ServiceBolt\api\BuildToolController.java,ServiceBolt
Service,FOLDERS,BuildToolService.java,FILE,CONTAINS,ServiceBolt\service\BuildToolService.java,ServiceBolt
Service,FOLDERS,BuildToolServiceImplemantation.java,FILE,CONTAINS,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
OneInsights,FOLDERS,ServiceBolt,FOLDERS,CONTAINS,,ServiceBolt
ServiceBolt,FOLDERS,Api,FOLDERS,CONTAINS,,ServiceBolt
ServiceBolt,FOLDERS,Service,FOLDERS,CONTAINS,,ServiceBolt
Buildtoolservice,INTERFACE,Searchfortest,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolservice,INTERFACE,Search,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolservice,INTERFACE,Searchjoblist,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolservice,INTERFACE,Buildfailurepattern,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolservice,INTERFACE,Fetchfailurepatterndata,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolservice,INTERFACE,Fetchbuilddata,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolservice,INTERFACE,Getonebyprojectname,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolservice,INTERFACE,Getbuilddetailshome,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolservice,INTERFACE,Getvaluestream,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolservice,INTERFACE,Getgitlabvaluestream,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildfailurepattern,METHOD,BuildfailurepatternPatternforproject,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildfailurepattern,METHOD,BuildfailurepatternBuildfailurepatternforprojectlist,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Fetchbuilddata,METHOD,FetchbuilddataLastupdated,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Fetchbuilddata,METHOD,FetchbuilddataResult,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Fetchfailurepatterndata,METHOD,FetchfailurepatterndataObj,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
Fetchfailurepatterndata,METHOD,FetchfailurepatterndataLastupdated,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Fetchfailurepatterndata,METHOD,FetchfailurepatterndataResponse,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Fetchfailurepatterndatacopy,METHOD,FetchfailurepatterndatacopyObj,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
Getbuilddetailshome,METHOD,GetbuilddetailshomeResult,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Getbuildtype,METHOD,GetbuildtypeBuildtype,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
Getbuildtype,METHOD,GetbuildtypeContext,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
Getbuildtype,METHOD,GetbuildtypeConfigurationrepo,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
Getgitlabvaluestream,METHOD,GetgitlabvaluestreamProname,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Getgitlabvaluestream,METHOD,GetgitlabvaluestreamConfig,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Getgitlabvaluestream,METHOD,GetgitlabvaluestreamMetric,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Getonebyprojectname,METHOD,GetonebyprojectnameResult,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Getvaluestream,METHOD,GetvaluestreamProname,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Getvaluestream,METHOD,GetvaluestreamConfig,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Getvaluestream,METHOD,GetvaluestreamMetric,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Joblist,METHOD,JoblistBuildtype,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
Savebuildfailurepattern,METHOD,SavebuildfailurepatternObj,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
Search,METHOD,SearchLastupdate,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Search,METHOD,SearchResult,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Searchfortest,METHOD,SearchfortestResult,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Searchjoblist,METHOD,SearchjoblistLastupdate,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Searchjoblist,METHOD,SearchjoblistResult,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Basemodel,CLASS,Getid,METHOD,DECLARES,UnifiedBolt\core\model\BaseModel.java,UnifiedBolt
Basemodel,CLASS,Setid,METHOD,DECLARES,UnifiedBolt\core\model\BaseModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Getprojectname,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Setprojectname,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Getpatternmetrics,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Setpatternmetrics,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Gettimestampofcreation,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Settimestampofcreation,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Addpatternmetric,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Getusername,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Setusername,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Getpatterndefined,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Setpatterndefined,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Getpatterndisplayed,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Setpatterndisplayed,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Getpatterncount,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Setpatterncount,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Getreponame,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Setreponame,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfileinfo,CLASS,Getfilenames,METHOD,DECLARES,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
Buildfileinfo,CLASS,Setfilenames,METHOD,DECLARES,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
Buildfileinfo,CLASS,Getedittype,METHOD,DECLARES,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
Buildfileinfo,CLASS,Setedittype,METHOD,DECLARES,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
Buildinfo,CLASS,Getmessage,METHOD,DECLARES,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Buildinfo,CLASS,Setmessage,METHOD,DECLARES,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Buildinfo,CLASS,Getcommitter,METHOD,DECLARES,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Buildinfo,CLASS,Setcommitter,METHOD,DECLARES,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Buildinfo,CLASS,Getbuildfileinfolist,METHOD,DECLARES,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Buildinfo,CLASS,Setbuildfileinfolist,METHOD,DECLARES,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Buildsteps,CLASS,Getstepname,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Setstepname,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Getduration,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Setduration,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Getresult,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Setresult,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Getstartedtime,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Setstartedtime,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Getcompletedtime,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Setcompletedtime,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildtool,CLASS,Compareto,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Getjobcount,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Setjobcount,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Gettriggertype,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Settriggertype,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Getreponame,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Setreponame,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Getgroupname,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Setgroupname,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Getdefinitionid,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Setdefinitionid,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Getpatterndetails,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Setpatterndetails,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtoolmetric,CLASS,Getname,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Setname,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Getvalue,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Setvalue,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Getformattedvalue,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Setformattedvalue,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Getstatusmessage,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Setstatusmessage,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Equals,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Hashcode,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Configurationsetting,CLASS,Ismanualdata,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Setmanualdata,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Gettimestamp,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Settimestamp,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Getprojectname,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Setprojectname,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Getmetrics,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Isaddflag,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Setaddflag,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Isbaseline,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Setbaseline,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Getprojecttype,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Setprojecttype,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getid,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setid,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Gettoolname,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Settoolname,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Geturl,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Seturl,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getusername,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setusername,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getpassword,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setpassword,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Gettooltype,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Settooltype,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getwidgetname,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setwidgetname,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getjobname,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setjobname,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getprojectcode,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setprojectcode,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getselected,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setselected,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getdomain,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setdomain,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Gethost,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Sethost,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getport,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setport,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getdbtype,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setdbtype,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getschema,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setschema,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getreponame,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setreponame,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getsecret,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setsecret,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Githubactionapplication,CLASS,Githubactionmain,METHOD,DECLARES,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionimplementation,CLASS,Getbuildtooldata,METHOD,DECLARES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Processpipelinedata,METHOD,DECLARES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Basemodel,CLASS,BasemodelId,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BaseModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,BuildfailurepatternforprojectinjenkinsmodelProjectname,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,BuildfailurepatternforprojectinjenkinsmodelUsername,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,BuildfailurepatternforprojectinjenkinsmodelPatternmetrics,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,BuildfailurepatternforprojectinjenkinsmodelTimestampofcreation,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,BuildfailurepatternmetricsPatterndefined,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,BuildfailurepatternmetricsPatterndisplayed,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,BuildfailurepatternmetricsPatterncount,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,BuildfailurepatternmetricsReponame,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfileinfo,CLASS,Filenames,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
Buildfileinfo,CLASS,Edittype,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
Buildinfo,CLASS,BuildinfoMessage,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Buildinfo,CLASS,BuildinfoCommitter,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Buildinfo,CLASS,BuildinfoBuildfileinfolist,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Buildsteps,CLASS,BuildstepsStepname,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,BuildstepsDuration,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,BuildstepsResult,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,BuildstepsStartedtime,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,BuildstepsCompletedtime,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildtool,CLASS,BuildtoolCollectoritemid,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,BuildtoolTimestamp,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,BuildtoolTimestring,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,BuildtoolName,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,BuildtoolJobname,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,BuildtoolUrl,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,BuildtoolVersion,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,BuildtoolBuildtype,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,BuildtoolBuildid,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,BuildtoolBuildinfolist,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,BuildtoolMetrics,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,BuildtoolStepslist,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,BuildtoolJoblist,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,BuildtoolJobcount,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,BuildtoolCreatedby,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,BuildtoolBranchname,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,BuildtoolReponame,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,BuildtoolGroupname,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,BuildtoolTriggertype,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,BuildtoolPatterndetails,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,BuildtoolDefinitionid,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtoolmetric,CLASS,BuildtoolmetricName,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,BuildtoolmetricValuebuildtool,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,BuildtoolmetricFormattedvaluebuildtool,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,BuildtoolmetricStatusmessagebuildtool,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Configurationsetting,CLASS,Manualdata,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Timestamp,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Projectname,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Metric,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Addflag,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Baseline,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Projecttype,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationtoolinfometric,CLASS,ConfigurationtoolinfometricSelected,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,ConfigurationtoolinfometricId,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,ConfigurationtoolinfometricToolname,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,ConfigurationtoolinfometricUrl,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,ConfigurationtoolinfometricUsername,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,ConfigurationtoolinfometricPassword,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,ConfigurationtoolinfometricTooltype,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,ConfigurationtoolinfometricWidgetname,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,ConfigurationtoolinfometricJobname,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,ConfigurationtoolinfometricProjectcode,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,ConfigurationtoolinfometricDomain,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,ConfigurationtoolinfometricHost,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,ConfigurationtoolinfometricPort,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,ConfigurationtoolinfometricDbtype,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,ConfigurationtoolinfometricSchema,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,ConfigurationtoolinfometricReponame,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,ConfigurationtoolinfometricSecret,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,ConfigurationtoolinfometricManualdata,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Githubactionapplication,CLASS,GithubactionapplicationLogger,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,GithubactionapplicationApplicationcontext,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,GithubactionapplicationRepo,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,GithubactionapplicationGithubactionmetrics,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,GithubactionapplicationResult,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,GithubactionapplicationBuildtype,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,GithubactionapplicationPagelimit,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,GithubactionapplicationConfigurationrepo,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,GithubactionapplicationConfiguration,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,GithubactionapplicationMetric,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,GithubactionapplicationMetric1,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionimplementation,CLASS,GithubactionimplementationLogger,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,GithubactionimplementationSegmentApi,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,GithubactionimplementationPublicGithubRepoHost,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,GithubactionimplementationPublicGithubHostName,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,GithubactionimplementationCtx,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,GithubactionimplementationProjectname,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,GithubactionimplementationUsername,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,GithubactionimplementationPassword,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,GithubactionimplementationTime,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,GithubactionimplementationBuildrepo,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,GithubactionimplementationJobcollection,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,GithubactionimplementationPipelineurl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,GithubactionimplementationSinglepipelineurl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,GithubactionimplementationGithuburl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,GithubactionimplementationJobsurl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,GithubactionimplementationSize,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,GithubactionimplementationLastpage,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,GithubactionimplementationLastbuildid,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,GithubactionimplementationNewbuildpipelinetimestamp,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,GithubactionimplementationReponame,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Logger,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,SegmentApi,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,PublicGithubRepoHost,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,PublicGithubHostName,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Ctx,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Projectname,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Username,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Password,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Time,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Buildrepo,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Jobcollection,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Pipelineurl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Singlepipelineurl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githuburl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Jobsurl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Size,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Lastpage,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Lastbuildid,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Newbuildpipelinetimestamp,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Basemodel.java,FILE,Basemodel,CLASS,DECLARES,UnifiedBolt\core\model\BaseModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel.java,FILE,Buildfailurepatternforprojectinjenkinsmodel,CLASS,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternmetrics.java,FILE,Buildfailurepatternmetrics,CLASS,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfileinfo.java,FILE,Buildfileinfo,CLASS,DECLARES,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
Buildinfo.java,FILE,Buildinfo,CLASS,DECLARES,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Buildsteps.java,FILE,Buildsteps,CLASS,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildtool.java,FILE,Buildtool,CLASS,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtoolmetric.java,FILE,Buildtoolmetric,CLASS,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Configurationsetting.java,FILE,Configurationsetting,CLASS,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationtoolinfometric.java,FILE,Configurationtoolinfometric,CLASS,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Githubactionapplication.java,FILE,Githubactionapplication,CLASS,DECLARES,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionimplementation.java,FILE,Githubactionimplementation,CLASS,DECLARES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Buildfailurepatternforprojectrepo.java,FILE,Buildfailurepatternforprojectrepo,INTERFACE,DECLARES,UnifiedBolt\core\repository\BuildFailurePatternForProjectRepo.java,UnifiedBolt
Buildtoolrep.java,FILE,Buildtoolrep,INTERFACE,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Configurationsettingrep.java,FILE,Configurationsettingrep,INTERFACE,DECLARES,UnifiedBolt\core\repository\ConfigurationSettingRep.java,UnifiedBolt
Githubaction.java,FILE,Githubaction,INTERFACE,DECLARES,UnifiedBolt\githubaction\GithubAction.java,UnifiedBolt
Githubaction,FOLDERS,GithubAction.java,FILE,CONTAINS,UnifiedBolt\githubaction\GithubAction.java,UnifiedBolt
Githubaction,FOLDERS,GithubActionApplication.java,FILE,CONTAINS,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubaction,FOLDERS,GithubActionImplementation.java,FILE,CONTAINS,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Model,FOLDERS,BaseModel.java,FILE,CONTAINS,UnifiedBolt\core\model\BaseModel.java,UnifiedBolt
Model,FOLDERS,BuildFailurePatternForProjectInJenkinsModel.java,FILE,CONTAINS,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Model,FOLDERS,BuildFailurePatternMetrics.java,FILE,CONTAINS,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Model,FOLDERS,BuildFileInfo.java,FILE,CONTAINS,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
Model,FOLDERS,BuildInfo.java,FILE,CONTAINS,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Model,FOLDERS,BuildSteps.java,FILE,CONTAINS,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Model,FOLDERS,BuildTool.java,FILE,CONTAINS,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Model,FOLDERS,BuildToolMetric.java,FILE,CONTAINS,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Model,FOLDERS,ConfigurationSetting.java,FILE,CONTAINS,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Model,FOLDERS,ConfigurationToolInfoMetric.java,FILE,CONTAINS,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Repository,FOLDERS,BuildFailurePatternForProjectRepo.java,FILE,CONTAINS,UnifiedBolt\core\repository\BuildFailurePatternForProjectRepo.java,UnifiedBolt
Repository,FOLDERS,BuildToolRep.java,FILE,CONTAINS,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Repository,FOLDERS,ConfigurationSettingRep.java,FILE,CONTAINS,UnifiedBolt\core\repository\ConfigurationSettingRep.java,UnifiedBolt
Core,FOLDERS,Model,FOLDERS,CONTAINS,,UnifiedBolt
Core,FOLDERS,Repository,FOLDERS,CONTAINS,,UnifiedBolt
OneInsights,FOLDERS,UnifiedBolt,FOLDERS,CONTAINS,,UnifiedBolt
UnifiedBolt,FOLDERS,Core,FOLDERS,CONTAINS,,UnifiedBolt
UnifiedBolt,FOLDERS,Githubaction,FOLDERS,CONTAINS,,UnifiedBolt
Buildfailurepatternforprojectrepo,INTERFACE,Findbyprojectname,METHOD,DECLARES,UnifiedBolt\core\repository\BuildFailurePatternForProjectRepo.java,UnifiedBolt
Buildfailurepatternforprojectrepo,INTERFACE,Findall,METHOD,DECLARES,UnifiedBolt\core\repository\BuildFailurePatternForProjectRepo.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbybuildtype,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Countbybuildtype,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbybuildtypeandname,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbynameandjobname,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Getidbybuildtype,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbybuildtypeandnameandtimestampbetween,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbynameandtimestampbetween,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbyname,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findonebyname,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findonebynameorderbybuildiddesc,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbybuildtypeandreponameandgroupnameorderbybuildiddesc,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbynameandbuildtypeignorecase,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbynameandreponameandbranchname,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbynameanddefinitionid,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbybuildtypeandreponameandnameorderbybuildiddesc,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbybuildtypeandreponameandnameorderbytimestampdesc,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Configurationsettingrep,INTERFACE,Findbyprojectname,METHOD,DECLARES,UnifiedBolt\core\repository\ConfigurationSettingRep.java,UnifiedBolt
Configurationsettingrep,INTERFACE,Findbyprojectnamein,METHOD,DECLARES,UnifiedBolt\core\repository\ConfigurationSettingRep.java,UnifiedBolt
Configurationsettingrep,INTERFACE,Deletebyprojectname,METHOD,DECLARES,UnifiedBolt\core\repository\ConfigurationSettingRep.java,UnifiedBolt
Githubaction,INTERFACE,Getbuildtooldata,METHOD,DECLARES,UnifiedBolt\githubaction\GithubAction.java,UnifiedBolt
Addpatternmetric,METHOD,AddpatternmetricBuildfailurepatternmetrics,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Compareto,METHOD,ComparetoObject,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getbranchname,METHOD,BuildtoolBranchname,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getbuildid,METHOD,BuildtoolBuildid,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getbuildinfolist,METHOD,BuildtoolBuildinfolist,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getbuildtooldata,METHOD,Page,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Getbuildtype,METHOD,BuildtoolBuildtype,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getcollectoritemid,METHOD,BuildtoolCollectoritemid,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getcreatedby,METHOD,BuildtoolCreatedby,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getdefinitionid,METHOD,BuildtoolDefinitionid,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getgroupname,METHOD,BuildtoolGroupname,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getjenkinsitemid,METHOD,BuildtoolCollectoritemid,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getjobcount,METHOD,BuildtoolJobcount,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getjoblist,METHOD,BuildtoolJoblist,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getjobname,METHOD,BuildtoolJobname,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getmetrics,METHOD,BuildtoolMetrics,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getname,METHOD,BuildtoolName,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getpatterndetails,METHOD,BuildtoolPatterndetails,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getreponame,METHOD,BuildtoolReponame,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getstepslist,METHOD,BuildtoolStepslist,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Gettimestamp,METHOD,BuildtoolTimestamp,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Gettimestring,METHOD,BuildtoolTimestring,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Gettriggertype,METHOD,BuildtoolTriggertype,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Geturl,METHOD,BuildtoolUrl,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getversion,METHOD,BuildtoolVersion,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Githubactionmain,METHOD,GithubactionmainApplicationcontext,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,GithubactionmainRepo,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,GithubactionmainInstanceurl,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,GithubactionmainBranch,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,GithubactionmainUsername,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,GithubactionmainPassword,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,GithubactionmainReponame,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Makerestcall,METHOD,ProcesspipelinedataJobsurl,VARIABLE,USES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Makerestcall,METHOD,ProcesspipelinedataUser,VARIABLE,USES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Makerestcall,METHOD,ProcesspipelinedataPass,VARIABLE,USES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,ProcesspipelinedataPipelinevalues,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,ProcesspipelinedataUser,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,ProcesspipelinedataPass,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,ProcesspipelinedataProjectname,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,ProcesspipelinedataReponame,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,ProcesspipelinedataGithuburl,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,ProcesspipelinedataBuilds,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,ProcesspipelinedataLastbuildid,VARIABLE,USES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Setbuildfileinfolist,METHOD,SetbuildfileinfolistBuildfileinfolist,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Setcommitter,METHOD,SetcommitterCommitter,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Setdefinitionid,METHOD,BuildtoolDefinitionid,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Setduration,METHOD,SetdurationDuration,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Setedittype,METHOD,SetedittypeEdittype,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
Setfilenames,METHOD,SetfilenamesFilenames,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
Setformattedvalue,METHOD,SetformattedvalueFormattedvalue,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Setgroupname,METHOD,BuildtoolGroupname,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Setid,METHOD,SetidId,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BaseModel.java,UnifiedBolt
Setid,METHOD,BasemodelId,VARIABLE,USES,UnifiedBolt\core\model\BaseModel.java,UnifiedBolt
Setjobcount,METHOD,BuildtoolJobcount,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Setmanualdata,METHOD,Manualdata,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Setmessage,METHOD,SetmessageMessage,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Setname,METHOD,SetnameName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Setpatterncount,METHOD,SetpatterncountPatterncount,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Setpatterndefined,METHOD,SetpatterndefinedPatterndefined,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Setpatterndetails,METHOD,BuildtoolPatterndetails,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Setpatterndisplayed,METHOD,SetpatterndisplayedPatterndisplayed,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Setpatternmetrics,METHOD,SetpatternmetricsPatternmetrics,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Setprojectname,METHOD,SetprojectnameProjectname,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Setprojectname,METHOD,Projectname,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Setreponame,METHOD,SetreponameReponame,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Setreponame,METHOD,BuildtoolReponame,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Setresult,METHOD,SetresultResult,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Setstartedtime,METHOD,SetstartedtimeStartedtime,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Setstatusmessage,METHOD,SetstatusmessageStatusmessage,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Setstepname,METHOD,SetstepnameStepname,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Settimestamp,METHOD,Timestamp,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Settimestampofcreation,METHOD,SettimestampofcreationTimestampofcreation,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Settriggertype,METHOD,BuildtoolTriggertype,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Setvalue,METHOD,SetvalueValue,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Auth,VARIABLE,Encodedauth,VARIABLE,TRANSFORMS_TO,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Createddate,VARIABLE,Responseentity,VARIABLE,TRANSFORMS_TO,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Encodedauth,VARIABLE,Authheader,VARIABLE,TRANSFORMS_TO,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Fmt,VARIABLE,Createddate,VARIABLE,TRANSFORMS_TO,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
ProcesspipelinedataGithuburl,VARIABLE,ProcesspipelinedataTimestamp,VARIABLE,TRANSFORMS_TO,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Splitdate,VARIABLE,Temp,VARIABLE,TRANSFORMS_TO,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Temp,VARIABLE,Temptime,VARIABLE,TRANSFORMS_TO,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
TempDate,VARIABLE,Splitdate,VARIABLE,TRANSFORMS_TO,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
