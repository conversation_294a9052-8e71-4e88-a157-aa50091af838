/**
 * 
 */
package com.bolt.dashboard.api;

/**
 * 
 */
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.BuildFailurePatternForProjectInJenkinsModel;
import com.bolt.dashboard.core.model.BuildTool;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.GitlabValueStream;
import com.bolt.dashboard.core.model.ValueStreamStep;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.request.BuildFailureRequest;
import com.bolt.dashboard.request.BuildToolFailureSettingReq;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.BuildToolService;

/**
 * <AUTHOR>
 *
 */
@RestController
public class BuildToolController {
	private BuildToolService buildtoolservice;

	/*
	 * static AnnotationConfigApplicationContext ctx; static {
	 * ctx=DataConfig.getContext(); }
	 */
	@Autowired
	public BuildToolController(BuildToolService buildtoolservice) {
		this.buildtoolservice = buildtoolservice;

	}
	
	@RequestMapping(value = "/buildJobList", method = GET, produces = APPLICATION_JSON_VALUE)
	public BuildTool getOneJob(@RequestParam("pName") String projName){
		 return buildtoolservice.getOneByProjectName(projName);
	}

	@RequestMapping(value = "/buildDetailsHome", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<Map<String,String>> getBuildDetailsHome(@RequestParam("pName") String projName,@RequestParam("almType") String almType){
		 
	
		
		return buildtoolservice.getBuildDetailsHome(projName,almType);
	}
	
	@RequestMapping(value = "/buildDetails", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<Iterable<BuildTool>> buildData(@RequestParam("proName") String[] proName,
			@RequestParam("sDate") long sDate, @RequestParam("eDate") long eDate, @RequestParam("flag") boolean flag) {
		//String buildType = getBuildType(proName[0]);
		return buildtoolservice.search(proName[0], sDate, eDate, flag);
	}

	@RequestMapping(value = "/jobsList", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<Iterable<BuildTool>> jobList(
			@RequestParam("proName") String[] proName/*
														 * ,
														 * 
														 * @RequestParam(
														 * "sDate") long
														 * sDate, @RequestParam(
														 * "eDate") long
														 * eDate, @RequestParam(
														 * "flag") boolean flag,
														 * HttpSession
														 * httpSession
														 */) {
		String buildType = getBuildType(proName[0]);
		return buildtoolservice.searchJobList(buildType, proName[0]);

	}

	@RequestMapping(value = "/jenkinsBuildFailure", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
	public DataResponse<List<BuildFailureRequest>> saveBuildFailurePattern(
			@RequestBody List<BuildFailureRequest> failureRequestList) {
		BuildToolFailureSettingReq obj = new BuildToolFailureSettingReq();

		obj.setMetrics(failureRequestList);

		return buildtoolservice.buildFailurePattern("JENKINS", failureRequestList);
	}

	@RequestMapping(value = "/jenkinsBuildFailureData", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<List<BuildFailurePatternForProjectInJenkinsModel>> fetchData() {

		return buildtoolservice.fetchBuildData();
	}

	@RequestMapping(value = "/jenkinsBuildFailurePatternFetchData", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<List<BuildFailurePatternForProjectInJenkinsModel>> fetchFailurePatternData(
			@RequestParam("proName") String[] proName /*
														 * , HttpSession
														 * httpSession
														 */) {
		BuildFailureRequest obj = new BuildFailureRequest();
		return buildtoolservice.fetchFailurePatternData(proName[0]);
	}
	
	@RequestMapping(value = "/jenkinsBuildFailurePatternFetchDataConfig", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<List<BuildFailurePatternForProjectInJenkinsModel>> fetchFailurePatternDataCopy(
			@RequestParam("proName") String[] proName /*
														 * , HttpSession
														 * httpSession
														 */) {
		BuildFailureRequest obj = new BuildFailureRequest();
		return buildtoolservice.fetchFailurePatternData(proName[0]);
	}
	
	@RequestMapping(value = "/getBuildValueStream", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<ValueStreamStep> buildData(@RequestParam("proName") String proName) {
		return buildtoolservice.getValueStream(proName);
	}
	
	@RequestMapping(value = "/getGitlabBuildValueStream", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<GitlabValueStream> gitlabBuildData(@RequestParam("proName") String proName) {
		return buildtoolservice.getGitlabValueStream(proName);
	}
	
	

	public static String getBuildType(String projectName) {
		String buildType = "";
		AnnotationConfigApplicationContext context = new AnnotationConfigApplicationContext(DataConfig.class);
		ConfigurationSettingRep configurationRepo = context.getBean(ConfigurationSettingRep.class);
		ConfigurationSetting configurationColection = configurationRepo.findByProjectName(projectName).get(0);
		Set<ConfigurationToolInfoMetric> metric = configurationColection.getMetrics();
		Iterator iter = metric.iterator();
		while (iter.hasNext()) {
			Object configuration1 = iter.next();
			ConfigurationToolInfoMetric metric1 = (ConfigurationToolInfoMetric) configuration1;
			
			if ("TeamCity".equals(metric1.getToolName())) {
				buildType = "TEAMCITY";
				break;
			} else if ("Jenkins".equals(metric1.getToolName())) {
				buildType = "JENKINS";
				break;
			} else if ("CircleCI".equals(metric1.getToolName())) {
				buildType = "CircleCI";
				break;
			} else if ("TFS Build".equals(metric1.getToolName())) {
				buildType = "TFSBUILD";
				break;
			}

		}
		context.close();
		return buildType;

	}
}
