source_node,source_type,destination_node,destination_type,relationship,file_path,application
OneInsight,PROJECT,OneInsights,PROJECT,CONTAINS,,OneInsight
OneInsights,PROJECT,ServiceBolt,APPLICATIONS,CONTAINS,,ServiceBolt
OneInsights,PROJECT,UnifiedBolt,APPLICATIONS,CONTAINS,,UnifiedBolt
ServiceBolt,APPLICATIONS,ServiceBolt,FOLDERS,CONTAINS,,ServiceBolt
ServiceBolt,APPLICATIONS,api,FOLDERS,CONTAINS,,ServiceBolt
api,FOLDERS,BuildToolController.java,FILE,CONTAINS,ServiceBolt\api\BuildToolController.java,ServiceBolt
ServiceBolt,APPLICATIONS,service,FOLDERS,CONTAINS,,ServiceBolt
service,FOLDERS,BuildToolService.java,FILE,CONTAINS,ServiceBolt\service\BuildToolService.java,ServiceBolt
service,FOLDERS,BuildToolServiceImplemantation.java,FILE,CONTAINS,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
UnifiedBolt,APPLICATIONS,UnifiedBolt,FOLDERS,CONTAINS,,UnifiedBolt
UnifiedBolt,APPLICATIONS,core,FOLDERS,CONTAINS,,UnifiedBolt
UnifiedBolt\core,FOLDERS,model,FOLDERS,CONTAINS,,UnifiedBolt
model,FOLDERS,BaseModel.java,FILE,CONTAINS,UnifiedBolt\core\model\BaseModel.java,UnifiedBolt
model,FOLDERS,BuildFailurePatternForProjectInJenkinsModel.java,FILE,CONTAINS,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
model,FOLDERS,BuildFailurePatternMetrics.java,FILE,CONTAINS,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
model,FOLDERS,BuildFileInfo.java,FILE,CONTAINS,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
model,FOLDERS,BuildInfo.java,FILE,CONTAINS,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
model,FOLDERS,BuildSteps.java,FILE,CONTAINS,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
model,FOLDERS,BuildTool.java,FILE,CONTAINS,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
model,FOLDERS,BuildToolMetric.java,FILE,CONTAINS,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
model,FOLDERS,ConfigurationSetting.java,FILE,CONTAINS,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
model,FOLDERS,ConfigurationToolInfoMetric.java,FILE,CONTAINS,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
UnifiedBolt\core,FOLDERS,repository,FOLDERS,CONTAINS,,UnifiedBolt
repository,FOLDERS,BuildFailurePatternForProjectRepo.java,FILE,CONTAINS,UnifiedBolt\core\repository\BuildFailurePatternForProjectRepo.java,UnifiedBolt
repository,FOLDERS,BuildToolRep.java,FILE,CONTAINS,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
repository,FOLDERS,ConfigurationSettingRep.java,FILE,CONTAINS,UnifiedBolt\core\repository\ConfigurationSettingRep.java,UnifiedBolt
UnifiedBolt,APPLICATIONS,githubaction,FOLDERS,CONTAINS,,UnifiedBolt
githubaction,FOLDERS,GithubAction.java,FILE,CONTAINS,UnifiedBolt\githubaction\GithubAction.java,UnifiedBolt
githubaction,FOLDERS,GithubActionApplication.java,FILE,CONTAINS,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubaction,FOLDERS,GithubActionImplementation.java,FILE,CONTAINS,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
BuildToolController,CLASS,GET /buildJobList,ENDPOINT,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
BuildToolController,CLASS,GET /buildDetailsHome,ENDPOINT,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
BuildToolController,CLASS,GET /buildDetails,ENDPOINT,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
BuildToolController,CLASS,GET /jobsList,ENDPOINT,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
BuildToolController,CLASS,GET /jenkinsBuildFailure,ENDPOINT,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
BuildToolController,CLASS,GET /jenkinsBuildFailureData,ENDPOINT,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
BuildToolController,CLASS,GET /jenkinsBuildFailurePatternFetchData,ENDPOINT,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
BuildToolController,CLASS,GET /jenkinsBuildFailurePatternFetchDataConfig,ENDPOINT,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
BuildToolController,CLASS,GET /getBuildValueStream,ENDPOINT,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
BuildToolController,CLASS,GET /getGitlabBuildValueStream,ENDPOINT,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
BuildToolServiceImplemantation,CLASS,BuildToolService,INTERFACE,IMPLEMENTS,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
BuildFailurePatternForProjectInJenkinsModel,CLASS,BaseModel,CLASS,EXTENDS,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
BuildTool,CLASS,BaseModel,CLASS,EXTENDS,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,Comparable,INTERFACE,IMPLEMENTS,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
ConfigurationSetting,CLASS,BaseModel,CLASS,EXTENDS,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
GithubActionImplementation,CLASS,GithubAction,INTERFACE,IMPLEMENTS,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
BuildToolController.java,FILE,BuildToolController,CLASS,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
BuildToolController,CLASS,BuildToolController.buildtoolservice,VARIABLE,HAS_FIELD,ServiceBolt\api\BuildToolController.java,ServiceBolt
BuildToolController,CLASS,getOneJob,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
BuildToolController,CLASS,getBuildDetailsHome,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
BuildToolController,CLASS,buildData,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
BuildToolController,CLASS,jobList,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
jobList,METHOD,jobList.buildType,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
BuildToolController,CLASS,saveBuildFailurePattern,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
saveBuildFailurePattern,METHOD,saveBuildFailurePattern.obj,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
BuildToolController,CLASS,fetchData,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
BuildToolController,CLASS,fetchFailurePatternData,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
fetchFailurePatternData,METHOD,fetchFailurePatternData.obj,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
BuildToolController,CLASS,fetchFailurePatternDataCopy,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
fetchFailurePatternDataCopy,METHOD,fetchFailurePatternDataCopy.obj,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
BuildToolController,CLASS,buildData,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
BuildToolController,CLASS,gitlabBuildData,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
BuildToolController,CLASS,getBuildType,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
getBuildType,METHOD,getBuildType.buildType,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
getBuildType,METHOD,getBuildType.context,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
getBuildType,METHOD,getBuildType.configurationRepo,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
getBuildType,METHOD,getBuildType.configurationColection,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
getBuildType,METHOD,getBuildType.metric,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
getBuildType,METHOD,getBuildType.iter,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
getBuildType,METHOD,getBuildType.configuration1,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
getBuildType,METHOD,getBuildType.metric1,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
getBuildType,METHOD,getBuildType.buildType,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
getBuildType,METHOD,getBuildType.buildType,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
getBuildType,METHOD,getBuildType.buildType,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
getBuildType,METHOD,getBuildType.buildType,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
jobList,METHOD,jobList.buildType,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
saveBuildFailurePattern,METHOD,saveBuildFailurePattern.obj,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
fetchFailurePatternData,METHOD,fetchFailurePatternData.obj,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
fetchFailurePatternDataCopy,METHOD,fetchFailurePatternDataCopy.obj,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
getBuildType,METHOD,getBuildType.buildType,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
getBuildType,METHOD,getBuildType.context,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
getBuildType,METHOD,getBuildType.configurationRepo,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
getBuildType,METHOD,getBuildType.configurationColection,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
getBuildType,METHOD,getBuildType.metric,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
getBuildType,METHOD,getBuildType.iter,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
getBuildType,METHOD,getBuildType.configuration1,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
getBuildType,METHOD,getBuildType.metric1,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
getBuildType,METHOD,getBuildType.buildType,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
getBuildType,METHOD,getBuildType.buildType,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
getBuildType,METHOD,getBuildType.buildType,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
getBuildType,METHOD,getBuildType.buildType,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
BuildToolService.java,FILE,BuildToolService,INTERFACE,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
BuildToolService,INTERFACE,searchForTest,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
BuildToolService,INTERFACE,search,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
BuildToolService,INTERFACE,searchJobList,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
BuildToolService,INTERFACE,search,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
BuildToolService,INTERFACE,buildFailurePattern,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
BuildToolService,INTERFACE,fetchFailurePatternData,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
BuildToolService,INTERFACE,fetchBuildData,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
BuildToolService,INTERFACE,getOneByProjectName,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
BuildToolService,INTERFACE,getBuildDetailsHome,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
BuildToolService,INTERFACE,getValueStream,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
BuildToolService,INTERFACE,getGitlabValueStream,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
BuildToolServiceImplemantation.java,FILE,BuildToolServiceImplemantation,CLASS,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
BuildToolServiceImplemantation,CLASS,BuildToolServiceImplemantation.buildToolRepository,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
BuildToolServiceImplemantation,CLASS,BuildToolServiceImplemantation.buildFailurePatternForProjectRepo,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
BuildToolServiceImplemantation,CLASS,BuildToolServiceImplemantation.LOG,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
BuildToolServiceImplemantation,CLASS,BuildToolServiceImplemantation.configSettingRepo,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
BuildToolServiceImplemantation,CLASS,BuildToolServiceImplemantation.metric,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
BuildToolServiceImplemantation,CLASS,BuildToolServiceImplemantation.noDataConst,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
BuildToolServiceImplemantation,CLASS,BuildToolServiceImplemantation.buildConst,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
BuildToolServiceImplemantation,CLASS,search,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.lastUpdate,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
BuildToolServiceImplemantation,CLASS,search,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.lastUpdate,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
BuildToolServiceImplemantation,CLASS,searchJobList,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
searchJobList,METHOD,searchJobList.lastUpdate,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
searchJobList,METHOD,searchJobList.result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
BuildToolServiceImplemantation,CLASS,searchForTest,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
searchForTest,METHOD,searchForTest.result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
BuildToolServiceImplemantation,CLASS,search,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.flagnew,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.flag,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.toolName,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.config,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.metric,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.metric,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.toolName,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.toolName,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.toolName,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.toolName,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.mongoAggr,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.lastUpdate,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
BuildToolServiceImplemantation,CLASS,buildFailurePattern,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
buildFailurePattern,METHOD,buildFailurePattern.patternForproject,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
buildFailurePattern,METHOD,buildFailurePattern.buildFailurePatternForProjectList,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
buildFailurePattern,METHOD,buildFailurePattern.buildFailurePatternForProject,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
BuildToolServiceImplemantation,CLASS,fetchBuildData,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
fetchBuildData,METHOD,fetchBuildData.lastUpdated,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
fetchBuildData,METHOD,fetchBuildData.result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
BuildToolServiceImplemantation,CLASS,fetchFailurePatternData,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
fetchFailurePatternData,METHOD,fetchFailurePatternData.lastUpdated,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
fetchFailurePatternData,METHOD,fetchFailurePatternData.response,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
BuildToolServiceImplemantation,CLASS,getOneByProjectName,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
getOneByProjectName,METHOD,getOneByProjectName.result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
BuildToolServiceImplemantation,CLASS,getBuildDetailsHome,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
getBuildDetailsHome,METHOD,getBuildDetailsHome.projHomeCalc,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
getBuildDetailsHome,METHOD,getBuildDetailsHome.result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
BuildToolServiceImplemantation,CLASS,getValueStream,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
getValueStream,METHOD,getValueStream.config,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
getValueStream,METHOD,getValueStream.metric,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
getValueStream,METHOD,getValueStream.metric,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
getValueStream,METHOD,getValueStream.buildCalculations,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
BuildToolServiceImplemantation,CLASS,getGitlabValueStream,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
getGitlabValueStream,METHOD,getGitlabValueStream.config,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
getGitlabValueStream,METHOD,getGitlabValueStream.metric,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
getGitlabValueStream,METHOD,getGitlabValueStream.metric,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
getGitlabValueStream,METHOD,getGitlabValueStream.buildCalculations,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.lastUpdate,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.lastUpdate,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
searchJobList,METHOD,searchJobList.lastUpdate,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
searchJobList,METHOD,searchJobList.result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
searchForTest,METHOD,searchForTest.result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.flagnew,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.flag,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.toolName,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.config,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.metric,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.metric,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.toolName,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.toolName,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.toolName,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.toolName,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.mongoAggr,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.lastUpdate,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
search,METHOD,search.result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
buildFailurePattern,METHOD,buildFailurePattern.patternForproject,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
buildFailurePattern,METHOD,buildFailurePattern.buildFailurePatternForProjectList,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
buildFailurePattern,METHOD,buildFailurePattern.buildFailurePatternForProject,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
fetchBuildData,METHOD,fetchBuildData.lastUpdated,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
fetchBuildData,METHOD,fetchBuildData.result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
fetchFailurePatternData,METHOD,fetchFailurePatternData.lastUpdated,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
fetchFailurePatternData,METHOD,fetchFailurePatternData.response,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
getOneByProjectName,METHOD,getOneByProjectName.result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
getBuildDetailsHome,METHOD,getBuildDetailsHome.projHomeCalc,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
getBuildDetailsHome,METHOD,getBuildDetailsHome.result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
getValueStream,METHOD,getValueStream.config,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
getValueStream,METHOD,getValueStream.metric,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
getValueStream,METHOD,getValueStream.metric,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
getValueStream,METHOD,getValueStream.buildCalculations,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
getGitlabValueStream,METHOD,getGitlabValueStream.config,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
getGitlabValueStream,METHOD,getGitlabValueStream.metric,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
getGitlabValueStream,METHOD,getGitlabValueStream.metric,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
getGitlabValueStream,METHOD,getGitlabValueStream.buildCalculations,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
BaseModel.java,FILE,BaseModel,CLASS,DECLARES,UnifiedBolt\core\model\BaseModel.java,UnifiedBolt
BaseModel,CLASS,BaseModel.id,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BaseModel.java,UnifiedBolt
BaseModel,CLASS,getId,METHOD,DECLARES,UnifiedBolt\core\model\BaseModel.java,UnifiedBolt
BaseModel,CLASS,setId,METHOD,DECLARES,UnifiedBolt\core\model\BaseModel.java,UnifiedBolt
setId,METHOD,setId.id,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BaseModel.java,UnifiedBolt
setId,METHOD,setId.id,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BaseModel.java,UnifiedBolt
BuildFailurePatternForProjectInJenkinsModel.java,FILE,BuildFailurePatternForProjectInJenkinsModel,CLASS,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
BuildFailurePatternForProjectInJenkinsModel,CLASS,BuildFailurePatternForProjectInJenkinsModel.projectName,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
BuildFailurePatternForProjectInJenkinsModel,CLASS,BuildFailurePatternForProjectInJenkinsModel.userName,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
BuildFailurePatternForProjectInJenkinsModel,CLASS,BuildFailurePatternForProjectInJenkinsModel.patternMetrics,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
BuildFailurePatternForProjectInJenkinsModel,CLASS,BuildFailurePatternForProjectInJenkinsModel.timestampOfCreation,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
BuildFailurePatternForProjectInJenkinsModel,CLASS,getProjectName,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
BuildFailurePatternForProjectInJenkinsModel,CLASS,setProjectName,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
setProjectName,METHOD,setProjectName.projectName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
BuildFailurePatternForProjectInJenkinsModel,CLASS,getPatternMetrics,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
BuildFailurePatternForProjectInJenkinsModel,CLASS,setPatternMetrics,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
setPatternMetrics,METHOD,setPatternMetrics.patternMetrics,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
BuildFailurePatternForProjectInJenkinsModel,CLASS,getTimestampOfCreation,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
BuildFailurePatternForProjectInJenkinsModel,CLASS,setTimestampOfCreation,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
setTimestampOfCreation,METHOD,setTimestampOfCreation.timestampOfCreation,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
BuildFailurePatternForProjectInJenkinsModel,CLASS,addPatternMetric,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
addPatternMetric,METHOD,addPatternMetric.buildFailurePatternMetrics,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
BuildFailurePatternForProjectInJenkinsModel,CLASS,addPatternMetric,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
addPatternMetric,METHOD,addPatternMetric.buildFailurePatternMetrics,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
BuildFailurePatternForProjectInJenkinsModel,CLASS,getUserName,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
BuildFailurePatternForProjectInJenkinsModel,CLASS,setUserName,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
setUserName,METHOD,setUserName.userName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
setProjectName,METHOD,setProjectName.projectName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
setPatternMetrics,METHOD,setPatternMetrics.patternMetrics,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
setTimestampOfCreation,METHOD,setTimestampOfCreation.timestampOfCreation,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
addPatternMetric,METHOD,addPatternMetric.buildFailurePatternMetrics,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
addPatternMetric,METHOD,addPatternMetric.buildFailurePatternMetrics,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
setUserName,METHOD,setUserName.userName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
BuildFailurePatternMetrics.java,FILE,BuildFailurePatternMetrics,CLASS,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
BuildFailurePatternMetrics,CLASS,BuildFailurePatternMetrics.patternDefined,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
BuildFailurePatternMetrics,CLASS,BuildFailurePatternMetrics.patternDisplayed,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
BuildFailurePatternMetrics,CLASS,BuildFailurePatternMetrics.patternCount,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
BuildFailurePatternMetrics,CLASS,BuildFailurePatternMetrics.repoName,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
BuildFailurePatternMetrics,CLASS,getPatternDefined,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
BuildFailurePatternMetrics,CLASS,setPatternDefined,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
setPatternDefined,METHOD,setPatternDefined.patternDefined,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
BuildFailurePatternMetrics,CLASS,getPatternDisplayed,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
BuildFailurePatternMetrics,CLASS,setPatternDisplayed,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
setPatternDisplayed,METHOD,setPatternDisplayed.patternDisplayed,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
BuildFailurePatternMetrics,CLASS,getPatternCount,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
BuildFailurePatternMetrics,CLASS,setPatternCount,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
setPatternCount,METHOD,setPatternCount.patternCount,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
BuildFailurePatternMetrics,CLASS,getRepoName,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
BuildFailurePatternMetrics,CLASS,setRepoName,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
setRepoName,METHOD,setRepoName.repoName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
setPatternDefined,METHOD,setPatternDefined.patternDefined,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
setPatternDisplayed,METHOD,setPatternDisplayed.patternDisplayed,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
setPatternCount,METHOD,setPatternCount.patternCount,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
setRepoName,METHOD,setRepoName.repoName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
BuildFileInfo.java,FILE,BuildFileInfo,CLASS,DECLARES,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
BuildFileInfo,CLASS,BuildFileInfo.fileNames,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
BuildFileInfo,CLASS,BuildFileInfo.editType,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
BuildFileInfo,CLASS,getFileNames,METHOD,DECLARES,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
BuildFileInfo,CLASS,setFileNames,METHOD,DECLARES,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
setFileNames,METHOD,setFileNames.fileNames,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
BuildFileInfo,CLASS,getEditType,METHOD,DECLARES,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
BuildFileInfo,CLASS,setEditType,METHOD,DECLARES,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
setEditType,METHOD,setEditType.editType,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
setFileNames,METHOD,setFileNames.fileNames,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
setEditType,METHOD,setEditType.editType,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
BuildInfo.java,FILE,BuildInfo,CLASS,DECLARES,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
BuildInfo,CLASS,BuildInfo.message,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
BuildInfo,CLASS,BuildInfo.committer,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
BuildInfo,CLASS,BuildInfo.buildFileInfoList,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
BuildInfo,CLASS,getMessage,METHOD,DECLARES,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
BuildInfo,CLASS,setMessage,METHOD,DECLARES,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
setMessage,METHOD,setMessage.message,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
BuildInfo,CLASS,getCommitter,METHOD,DECLARES,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
BuildInfo,CLASS,setCommitter,METHOD,DECLARES,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
setCommitter,METHOD,setCommitter.committer,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
BuildInfo,CLASS,getBuildFileInfoList,METHOD,DECLARES,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
BuildInfo,CLASS,setBuildFileInfoList,METHOD,DECLARES,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
setBuildFileInfoList,METHOD,setBuildFileInfoList.buildFileInfoList,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
setMessage,METHOD,setMessage.message,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
setCommitter,METHOD,setCommitter.committer,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
setBuildFileInfoList,METHOD,setBuildFileInfoList.buildFileInfoList,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
BuildSteps.java,FILE,BuildSteps,CLASS,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
BuildSteps,CLASS,BuildSteps.stepName,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
BuildSteps,CLASS,BuildSteps.duration,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
BuildSteps,CLASS,BuildSteps.result,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
BuildSteps,CLASS,BuildSteps.startedTime,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
BuildSteps,CLASS,BuildSteps.completedTime,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
BuildSteps,CLASS,getStepName,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
BuildSteps,CLASS,setStepName,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
setStepName,METHOD,setStepName.stepName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
BuildSteps,CLASS,getDuration,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
BuildSteps,CLASS,setDuration,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
setDuration,METHOD,setDuration.duration,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
BuildSteps,CLASS,getResult,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
BuildSteps,CLASS,setResult,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
setResult,METHOD,setResult.result,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
BuildSteps,CLASS,getStartedTime,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
BuildSteps,CLASS,setStartedTime,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
setStartedTime,METHOD,setStartedTime.startedTime,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
BuildSteps,CLASS,getCompletedTime,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
BuildSteps,CLASS,setCompletedTime,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
setCompletedTime,METHOD,setCompletedTime.completedTime,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
setStepName,METHOD,setStepName.stepName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
setDuration,METHOD,setDuration.duration,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
setResult,METHOD,setResult.result,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
setStartedTime,METHOD,setStartedTime.startedTime,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
setCompletedTime,METHOD,setCompletedTime.completedTime,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
BuildTool.java,FILE,BuildTool,CLASS,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,BuildTool.collectorItemId,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,BuildTool.timestamp,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,BuildTool.timeString,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,BuildTool.name,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,BuildTool.jobName,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,BuildTool.url,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,BuildTool.version,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,BuildTool.buildType,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,BuildTool.buildID,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,BuildTool.buildInfoList,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,BuildTool.metrics,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,BuildTool.stepsList,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,BuildTool.jobList,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,BuildTool.jobCount,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,BuildTool.createdBy,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,BuildTool.branchName,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,BuildTool.repoName,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,BuildTool.groupName,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,BuildTool.triggerType,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,BuildTool.definitionId,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,getTimeString,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,setTimeString,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setTimeString,METHOD,setTimeString.timeString,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,getCollectorItemId,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,setCollectorItemId,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setCollectorItemId,METHOD,setCollectorItemId.collectorItemId,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,getStepsList,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,setStepsList,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setStepsList,METHOD,setStepsList.stepsList,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,getCreatedBy,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,setCreatedBy,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setCreatedBy,METHOD,setCreatedBy.createdBy,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,getBranchName,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,setBranchName,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setBranchName,METHOD,setBranchName.branchName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,setMetrics,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setMetrics,METHOD,setMetrics.metrics,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,BuildTool.patternDetails,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,getBuildID,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,setBuildID,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setBuildID,METHOD,setBuildID.buildID,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,getJenkinsItemId,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,setJenkinsItemId,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setJenkinsItemId,METHOD,setJenkinsItemId.jenkinsItemId,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,getTimestamp,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,setTimestamp,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setTimestamp,METHOD,setTimestamp.timestamp,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,getName,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,setName,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setName,METHOD,setName.name,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,getUrl,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,setUrl,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setUrl,METHOD,setUrl.url,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,getVersion,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,setVersion,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setVersion,METHOD,setVersion.version,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,getMetrics,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,getBuildType,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,setBuildType,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setBuildType,METHOD,setBuildType.buildType,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,getBuildInfoList,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,setBuildInfoList,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setBuildInfoList,METHOD,setBuildInfoList.buildInfoList,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,getJobName,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,setJobName,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setJobName,METHOD,setJobName.jobName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,getJobList,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,setJobList,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setJobList,METHOD,setJobList.jobList,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,getJobCount,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,setJobCount,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setJobCount,METHOD,setJobCount.jobCount,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,getPatternDetails,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,setPatternDetails,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setPatternDetails,METHOD,setPatternDetails.patternDetails,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,compareTo,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,getTriggerType,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,setTriggerType,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setTriggerType,METHOD,setTriggerType.triggerType,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,getRepoName,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,setRepoName,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setRepoName,METHOD,setRepoName.repoName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,getGroupName,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,setGroupName,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setGroupName,METHOD,setGroupName.groupName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,getDefinitionId,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildTool,CLASS,setDefinitionId,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setDefinitionId,METHOD,setDefinitionId.definitionId,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setTimeString,METHOD,setTimeString.timeString,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setCollectorItemId,METHOD,setCollectorItemId.collectorItemId,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setStepsList,METHOD,setStepsList.stepsList,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setCreatedBy,METHOD,setCreatedBy.createdBy,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setBranchName,METHOD,setBranchName.branchName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setMetrics,METHOD,setMetrics.metrics,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setBuildID,METHOD,setBuildID.buildID,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setJenkinsItemId,METHOD,setJenkinsItemId.jenkinsItemId,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setTimestamp,METHOD,setTimestamp.timestamp,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setName,METHOD,setName.name,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setUrl,METHOD,setUrl.url,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setVersion,METHOD,setVersion.version,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setBuildType,METHOD,setBuildType.buildType,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setBuildInfoList,METHOD,setBuildInfoList.buildInfoList,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setJobName,METHOD,setJobName.jobName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setJobList,METHOD,setJobList.jobList,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setJobCount,METHOD,setJobCount.jobCount,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setPatternDetails,METHOD,setPatternDetails.patternDetails,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setTriggerType,METHOD,setTriggerType.triggerType,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setRepoName,METHOD,setRepoName.repoName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setGroupName,METHOD,setGroupName.groupName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
setDefinitionId,METHOD,setDefinitionId.definitionId,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
BuildToolMetric.java,FILE,BuildToolMetric,CLASS,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
BuildToolMetric,CLASS,BuildToolMetric.name,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
BuildToolMetric,CLASS,BuildToolMetric.valueBuildTool,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
BuildToolMetric,CLASS,BuildToolMetric.formattedValueBuildTool,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
BuildToolMetric,CLASS,BuildToolMetric.statusMessageBuildTool,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
BuildToolMetric,CLASS,getName,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
BuildToolMetric,CLASS,setName,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
setName,METHOD,setName.name,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
BuildToolMetric,CLASS,getValue,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
BuildToolMetric,CLASS,setValue,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
setValue,METHOD,setValue.value,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
BuildToolMetric,CLASS,getFormattedValue,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
BuildToolMetric,CLASS,setFormattedValue,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
setFormattedValue,METHOD,setFormattedValue.formattedValue,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
BuildToolMetric,CLASS,getStatusMessage,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
BuildToolMetric,CLASS,setStatusMessage,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
setStatusMessage,METHOD,setStatusMessage.statusMessage,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
BuildToolMetric,CLASS,equals,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
BuildToolMetric,CLASS,hashCode,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
setName,METHOD,setName.name,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
setValue,METHOD,setValue.value,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
setFormattedValue,METHOD,setFormattedValue.formattedValue,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
setStatusMessage,METHOD,setStatusMessage.statusMessage,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
ConfigurationSetting.java,FILE,ConfigurationSetting,CLASS,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
ConfigurationSetting,CLASS,ConfigurationSetting.timestamp,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
ConfigurationSetting,CLASS,ConfigurationSetting.baseline,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
ConfigurationSetting,CLASS,ConfigurationSetting.projectName,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
ConfigurationSetting,CLASS,ConfigurationSetting.addFlag,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
ConfigurationSetting,CLASS,ConfigurationSetting.projectType,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
ConfigurationSetting,CLASS,ConfigurationSetting.manualData,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
ConfigurationSetting,CLASS,ConfigurationSetting.metric,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
ConfigurationSetting,CLASS,isManualData,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
ConfigurationSetting,CLASS,setManualData,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
setManualData,METHOD,setManualData.manualData,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
ConfigurationSetting,CLASS,getTimestamp,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
ConfigurationSetting,CLASS,setTimestamp,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
setTimestamp,METHOD,setTimestamp.timestamp,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
ConfigurationSetting,CLASS,getProjectName,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
ConfigurationSetting,CLASS,setProjectName,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
setProjectName,METHOD,setProjectName.projectName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
ConfigurationSetting,CLASS,getMetrics,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
ConfigurationSetting,CLASS,isAddFlag,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
ConfigurationSetting,CLASS,setAddFlag,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
setAddFlag,METHOD,setAddFlag.addFlag,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
ConfigurationSetting,CLASS,isBaseline,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
ConfigurationSetting,CLASS,setBaseline,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
setBaseline,METHOD,setBaseline.baseline,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
ConfigurationSetting,CLASS,getProjectType,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
ConfigurationSetting,CLASS,setProjectType,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
setProjectType,METHOD,setProjectType.projectType,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
setManualData,METHOD,setManualData.manualData,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
setTimestamp,METHOD,setTimestamp.timestamp,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
setProjectName,METHOD,setProjectName.projectName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
setAddFlag,METHOD,setAddFlag.addFlag,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
setBaseline,METHOD,setBaseline.baseline,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
setProjectType,METHOD,setProjectType.projectType,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
ConfigurationToolInfoMetric.java,FILE,ConfigurationToolInfoMetric,CLASS,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,ConfigurationToolInfoMetric.selected,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,ConfigurationToolInfoMetric.id,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,ConfigurationToolInfoMetric.toolName,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,ConfigurationToolInfoMetric.url,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,ConfigurationToolInfoMetric.userName,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,ConfigurationToolInfoMetric.password,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,ConfigurationToolInfoMetric.toolType,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,ConfigurationToolInfoMetric.widgetName,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,ConfigurationToolInfoMetric.jobName,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,ConfigurationToolInfoMetric.projectCode,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,ConfigurationToolInfoMetric.domain,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,ConfigurationToolInfoMetric.host,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,ConfigurationToolInfoMetric.port,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,ConfigurationToolInfoMetric.dbType,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,ConfigurationToolInfoMetric.schema,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,ConfigurationToolInfoMetric.repoName,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,ConfigurationToolInfoMetric.secret,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,ConfigurationToolInfoMetric.manualData,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,getId,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,setId,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setId,METHOD,setId.id,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,getToolName,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,setToolName,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setToolName,METHOD,setToolName.toolName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,getUrl,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,setUrl,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setUrl,METHOD,setUrl.url,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,getUserName,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,setUserName,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setUserName,METHOD,setUserName.userName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,getPassword,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,setPassword,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setPassword,METHOD,setPassword.password,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,getToolType,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,setToolType,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setToolType,METHOD,setToolType.toolType,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,getWidgetName,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,setWidgetName,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setWidgetName,METHOD,setWidgetName.widgetName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,getJobName,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,setJobName,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setJobName,METHOD,setJobName.jobName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,getProjectCode,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,setProjectCode,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setProjectCode,METHOD,setProjectCode.projectCode,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,getSelected,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,setSelected,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setSelected,METHOD,setSelected.selected,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,getDomain,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,setDomain,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setDomain,METHOD,setDomain.domain,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,getHost,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,setHost,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setHost,METHOD,setHost.host,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,getPort,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,setPort,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setPort,METHOD,setPort.port,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,getDbType,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,setDbType,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setDbType,METHOD,setDbType.dbType,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,getSchema,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,setSchema,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setSchema,METHOD,setSchema.schema,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,getRepoName,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,setRepoName,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setRepoName,METHOD,setRepoName.repoName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,getSecret,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
ConfigurationToolInfoMetric,CLASS,setSecret,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setSecret,METHOD,setSecret.secret,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setId,METHOD,setId.id,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setToolName,METHOD,setToolName.toolName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setUrl,METHOD,setUrl.url,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setUserName,METHOD,setUserName.userName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setPassword,METHOD,setPassword.password,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setToolType,METHOD,setToolType.toolType,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setWidgetName,METHOD,setWidgetName.widgetName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setJobName,METHOD,setJobName.jobName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setProjectCode,METHOD,setProjectCode.projectCode,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setSelected,METHOD,setSelected.selected,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setDomain,METHOD,setDomain.domain,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setHost,METHOD,setHost.host,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setPort,METHOD,setPort.port,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setDbType,METHOD,setDbType.dbType,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setSchema,METHOD,setSchema.schema,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setRepoName,METHOD,setRepoName.repoName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
setSecret,METHOD,setSecret.secret,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
BuildFailurePatternForProjectRepo.java,FILE,BuildFailurePatternForProjectRepo,INTERFACE,DECLARES,UnifiedBolt\core\repository\BuildFailurePatternForProjectRepo.java,UnifiedBolt
BuildFailurePatternForProjectRepo,INTERFACE,findByProjectName,METHOD,DECLARES,UnifiedBolt\core\repository\BuildFailurePatternForProjectRepo.java,UnifiedBolt
BuildFailurePatternForProjectRepo,INTERFACE,findAll,METHOD,DECLARES,UnifiedBolt\core\repository\BuildFailurePatternForProjectRepo.java,UnifiedBolt
BuildToolRep.java,FILE,BuildToolRep,INTERFACE,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
BuildToolRep,INTERFACE,findBybuildType,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
BuildToolRep,INTERFACE,countByBuildType,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
BuildToolRep,INTERFACE,findByBuildTypeAndName,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
BuildToolRep,INTERFACE,findByNameAndJobName,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
BuildToolRep,INTERFACE,getIdByBuildType,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
BuildToolRep,INTERFACE,findByBuildTypeAndNameAndTimestampBetween,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
BuildToolRep,INTERFACE,findByNameAndTimestampBetween,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
BuildToolRep,INTERFACE,findByName,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
BuildToolRep,INTERFACE,findOneByName,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
BuildToolRep,INTERFACE,findOneByNameOrderByBuildIDDesc,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
BuildToolRep,INTERFACE,findByBuildTypeAndRepoNameAndGroupNameOrderByBuildIDDesc,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
BuildToolRep,INTERFACE,findByNameAndBuildTypeIgnoreCase,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
BuildToolRep,INTERFACE,findByNameAndRepoNameAndBranchName,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
BuildToolRep,INTERFACE,findByNameAndDefinitionId,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
BuildToolRep,INTERFACE,findByBuildTypeAndRepoNameAndNameOrderByBuildIDDesc,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
BuildToolRep,INTERFACE,findByBuildTypeAndRepoNameAndNameOrderByTimestampDesc,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
ConfigurationSettingRep.java,FILE,ConfigurationSettingRep,INTERFACE,DECLARES,UnifiedBolt\core\repository\ConfigurationSettingRep.java,UnifiedBolt
ConfigurationSettingRep,INTERFACE,findByProjectName,METHOD,DECLARES,UnifiedBolt\core\repository\ConfigurationSettingRep.java,UnifiedBolt
ConfigurationSettingRep,INTERFACE,findByProjectNameIn,METHOD,DECLARES,UnifiedBolt\core\repository\ConfigurationSettingRep.java,UnifiedBolt
ConfigurationSettingRep,INTERFACE,deleteByProjectName,METHOD,DECLARES,UnifiedBolt\core\repository\ConfigurationSettingRep.java,UnifiedBolt
GithubAction.java,FILE,GithubAction,INTERFACE,DECLARES,UnifiedBolt\githubaction\GithubAction.java,UnifiedBolt
GithubAction,INTERFACE,getBuildToolData,METHOD,DECLARES,UnifiedBolt\githubaction\GithubAction.java,UnifiedBolt
GithubActionApplication.java,FILE,GithubActionApplication,CLASS,DECLARES,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
GithubActionApplication,CLASS,GithubActionApplication.LOGGER,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
GithubActionApplication,CLASS,GithubActionApplication.applicationContext,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
GithubActionApplication,CLASS,GithubActionApplication.repo,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
GithubActionApplication,CLASS,GithubActionApplication.githubActionMetrics,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
GithubActionApplication,CLASS,GithubActionApplication.result,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
GithubActionApplication,CLASS,GithubActionApplication.buildType,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
GithubActionApplication,CLASS,GithubActionApplication.pageLimit,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
GithubActionApplication,CLASS,GithubActionApplication.configurationRepo,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
GithubActionApplication,CLASS,GithubActionApplication.configuration,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
GithubActionApplication,CLASS,GithubActionApplication.metric,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
GithubActionApplication,CLASS,GithubActionApplication.metric1,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
GithubActionApplication,CLASS,githubActionMain,METHOD,DECLARES,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.applicationContext,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.repo,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.instanceURL,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.branch,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.username,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.password,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.repoName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.firstRun,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.apiToken,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.configurationRepo,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.configuration,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.metric,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.iter,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.githubActionMetrics,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.splitUrl,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.splitRepoName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.configuration1,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.metric1,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.instanceURL,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.username,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.password,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.repoName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.repoName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.projectName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.splitUrl,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.splitRepoName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.splitUrl,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.instanceURL,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.splitRepoName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.repoName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.result,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
GithubActionApplication,CLASS,cleanObject,METHOD,DECLARES,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
cleanObject,METHOD,cleanObject.repo,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
cleanObject,METHOD,cleanObject.githubActionMetrics,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
GithubActionApplication,CLASS,parseAsArray,METHOD,DECLARES,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
GithubActionApplication,CLASS,makeRestCall,METHOD,DECLARES,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
makeRestCall,METHOD,makeRestCall.requestFactory,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
makeRestCall,METHOD,makeRestCall.rest,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
makeRestCall,METHOD,makeRestCall.response,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.applicationContext,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.repo,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.instanceURL,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.branch,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.username,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.password,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.repoName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.firstRun,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.apiToken,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.configurationRepo,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.configuration,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.metric,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.iter,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.githubActionMetrics,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.splitUrl,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.splitRepoName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.configuration1,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.metric1,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.instanceURL,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.username,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.password,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.repoName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.repoName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.projectName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.splitUrl,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.splitRepoName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.splitUrl,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.instanceURL,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.splitRepoName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.repoName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubActionMain,METHOD,githubActionMain.result,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
cleanObject,METHOD,cleanObject.repo,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
cleanObject,METHOD,cleanObject.githubActionMetrics,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
makeRestCall,METHOD,makeRestCall.requestFactory,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
makeRestCall,METHOD,makeRestCall.rest,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
makeRestCall,METHOD,makeRestCall.response,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
GithubActionImplementation.java,FILE,GithubActionImplementation,CLASS,DECLARES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,GithubActionImplementation.LOGGER,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,GithubActionImplementation.SEGMENT_API,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,GithubActionImplementation.PUBLIC_GITHUB_REPO_HOST,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,GithubActionImplementation.PUBLIC_GITHUB_HOST_NAME,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,GithubActionImplementation.ctx,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,GithubActionImplementation.projectName,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,GithubActionImplementation.userName,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,GithubActionImplementation.password,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,GithubActionImplementation.time,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,GithubActionImplementation.buildRepo,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,GithubActionImplementation.jobCollection,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,GithubActionImplementation.pipelineUrl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,GithubActionImplementation.singlePipelineUrl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,GithubActionImplementation.githubUrl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,GithubActionImplementation.jobsUrl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,GithubActionImplementation.size,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,GithubActionImplementation.lastPage,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,GithubActionImplementation.lastBuildId,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,GithubActionImplementation.newbuildPipelineTimestamp,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,GithubActionImplementation.timestamp,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,GithubActionImplementation.page,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,GithubActionImplementation.per_page,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,GithubActionImplementation.totalPages,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,GithubActionImplementation.pipelineId,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,GithubActionImplementation.build,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,GithubActionImplementation.jobsList,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,GithubActionImplementation.failurePatternRepo,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,GithubActionImplementation.repoName,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,getBuildToolData,METHOD,DECLARES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.githubUrl,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.baseUrl,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.githubUrl,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.url,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.hostName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.protocol,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.port,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.url,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.hostName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.protocol,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.port,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.hostUrl,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.repoName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.githubUrl,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.githubUrl,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.projectName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.repo,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.repoName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.page,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.tool,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.delta,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.timeString,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.count,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.timeString,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.delta,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.response,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.runs,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.totalRuns,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.page,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.runsCollected,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.pipelineUrl,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.response,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.runs,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.valueArray,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.obj,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.builds,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.runsCollected,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,processPipelineData,METHOD,DECLARES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.builds,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.pipeline_Obj,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.timestamp,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.build,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.id,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.jobsUrl,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.jobResponse,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.createTime,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.createTime,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.updatedTime,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.updatedTime,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.completeTime,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.completeTime,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.durationMetric,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.duration,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.duration,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.resultMetric,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.timestampMetric,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.jobsArray1,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.jobsArray,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.singleJobsobj,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.commitObj,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.authorObj,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.jobsList,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.stepsObj,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.completeDate,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.completed,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.started,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.completeDate,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.completed,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.completeDate,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.completeDate,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.started,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.result,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,processFailure,METHOD,DECLARES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processFailure,METHOD,processFailure.ctx,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processFailure,METHOD,processFailure.failurePatternRepo,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processFailure,METHOD,processFailure.failurePattern,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processFailure,METHOD,processFailure.failureMetrics,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processFailure,METHOD,processFailure.newList,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processFailure,METHOD,processFailure.url,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processFailure,METHOD,processFailure.logResponseData,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processFailure,METHOD,processFailure.failureLog,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processFailure,METHOD,processFailure.flag,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processFailure,METHOD,processFailure.flag,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processFailure,METHOD,processFailure.b,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,getTimeInMiliseconds,METHOD,DECLARES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getTimeInMiliseconds,METHOD,getTimeInMiliseconds.splitDate,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getTimeInMiliseconds,METHOD,getTimeInMiliseconds.fmt,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getTimeInMiliseconds,METHOD,getTimeInMiliseconds.createdDate,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,makeRestCall,METHOD,DECLARES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,createHeaders,METHOD,DECLARES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
createHeaders,METHOD,createHeaders.auth,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
createHeaders,METHOD,createHeaders.encodedAuth,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
createHeaders,METHOD,createHeaders.authHeader,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
createHeaders,METHOD,createHeaders.headers,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
GithubActionImplementation,CLASS,get,METHOD,DECLARES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
get,METHOD,get.requestFactory,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.githubUrl,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.baseUrl,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.githubUrl,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.url,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.hostName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.protocol,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.port,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.url,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.hostName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.protocol,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.port,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.hostUrl,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.repoName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.githubUrl,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.githubUrl,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.projectName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.repo,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.repoName,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.page,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.tool,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.delta,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.timeString,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.count,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.timeString,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.delta,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.response,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.runs,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.totalRuns,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.page,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.runsCollected,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.pipelineUrl,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.response,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.runs,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.valueArray,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.obj,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.builds,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getBuildToolData,METHOD,getBuildToolData.runsCollected,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.builds,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.pipeline_Obj,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.timestamp,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.build,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.id,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.jobsUrl,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.jobResponse,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.createTime,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.createTime,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.updatedTime,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.updatedTime,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.completeTime,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.completeTime,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.durationMetric,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.duration,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.duration,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.resultMetric,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.timestampMetric,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.jobsArray1,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.jobsArray,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.singleJobsobj,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.commitObj,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.authorObj,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.jobsList,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.stepsObj,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.completeDate,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.completed,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.started,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.completeDate,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.completed,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.completeDate,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.completeDate,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.started,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processPipelineData,METHOD,processPipelineData.result,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processFailure,METHOD,processFailure.ctx,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processFailure,METHOD,processFailure.failurePatternRepo,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processFailure,METHOD,processFailure.failurePattern,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processFailure,METHOD,processFailure.failureMetrics,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processFailure,METHOD,processFailure.newList,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processFailure,METHOD,processFailure.url,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processFailure,METHOD,processFailure.logResponseData,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processFailure,METHOD,processFailure.failureLog,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processFailure,METHOD,processFailure.flag,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processFailure,METHOD,processFailure.flag,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
processFailure,METHOD,processFailure.b,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getTimeInMiliseconds,METHOD,getTimeInMiliseconds.splitDate,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getTimeInMiliseconds,METHOD,getTimeInMiliseconds.fmt,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
getTimeInMiliseconds,METHOD,getTimeInMiliseconds.createdDate,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
createHeaders,METHOD,createHeaders.auth,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
createHeaders,METHOD,createHeaders.encodedAuth,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
createHeaders,METHOD,createHeaders.authHeader,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
createHeaders,METHOD,createHeaders.headers,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
get,METHOD,get.requestFactory,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
