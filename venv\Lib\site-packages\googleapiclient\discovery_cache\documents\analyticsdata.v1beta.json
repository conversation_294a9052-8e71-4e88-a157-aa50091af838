{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/analytics": {"description": "View and manage your Google Analytics data"}, "https://www.googleapis.com/auth/analytics.readonly": {"description": "See and download your Google Analytics data"}}}}, "basePath": "", "baseUrl": "https://analyticsdata.googleapis.com/", "batchPath": "batch", "canonicalName": "AnalyticsData", "description": "Accesses report data in Google Analytics. Warning: Creating multiple Customer Applications, Accounts, or Projects to simulate or act as a single Customer Application, Account, or Project (respectively) or to circumvent Service-specific usage limits or quotas is a direct violation of Google Cloud Platform Terms of Service as well as Google APIs Terms of Service. These actions can result in immediate termination of your GCP project(s) without any warning. ", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/analytics/devguides/reporting/data/v1/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "analyticsdata:v1beta", "kind": "discovery#restDescription", "mtlsRootUrl": "https://analyticsdata.mtls.googleapis.com/", "name": "analyticsdata", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"properties": {"methods": {"batchRunPivotReports": {"description": "Returns multiple pivot reports in a batch. All reports must be for the same Google Analytics property.", "flatPath": "v1beta/properties/{propertiesId}:batchRunPivotReports", "httpMethod": "POST", "id": "analyticsdata.properties.batchRunPivotReports", "parameterOrder": ["property"], "parameters": {"property": {"description": "A Google Analytics property identifier whose events are tracked. Specified in the URL path and not the body. To learn more, see [where to find your Property ID](https://developers.google.com/analytics/devguides/reporting/data/v1/property-id). This property must be specified for the batch. The property within RunPivotReportRequest may either be unspecified or consistent with this property. Example: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+property}:batchRunPivotReports", "request": {"$ref": "BatchRunPivotReportsRequest"}, "response": {"$ref": "BatchRunPivotReportsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics", "https://www.googleapis.com/auth/analytics.readonly"]}, "batchRunReports": {"description": "Returns multiple reports in a batch. All reports must be for the same Google Analytics property.", "flatPath": "v1beta/properties/{propertiesId}:batchRunReports", "httpMethod": "POST", "id": "analyticsdata.properties.batchRunReports", "parameterOrder": ["property"], "parameters": {"property": {"description": "A Google Analytics property identifier whose events are tracked. Specified in the URL path and not the body. To learn more, see [where to find your Property ID](https://developers.google.com/analytics/devguides/reporting/data/v1/property-id). This property must be specified for the batch. The property within RunReportRequest may either be unspecified or consistent with this property. Example: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+property}:batchRunReports", "request": {"$ref": "BatchRunReportsRequest"}, "response": {"$ref": "BatchRunReportsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics", "https://www.googleapis.com/auth/analytics.readonly"]}, "checkCompatibility": {"description": "This compatibility method lists dimensions and metrics that can be added to a report request and maintain compatibility. This method fails if the request's dimensions and metrics are incompatible. In Google Analytics, reports fail if they request incompatible dimensions and/or metrics; in that case, you will need to remove dimensions and/or metrics from the incompatible report until the report is compatible. The Realtime and Core reports have different compatibility rules. This method checks compatibility for Core reports.", "flatPath": "v1beta/properties/{propertiesId}:checkCompatibility", "httpMethod": "POST", "id": "analyticsdata.properties.checkCompatibility", "parameterOrder": ["property"], "parameters": {"property": {"description": "A Google Analytics property identifier whose events are tracked. To learn more, see [where to find your Property ID](https://developers.google.com/analytics/devguides/reporting/data/v1/property-id). `property` should be the same value as in your `runReport` request. Example: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+property}:checkCompatibility", "request": {"$ref": "CheckCompatibilityRequest"}, "response": {"$ref": "CheckCompatibilityResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics", "https://www.googleapis.com/auth/analytics.readonly"]}, "getMetadata": {"description": "Returns metadata for dimensions and metrics available in reporting methods. Used to explore the dimensions and metrics. In this method, a Google Analytics property identifier is specified in the request, and the metadata response includes Custom dimensions and metrics as well as Universal metadata. For example if a custom metric with parameter name `levels_unlocked` is registered to a property, the Metadata response will contain `customEvent:levels_unlocked`. Universal metadata are dimensions and metrics applicable to any property such as `country` and `totalUsers`.", "flatPath": "v1beta/properties/{propertiesId}/metadata", "httpMethod": "GET", "id": "analyticsdata.properties.getMetadata", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the metadata to retrieve. This name field is specified in the URL path and not URL parameters. Property is a numeric Google Analytics property identifier. To learn more, see [where to find your Property ID](https://developers.google.com/analytics/devguides/reporting/data/v1/property-id). Example: properties/1234/metadata Set the Property ID to 0 for dimensions and metrics common to all properties. In this special mode, this method will not return custom dimensions and metrics.", "location": "path", "pattern": "^properties/[^/]+/metadata$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "<PERSON><PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/analytics", "https://www.googleapis.com/auth/analytics.readonly"]}, "runPivotReport": {"description": "Returns a customized pivot report of your Google Analytics event data. Pivot reports are more advanced and expressive formats than regular reports. In a pivot report, dimensions are only visible if they are included in a pivot. Multiple pivots can be specified to further dissect your data.", "flatPath": "v1beta/properties/{propertiesId}:runPivotReport", "httpMethod": "POST", "id": "analyticsdata.properties.runPivotReport", "parameterOrder": ["property"], "parameters": {"property": {"description": "A Google Analytics property identifier whose events are tracked. Specified in the URL path and not the body. To learn more, see [where to find your Property ID](https://developers.google.com/analytics/devguides/reporting/data/v1/property-id). Within a batch request, this property should either be unspecified or consistent with the batch-level property. Example: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+property}:runPivotReport", "request": {"$ref": "RunPivotReportRequest"}, "response": {"$ref": "RunPivotReportResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics", "https://www.googleapis.com/auth/analytics.readonly"]}, "runRealtimeReport": {"description": "Returns a customized report of realtime event data for your property. Events appear in realtime reports seconds after they have been sent to the Google Analytics. Realtime reports show events and usage data for the periods of time ranging from the present moment to 30 minutes ago (up to 60 minutes for Google Analytics 360 properties). For a guide to constructing realtime requests & understanding responses, see [Creating a Realtime Report](https://developers.google.com/analytics/devguides/reporting/data/v1/realtime-basics).", "flatPath": "v1beta/properties/{propertiesId}:runRealtimeReport", "httpMethod": "POST", "id": "analyticsdata.properties.runRealtimeReport", "parameterOrder": ["property"], "parameters": {"property": {"description": "A Google Analytics property identifier whose events are tracked. Specified in the URL path and not the body. To learn more, see [where to find your Property ID](https://developers.google.com/analytics/devguides/reporting/data/v1/property-id). Example: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+property}:runRealtimeReport", "request": {"$ref": "RunRealtimeReportRequest"}, "response": {"$ref": "RunRealtimeReportResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics", "https://www.googleapis.com/auth/analytics.readonly"]}, "runReport": {"description": "Returns a customized report of your Google Analytics event data. Reports contain statistics derived from data collected by the Google Analytics tracking code. The data returned from the API is as a table with columns for the requested dimensions and metrics. Metrics are individual measurements of user activity on your property, such as active users or event count. Dimensions break down metrics across some common criteria, such as country or event name. For a guide to constructing requests & understanding responses, see [Creating a Report](https://developers.google.com/analytics/devguides/reporting/data/v1/basics).", "flatPath": "v1beta/properties/{propertiesId}:runReport", "httpMethod": "POST", "id": "analyticsdata.properties.runReport", "parameterOrder": ["property"], "parameters": {"property": {"description": "A Google Analytics property identifier whose events are tracked. Specified in the URL path and not the body. To learn more, see [where to find your Property ID](https://developers.google.com/analytics/devguides/reporting/data/v1/property-id). Within a batch request, this property should either be unspecified or consistent with the batch-level property. Example: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+property}:runReport", "request": {"$ref": "RunReportRequest"}, "response": {"$ref": "RunReportResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics", "https://www.googleapis.com/auth/analytics.readonly"]}}, "resources": {"audienceExports": {"methods": {"create": {"description": "Creates an audience export for later retrieval. This method quickly returns the audience export's resource name and initiates a long running asynchronous request to form an audience export. To export the users in an audience export, first create the audience export through this method and then send the audience resource name to the `QueryAudienceExport` method. See [Creating an Audience Export](https://developers.google.com/analytics/devguides/reporting/data/v1/audience-list-basics) for an introduction to Audience Exports with examples. An audience export is a snapshot of the users currently in the audience at the time of audience export creation. Creating audience exports for one audience on different days will return different results as users enter and exit the audience. Audiences in Google Analytics 4 allow you to segment your users in the ways that are important to your business. To learn more, see https://support.google.com/analytics/answer/9267572. Audience exports contain the users in each audience. Audience Export APIs have some methods at alpha and other methods at beta stability. The intention is to advance methods to beta stability after some feedback and adoption. To give your feedback on this API, complete the [Google Analytics Audience Export API Feedback](https://forms.gle/EeA5u5LW6PEggtCEA) form.", "flatPath": "v1beta/properties/{propertiesId}/audienceExports", "httpMethod": "POST", "id": "analyticsdata.properties.audienceExports.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource where this audience export will be created. Format: `properties/{property}`", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/audienceExports", "request": {"$ref": "AudienceExport"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/analytics", "https://www.googleapis.com/auth/analytics.readonly"]}, "get": {"description": "Gets configuration metadata about a specific audience export. This method can be used to understand an audience export after it has been created. See [Creating an Audience Export](https://developers.google.com/analytics/devguides/reporting/data/v1/audience-list-basics) for an introduction to Audience Exports with examples. Audience Export APIs have some methods at alpha and other methods at beta stability. The intention is to advance methods to beta stability after some feedback and adoption. To give your feedback on this API, complete the [Google Analytics Audience Export API Feedback](https://forms.gle/EeA5u5LW6PEggtCEA) form.", "flatPath": "v1beta/properties/{propertiesId}/audienceExports/{audienceExportsId}", "httpMethod": "GET", "id": "analyticsdata.properties.audienceExports.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The audience export resource name. Format: `properties/{property}/audienceExports/{audience_export}`", "location": "path", "pattern": "^properties/[^/]+/audienceExports/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "AudienceExport"}, "scopes": ["https://www.googleapis.com/auth/analytics", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"description": "Lists all audience exports for a property. This method can be used for you to find and reuse existing audience exports rather than creating unnecessary new audience exports. The same audience can have multiple audience exports that represent the export of users that were in an audience on different days. See [Creating an Audience Export](https://developers.google.com/analytics/devguides/reporting/data/v1/audience-list-basics) for an introduction to Audience Exports with examples. Audience Export APIs have some methods at alpha and other methods at beta stability. The intention is to advance methods to beta stability after some feedback and adoption. To give your feedback on this API, complete the [Google Analytics Audience Export API Feedback](https://forms.gle/EeA5u5LW6PEggtCEA) form.", "flatPath": "v1beta/properties/{propertiesId}/audienceExports", "httpMethod": "GET", "id": "analyticsdata.properties.audienceExports.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of audience exports to return. The service may return fewer than this value. If unspecified, at most 200 audience exports will be returned. The maximum value is 1000 (higher values will be coerced to the maximum).", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListAudienceExports` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListAudienceExports` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. All audience exports for this property will be listed in the response. Format: `properties/{property}`", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/audienceExports", "response": {"$ref": "ListAudienceExportsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics", "https://www.googleapis.com/auth/analytics.readonly"]}, "query": {"description": "Retrieves an audience export of users. After creating an audience, the users are not immediately available for exporting. First, a request to `CreateAudienceExport` is necessary to create an audience export of users, and then second, this method is used to retrieve the users in the audience export. See [Creating an Audience Export](https://developers.google.com/analytics/devguides/reporting/data/v1/audience-list-basics) for an introduction to Audience Exports with examples. Audiences in Google Analytics 4 allow you to segment your users in the ways that are important to your business. To learn more, see https://support.google.com/analytics/answer/9267572. Audience Export APIs have some methods at alpha and other methods at beta stability. The intention is to advance methods to beta stability after some feedback and adoption. To give your feedback on this API, complete the [Google Analytics Audience Export API Feedback](https://forms.gle/EeA5u5LW6PEggtCEA) form.", "flatPath": "v1beta/properties/{propertiesId}/audienceExports/{audienceExportsId}:query", "httpMethod": "POST", "id": "analyticsdata.properties.audienceExports.query", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the audience export to retrieve users from. Format: `properties/{property}/audienceExports/{audience_export}`", "location": "path", "pattern": "^properties/[^/]+/audienceExports/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}:query", "request": {"$ref": "QueryAudienceExportRequest"}, "response": {"$ref": "QueryAudienceExportResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics", "https://www.googleapis.com/auth/analytics.readonly"]}}}}}}, "revision": "20241117", "rootUrl": "https://analyticsdata.googleapis.com/", "schemas": {"ActiveMetricRestriction": {"description": "A metric actively restricted in creating the report.", "id": "ActiveMetricRestriction", "properties": {"metricName": {"description": "The name of the restricted metric.", "type": "string"}, "restrictedMetricTypes": {"description": "The reason for this metric's restriction.", "items": {"enum": ["RESTRICTED_METRIC_TYPE_UNSPECIFIED", "COST_DATA", "REVENUE_DATA"], "enumDescriptions": ["Unspecified type.", "Cost metrics such as `adCost`.", "Revenue metrics such as `purchaseRevenue`."], "type": "string"}, "type": "array"}}, "type": "object"}, "AudienceExport": {"description": "An audience export is a list of users in an audience at the time of the list's creation. One audience may have multiple audience exports created for different days.", "id": "AudienceExport", "properties": {"audience": {"description": "Required. The audience resource name. This resource name identifies the audience being listed and is shared between the Analytics Data & Admin APIs. Format: `properties/{property}/audiences/{audience}`", "type": "string"}, "audienceDisplayName": {"description": "Output only. The descriptive display name for this audience. For example, \"Purchasers\".", "readOnly": true, "type": "string"}, "beginCreatingTime": {"description": "Output only. The time when CreateAudienceExport was called and the AudienceExport began the `CREATING` state.", "format": "google-datetime", "readOnly": true, "type": "string"}, "creationQuotaTokensCharged": {"description": "Output only. The total quota tokens charged during creation of the AudienceExport. Because this token count is based on activity from the `CREATING` state, this tokens charged will be fixed once an AudienceExport enters the `ACTIVE` or `FAILED` states.", "format": "int32", "readOnly": true, "type": "integer"}, "dimensions": {"description": "Required. The dimensions requested and displayed in the query response.", "items": {"$ref": "V1betaAudienceDimension"}, "type": "array"}, "errorMessage": {"description": "Output only. Error message is populated when an audience export fails during creation. A common reason for such a failure is quota exhaustion.", "readOnly": true, "type": "string"}, "name": {"description": "Output only. Identifier. The audience export resource name assigned during creation. This resource name identifies this `AudienceExport`. Format: `properties/{property}/audienceExports/{audience_export}`", "readOnly": true, "type": "string"}, "percentageCompleted": {"description": "Output only. The percentage completed for this audience export ranging between 0 to 100.", "format": "double", "readOnly": true, "type": "number"}, "rowCount": {"description": "Output only. The total number of rows in the AudienceExport result.", "format": "int32", "readOnly": true, "type": "integer"}, "state": {"description": "Output only. The current state for this AudienceExport.", "enum": ["STATE_UNSPECIFIED", "CREATING", "ACTIVE", "FAILED"], "enumDescriptions": ["Unspecified state will never be used.", "The AudienceExport is currently creating and will be available in the future. Creating occurs immediately after the CreateAudienceExport call.", "The AudienceExport is fully created and ready for querying. An AudienceExport is updated to active asynchronously from a request; this occurs some time (for example 15 minutes) after the initial create call.", "The AudienceExport failed to be created. It is possible that re-requesting this audience export will succeed."], "readOnly": true, "type": "string"}}, "type": "object"}, "AudienceListMetadata": {"description": "This metadata is currently blank.", "id": "AudienceListMetadata", "properties": {}, "type": "object"}, "BatchRunPivotReportsRequest": {"description": "The batch request containing multiple pivot report requests.", "id": "BatchRunPivotReportsRequest", "properties": {"requests": {"description": "Individual requests. Each request has a separate pivot report response. Each batch request is allowed up to 5 requests.", "items": {"$ref": "RunPivotReportRequest"}, "type": "array"}}, "type": "object"}, "BatchRunPivotReportsResponse": {"description": "The batch response containing multiple pivot reports.", "id": "BatchRunPivotReportsResponse", "properties": {"kind": {"description": "Identifies what kind of resource this message is. This `kind` is always the fixed string \"analyticsData#batchRunPivotReports\". Useful to distinguish between response types in JSON.", "type": "string"}, "pivotReports": {"description": "Individual responses. Each response has a separate pivot report request.", "items": {"$ref": "RunPivotReportResponse"}, "type": "array"}}, "type": "object"}, "BatchRunReportsRequest": {"description": "The batch request containing multiple report requests.", "id": "BatchRunReportsRequest", "properties": {"requests": {"description": "Individual requests. Each request has a separate report response. Each batch request is allowed up to 5 requests.", "items": {"$ref": "RunReportRequest"}, "type": "array"}}, "type": "object"}, "BatchRunReportsResponse": {"description": "The batch response containing multiple reports.", "id": "BatchRunReportsResponse", "properties": {"kind": {"description": "Identifies what kind of resource this message is. This `kind` is always the fixed string \"analyticsData#batchRunReports\". Useful to distinguish between response types in JSON.", "type": "string"}, "reports": {"description": "Individual responses. Each response has a separate report request.", "items": {"$ref": "RunReportResponse"}, "type": "array"}}, "type": "object"}, "BetweenFilter": {"description": "To express that the result needs to be between two numbers (inclusive).", "id": "BetweenFilter", "properties": {"fromValue": {"$ref": "NumericValue", "description": "Begins with this number."}, "toValue": {"$ref": "NumericValue", "description": "Ends with this number."}}, "type": "object"}, "CaseExpression": {"description": "Used to convert a dimension value to a single case.", "id": "CaseExpression", "properties": {"dimensionName": {"description": "Name of a dimension. The name must refer back to a name in dimensions field of the request.", "type": "string"}}, "type": "object"}, "CheckCompatibilityRequest": {"description": "The request for compatibility information for a report's dimensions and metrics. Check compatibility provides a preview of the compatibility of a report; fields shared with the `runReport` request should be the same values as in your `runReport` request.", "id": "CheckCompatibilityRequest", "properties": {"compatibilityFilter": {"description": "Filters the dimensions and metrics in the response to just this compatibility. Commonly used as `”compatibilityFilter”: “COMPATIBLE”` to only return compatible dimensions & metrics.", "enum": ["COMPATIBILITY_UNSPECIFIED", "COMPATIBLE", "INCOMPATIBLE"], "enumDescriptions": ["Unspecified compatibility.", "The dimension or metric is compatible. This dimension or metric can be successfully added to a report.", "The dimension or metric is incompatible. This dimension or metric cannot be successfully added to a report."], "type": "string"}, "dimensionFilter": {"$ref": "FilterExpression", "description": "The filter clause of dimensions. `dimensionFilter` should be the same value as in your `runReport` request."}, "dimensions": {"description": "The dimensions in this report. `dimensions` should be the same value as in your `runReport` request.", "items": {"$ref": "Dimension"}, "type": "array"}, "metricFilter": {"$ref": "FilterExpression", "description": "The filter clause of metrics. `metricFilter` should be the same value as in your `runReport` request"}, "metrics": {"description": "The metrics in this report. `metrics` should be the same value as in your `runReport` request.", "items": {"$ref": "Metric"}, "type": "array"}}, "type": "object"}, "CheckCompatibilityResponse": {"description": "The compatibility response with the compatibility of each dimension & metric.", "id": "CheckCompatibilityResponse", "properties": {"dimensionCompatibilities": {"description": "The compatibility of each dimension.", "items": {"$ref": "DimensionCompatibility"}, "type": "array"}, "metricCompatibilities": {"description": "The compatibility of each metric.", "items": {"$ref": "MetricCompatibility"}, "type": "array"}}, "type": "object"}, "Cohort": {"description": "Defines a cohort selection criteria. A cohort is a group of users who share a common characteristic. For example, users with the same `firstSessionDate` belong to the same cohort.", "id": "Cohort", "properties": {"dateRange": {"$ref": "DateRange", "description": "The cohort selects users whose first touch date is between start date and end date defined in the `dateRange`. This `dateRange` does not specify the full date range of event data that is present in a cohort report. In a cohort report, this `dateRange` is extended by the granularity and offset present in the `cohortsRange`; event data for the extended reporting date range is present in a cohort report. In a cohort request, this `dateRange` is required and the `dateRanges` in the `RunReportRequest` or `RunPivotReportRequest` must be unspecified. This `dateRange` should generally be aligned with the cohort's granularity. If `CohortsRange` uses daily granularity, this `dateRange` can be a single day. If `CohortsRange` uses weekly granularity, this `dateRange` can be aligned to a week boundary, starting at Sunday and ending Saturday. If `CohortsRange` uses monthly granularity, this `dateRange` can be aligned to a month, starting at the first and ending on the last day of the month."}, "dimension": {"description": "Dimension used by the cohort. Required and only supports `firstSessionDate`.", "type": "string"}, "name": {"description": "Assigns a name to this cohort. The dimension `cohort` is valued to this name in a report response. If set, cannot begin with `cohort_` or `RESERVED_`. If not set, cohorts are named by their zero based index `cohort_0`, `cohort_1`, etc.", "type": "string"}}, "type": "object"}, "CohortReportSettings": {"description": "Optional settings of a cohort report.", "id": "CohortReportSettings", "properties": {"accumulate": {"description": "If true, accumulates the result from first touch day to the end day. Not supported in `RunReportRequest`.", "type": "boolean"}}, "type": "object"}, "CohortSpec": {"description": "The specification of cohorts for a cohort report. Cohort reports create a time series of user retention for the cohort. For example, you could select the cohort of users that were acquired in the first week of September and follow that cohort for the next six weeks. Selecting the users acquired in the first week of September cohort is specified in the `cohort` object. Following that cohort for the next six weeks is specified in the `cohortsRange` object. For examples, see [Cohort Report Examples](https://developers.google.com/analytics/devguides/reporting/data/v1/advanced#cohort_report_examples). The report response could show a weekly time series where say your app has retained 60% of this cohort after three weeks and 25% of this cohort after six weeks. These two percentages can be calculated by the metric `cohortActiveUsers/cohortTotalUsers` and will be separate rows in the report.", "id": "CohortSpec", "properties": {"cohortReportSettings": {"$ref": "CohortReportSettings", "description": "Optional settings for a cohort report."}, "cohorts": {"description": "Defines the selection criteria to group users into cohorts. Most cohort reports define only a single cohort. If multiple cohorts are specified, each cohort can be recognized in the report by their name.", "items": {"$ref": "Cohort"}, "type": "array"}, "cohortsRange": {"$ref": "CohortsRange", "description": "Cohort reports follow cohorts over an extended reporting date range. This range specifies an offset duration to follow the cohorts over."}}, "type": "object"}, "CohortsRange": {"description": "Configures the extended reporting date range for a cohort report. Specifies an offset duration to follow the cohorts over.", "id": "CohortsRange", "properties": {"endOffset": {"description": "Required. `endOffset` specifies the end date of the extended reporting date range for a cohort report. `endOffset` can be any positive integer but is commonly set to 5 to 10 so that reports contain data on the cohort for the next several granularity time periods. If `granularity` is `DAILY`, the `endDate` of the extended reporting date range is `endDate` of the cohort plus `endOffset` days. If `granularity` is `WEEKLY`, the `endDate` of the extended reporting date range is `endDate` of the cohort plus `endOffset * 7` days. If `granularity` is `MONTHLY`, the `endDate` of the extended reporting date range is `endDate` of the cohort plus `endOffset * 30` days.", "format": "int32", "type": "integer"}, "granularity": {"description": "Required. The granularity used to interpret the `startOffset` and `endOffset` for the extended reporting date range for a cohort report.", "enum": ["GRANULARITY_UNSPECIFIED", "DAILY", "WEEKLY", "MONTHLY"], "enumDescriptions": ["Should never be specified.", "Daily granularity. Commonly used if the cohort's `dateRange` is a single day and the request contains `cohortNthDay`.", "Weekly granularity. Commonly used if the cohort's `dateRange` is a week in duration (starting on Sunday and ending on Saturday) and the request contains `cohortNthWeek`.", "Monthly granularity. Commonly used if the cohort's `dateRange` is a month in duration and the request contains `cohort<PERSON><PERSON><PERSON><PERSON><PERSON>`."], "type": "string"}, "startOffset": {"description": "`startOffset` specifies the start date of the extended reporting date range for a cohort report. `startOffset` is commonly set to 0 so that reports contain data from the acquisition of the cohort forward. If `granularity` is `DAILY`, the `startDate` of the extended reporting date range is `startDate` of the cohort plus `startOffset` days. If `granularity` is `WEEKLY`, the `startDate` of the extended reporting date range is `startDate` of the cohort plus `startOffset * 7` days. If `granularity` is `MONTHLY`, the `startDate` of the extended reporting date range is `startDate` of the cohort plus `startOffset * 30` days.", "format": "int32", "type": "integer"}}, "type": "object"}, "Comparison": {"description": "Defines an individual comparison. Most requests will include multiple comparisons so that the report compares between the comparisons.", "id": "Comparison", "properties": {"comparison": {"description": "A saved comparison identified by the comparison's resource name. For example, 'comparisons/1234'.", "type": "string"}, "dimensionFilter": {"$ref": "FilterExpression", "description": "A basic comparison."}, "name": {"description": "Each comparison produces separate rows in the response. In the response, this comparison is identified by this name. If name is unspecified, we will use the saved comparisons display name.", "type": "string"}}, "type": "object"}, "ComparisonMetadata": {"description": "The metadata for a single comparison.", "id": "ComparisonMetadata", "properties": {"apiName": {"description": "This comparison's resource name. Useable in [Comparison](#Comparison)'s `comparison` field. For example, 'comparisons/1234'.", "type": "string"}, "description": {"description": "This comparison's description.", "type": "string"}, "uiName": {"description": "This comparison's name within the Google Analytics user interface.", "type": "string"}}, "type": "object"}, "ConcatenateExpression": {"description": "Used to combine dimension values to a single dimension.", "id": "ConcatenateExpression", "properties": {"delimiter": {"description": "The delimiter placed between dimension names. Delimiters are often single characters such as \"|\" or \",\" but can be longer strings. If a dimension value contains the delimiter, both will be present in response with no distinction. For example if dimension 1 value = \"US,FR\", dimension 2 value = \"JP\", and delimiter = \",\", then the response will contain \"US,FR,JP\".", "type": "string"}, "dimensionNames": {"description": "Names of dimensions. The names must refer back to names in the dimensions field of the request.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "DateRange": {"description": "A contiguous set of days: `startDate`, `startDate + 1`, ..., `endDate`. Requests are allowed up to 4 date ranges.", "id": "DateRange", "properties": {"endDate": {"description": "The inclusive end date for the query in the format `YYYY-MM-DD`. Cannot be before `start_date`. The format `NdaysAgo`, `yesterday`, or `today` is also accepted, and in that case, the date is inferred based on the property's reporting time zone.", "type": "string"}, "name": {"description": "Assigns a name to this date range. The dimension `dateRange` is valued to this name in a report response. If set, cannot begin with `date_range_` or `RESERVED_`. If not set, date ranges are named by their zero based index in the request: `date_range_0`, `date_range_1`, etc.", "type": "string"}, "startDate": {"description": "The inclusive start date for the query in the format `YYYY-MM-DD`. Cannot be after `end_date`. The format `NdaysAgo`, `yesterday`, or `today` is also accepted, and in that case, the date is inferred based on the property's reporting time zone.", "type": "string"}}, "type": "object"}, "Dimension": {"description": "Dimensions are attributes of your data. For example, the dimension city indicates the city from which an event originates. Dimension values in report responses are strings; for example, the city could be \"Paris\" or \"New York\". Requests are allowed up to 9 dimensions.", "id": "Dimension", "properties": {"dimensionExpression": {"$ref": "DimensionExpression", "description": "One dimension can be the result of an expression of multiple dimensions. For example, dimension \"country, city\": concatenate(country, \", \", city)."}, "name": {"description": "The name of the dimension. See the [API Dimensions](https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema#dimensions) for the list of dimension names supported by core reporting methods such as `runReport` and `batchRunReports`. See [Realtime Dimensions](https://developers.google.com/analytics/devguides/reporting/data/v1/realtime-api-schema#dimensions) for the list of dimension names supported by the `runRealtimeReport` method. See [Funnel Dimensions](https://developers.google.com/analytics/devguides/reporting/data/v1/exploration-api-schema#dimensions) for the list of dimension names supported by the `runFunnelReport` method. If `dimensionExpression` is specified, `name` can be any string that you would like within the allowed character set. For example if a `dimensionExpression` concatenates `country` and `city`, you could call that dimension `countryAndCity`. Dimension names that you choose must match the regular expression `^[a-zA-Z0-9_]$`. Dimensions are referenced by `name` in `dimensionFilter`, `orderBys`, `dimensionExpression`, and `pivots`.", "type": "string"}}, "type": "object"}, "DimensionCompatibility": {"description": "The compatibility for a single dimension.", "id": "DimensionCompatibility", "properties": {"compatibility": {"description": "The compatibility of this dimension. If the compatibility is COMPATIBLE, this dimension can be successfully added to the report.", "enum": ["COMPATIBILITY_UNSPECIFIED", "COMPATIBLE", "INCOMPATIBLE"], "enumDescriptions": ["Unspecified compatibility.", "The dimension or metric is compatible. This dimension or metric can be successfully added to a report.", "The dimension or metric is incompatible. This dimension or metric cannot be successfully added to a report."], "type": "string"}, "dimensionMetadata": {"$ref": "DimensionMetadata", "description": "The dimension metadata contains the API name for this compatibility information. The dimension metadata also contains other helpful information like the UI name and description."}}, "type": "object"}, "DimensionExpression": {"description": "Used to express a dimension which is the result of a formula of multiple dimensions. Example usages: 1) lower_case(dimension) 2) concatenate(dimension1, symbol, dimension2).", "id": "DimensionExpression", "properties": {"concatenate": {"$ref": "ConcatenateExpression", "description": "Used to combine dimension values to a single dimension. For example, dimension \"country, city\": concatenate(country, \", \", city)."}, "lowerCase": {"$ref": "CaseExpression", "description": "Used to convert a dimension value to lower case."}, "upperCase": {"$ref": "CaseExpression", "description": "Used to convert a dimension value to upper case."}}, "type": "object"}, "DimensionHeader": {"description": "Describes a dimension column in the report. Dimensions requested in a report produce column entries within rows and DimensionHeaders. However, dimensions used exclusively within filters or expressions do not produce columns in a report; correspondingly, those dimensions do not produce headers.", "id": "DimensionHeader", "properties": {"name": {"description": "The dimension's name.", "type": "string"}}, "type": "object"}, "DimensionMetadata": {"description": "Explains a dimension.", "id": "DimensionMetadata", "properties": {"apiName": {"description": "This dimension's name. Useable in [Dimension](#Dimension)'s `name`. For example, `eventName`.", "type": "string"}, "category": {"description": "The display name of the category that this dimension belongs to. Similar dimensions and metrics are categorized together.", "type": "string"}, "customDefinition": {"description": "True if the dimension is custom to this property. This includes user, event, & item scoped custom dimensions; to learn more about custom dimensions, see https://support.google.com/analytics/answer/14240153. This also include custom channel groups; to learn more about custom channel groups, see https://support.google.com/analytics/answer/13051316.", "type": "boolean"}, "deprecatedApiNames": {"description": "Still usable but deprecated names for this dimension. If populated, this dimension is available by either `apiName` or one of `deprecatedApiNames` for a period of time. After the deprecation period, the dimension will be available only by `apiName`.", "items": {"type": "string"}, "type": "array"}, "description": {"description": "Description of how this dimension is used and calculated.", "type": "string"}, "uiName": {"description": "This dimension's name within the Google Analytics user interface. For example, `Event name`.", "type": "string"}}, "type": "object"}, "DimensionOrderBy": {"description": "Sorts by dimension values.", "id": "DimensionOrderBy", "properties": {"dimensionName": {"description": "A dimension name in the request to order by.", "type": "string"}, "orderType": {"description": "Controls the rule for dimension value ordering.", "enum": ["ORDER_TYPE_UNSPECIFIED", "ALPHANUMERIC", "CASE_INSENSITIVE_ALPHANUMERIC", "NUMERIC"], "enumDescriptions": ["Unspecified.", "Alphanumeric sort by Unicode code point. For example, \"2\" < \"A\" < \"X\" < \"b\" < \"z\".", "Case insensitive alphanumeric sort by lower case Unicode code point. For example, \"2\" < \"A\" < \"b\" < \"X\" < \"z\".", "Dimension values are converted to numbers before sorting. For example in NUMERIC sort, \"25\" < \"100\", and in `ALPHANUMERIC` sort, \"100\" < \"25\". Non-numeric dimension values all have equal ordering value below all numeric values."], "type": "string"}}, "type": "object"}, "DimensionValue": {"description": "The value of a dimension.", "id": "DimensionValue", "properties": {"value": {"description": "Value as a string if the dimension type is a string.", "type": "string"}}, "type": "object"}, "EmptyFilter": {"description": "Filter for empty values.", "id": "EmptyFilter", "properties": {}, "type": "object"}, "Filter": {"description": "An expression to filter dimension or metric values.", "id": "Filter", "properties": {"betweenFilter": {"$ref": "BetweenFilter", "description": "A filter for two values."}, "emptyFilter": {"$ref": "EmptyFilter", "description": "A filter for empty values such as \"(not set)\" and \"\" values."}, "fieldName": {"description": "The dimension name or metric name. In most methods, dimensions & metrics can be used for the first time in this field. However in a RunPivotReportRequest, this field must be additionally specified by name in the RunPivotReportRequest's dimensions or metrics.", "type": "string"}, "inListFilter": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "A filter for in list values."}, "numericFilter": {"$ref": "NumericFilter", "description": "A filter for numeric or date values."}, "stringFilter": {"$ref": "StringFilter", "description": "Strings related filter."}}, "type": "object"}, "FilterExpression": {"description": "To express dimension or metric filters. The fields in the same FilterExpression need to be either all dimensions or all metrics.", "id": "FilterExpression", "properties": {"andGroup": {"$ref": "FilterExpressionList", "description": "The FilterExpressions in and_group have an AND relationship."}, "filter": {"$ref": "Filter", "description": "A primitive filter. In the same FilterExpression, all of the filter's field names need to be either all dimensions or all metrics."}, "notExpression": {"$ref": "FilterExpression", "description": "The FilterExpression is NOT of not_expression."}, "orGroup": {"$ref": "FilterExpressionList", "description": "The FilterExpressions in or_group have an OR relationship."}}, "type": "object"}, "FilterExpressionList": {"description": "A list of filter expressions.", "id": "FilterExpressionList", "properties": {"expressions": {"description": "A list of filter expressions.", "items": {"$ref": "FilterExpression"}, "type": "array"}}, "type": "object"}, "InListFilter": {"description": "The result needs to be in a list of string values.", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {"caseSensitive": {"description": "If true, the string value is case sensitive.", "type": "boolean"}, "values": {"description": "The list of string values. Must be non-empty.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListAudienceExportsResponse": {"description": "A list of all audience exports for a property.", "id": "ListAudienceExportsResponse", "properties": {"audienceExports": {"description": "Each audience export for a property.", "items": {"$ref": "AudienceExport"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "Metadata": {"description": "The dimensions, metrics and comparisons currently accepted in reporting methods.", "id": "<PERSON><PERSON><PERSON>", "properties": {"comparisons": {"description": "The comparison descriptions.", "items": {"$ref": "ComparisonMetadata"}, "type": "array"}, "dimensions": {"description": "The dimension descriptions.", "items": {"$ref": "DimensionMetadata"}, "type": "array"}, "metrics": {"description": "The metric descriptions.", "items": {"$ref": "MetricMetadata"}, "type": "array"}, "name": {"description": "Resource name of this metadata.", "type": "string"}}, "type": "object"}, "Metric": {"description": "The quantitative measurements of a report. For example, the metric `eventCount` is the total number of events. Requests are allowed up to 10 metrics.", "id": "Metric", "properties": {"expression": {"description": "A mathematical expression for derived metrics. For example, the metric Event count per user is `eventCount/totalUsers`.", "type": "string"}, "invisible": {"description": "Indicates if a metric is invisible in the report response. If a metric is invisible, the metric will not produce a column in the response, but can be used in `metricFilter`, `orderBys`, or a metric `expression`.", "type": "boolean"}, "name": {"description": "The name of the metric. See the [API Metrics](https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema#metrics) for the list of metric names supported by core reporting methods such as `runReport` and `batchRunReports`. See [Realtime Metrics](https://developers.google.com/analytics/devguides/reporting/data/v1/realtime-api-schema#metrics) for the list of metric names supported by the `runRealtimeReport` method. See [Funnel Metrics](https://developers.google.com/analytics/devguides/reporting/data/v1/exploration-api-schema#metrics) for the list of metric names supported by the `runFunnelReport` method. If `expression` is specified, `name` can be any string that you would like within the allowed character set. For example if `expression` is `screenPageViews/sessions`, you could call that metric's name = `viewsPerSession`. Metric names that you choose must match the regular expression `^[a-zA-Z0-9_]$`. Metrics are referenced by `name` in `metricFilter`, `orderBys`, and metric `expression`.", "type": "string"}}, "type": "object"}, "MetricCompatibility": {"description": "The compatibility for a single metric.", "id": "MetricCompatibility", "properties": {"compatibility": {"description": "The compatibility of this metric. If the compatibility is COMPATIBLE, this metric can be successfully added to the report.", "enum": ["COMPATIBILITY_UNSPECIFIED", "COMPATIBLE", "INCOMPATIBLE"], "enumDescriptions": ["Unspecified compatibility.", "The dimension or metric is compatible. This dimension or metric can be successfully added to a report.", "The dimension or metric is incompatible. This dimension or metric cannot be successfully added to a report."], "type": "string"}, "metricMetadata": {"$ref": "MetricMetadata", "description": "The metric metadata contains the API name for this compatibility information. The metric metadata also contains other helpful information like the UI name and description."}}, "type": "object"}, "MetricHeader": {"description": "Describes a metric column in the report. Visible metrics requested in a report produce column entries within rows and MetricHeaders. However, metrics used exclusively within filters or expressions do not produce columns in a report; correspondingly, those metrics do not produce headers.", "id": "Metric<PERSON><PERSON><PERSON>", "properties": {"name": {"description": "The metric's name.", "type": "string"}, "type": {"description": "The metric's data type.", "enum": ["METRIC_TYPE_UNSPECIFIED", "TYPE_INTEGER", "TYPE_FLOAT", "TYPE_SECONDS", "TYPE_MILLISECONDS", "TYPE_MINUTES", "TYPE_HOURS", "TYPE_STANDARD", "TYPE_CURRENCY", "TYPE_FEET", "TYPE_MILES", "TYPE_METERS", "TYPE_KILOMETERS"], "enumDescriptions": ["Unspecified type.", "Integer type.", "Floating point type.", "A duration of seconds; a special floating point type.", "A duration in milliseconds; a special floating point type.", "A duration in minutes; a special floating point type.", "A duration in hours; a special floating point type.", "A custom metric of standard type; a special floating point type.", "An amount of money; a special floating point type.", "A length in feet; a special floating point type.", "A length in miles; a special floating point type.", "A length in meters; a special floating point type.", "A length in kilometers; a special floating point type."], "type": "string"}}, "type": "object"}, "MetricMetadata": {"description": "Explains a metric.", "id": "MetricMetadata", "properties": {"apiName": {"description": "A metric name. Useable in [Metric](#Metric)'s `name`. For example, `eventCount`.", "type": "string"}, "blockedReasons": {"description": "If reasons are specified, your access is blocked to this metric for this property. API requests from you to this property for this metric will succeed; however, the report will contain only zeros for this metric. API requests with metric filters on blocked metrics will fail. If reasons are empty, you have access to this metric. To learn more, see [Access and data-restriction management](https://support.google.com/analytics/answer/10851388).", "items": {"enum": ["BLOCKED_REASON_UNSPECIFIED", "NO_REVENUE_METRICS", "NO_COST_METRICS"], "enumDescriptions": ["Will never be specified in API response.", "If present, your access is blocked to revenue related metrics for this property, and this metric is revenue related.", "If present, your access is blocked to cost related metrics for this property, and this metric is cost related."], "type": "string"}, "type": "array"}, "category": {"description": "The display name of the category that this metrics belongs to. Similar dimensions and metrics are categorized together.", "type": "string"}, "customDefinition": {"description": "True if the metric is a custom metric for this property.", "type": "boolean"}, "deprecatedApiNames": {"description": "Still usable but deprecated names for this metric. If populated, this metric is available by either `apiName` or one of `deprecatedApiNames` for a period of time. After the deprecation period, the metric will be available only by `apiName`.", "items": {"type": "string"}, "type": "array"}, "description": {"description": "Description of how this metric is used and calculated.", "type": "string"}, "expression": {"description": "The mathematical expression for this derived metric. Can be used in [Metric](#Metric)'s `expression` field for equivalent reports. Most metrics are not expressions, and for non-expressions, this field is empty.", "type": "string"}, "type": {"description": "The type of this metric.", "enum": ["METRIC_TYPE_UNSPECIFIED", "TYPE_INTEGER", "TYPE_FLOAT", "TYPE_SECONDS", "TYPE_MILLISECONDS", "TYPE_MINUTES", "TYPE_HOURS", "TYPE_STANDARD", "TYPE_CURRENCY", "TYPE_FEET", "TYPE_MILES", "TYPE_METERS", "TYPE_KILOMETERS"], "enumDescriptions": ["Unspecified type.", "Integer type.", "Floating point type.", "A duration of seconds; a special floating point type.", "A duration in milliseconds; a special floating point type.", "A duration in minutes; a special floating point type.", "A duration in hours; a special floating point type.", "A custom metric of standard type; a special floating point type.", "An amount of money; a special floating point type.", "A length in feet; a special floating point type.", "A length in miles; a special floating point type.", "A length in meters; a special floating point type.", "A length in kilometers; a special floating point type."], "type": "string"}, "uiName": {"description": "This metric's name within the Google Analytics user interface. For example, `Event count`.", "type": "string"}}, "type": "object"}, "MetricOrderBy": {"description": "Sorts by metric values.", "id": "MetricOrderBy", "properties": {"metricName": {"description": "A metric name in the request to order by.", "type": "string"}}, "type": "object"}, "MetricValue": {"description": "The value of a metric.", "id": "MetricValue", "properties": {"value": {"description": "Measurement value. See MetricHeader for type.", "type": "string"}}, "type": "object"}, "MinuteRange": {"description": "A contiguous set of minutes: `startMinutesAgo`, `startMinutesAgo + 1`, ..., `endMinutesAgo`. Requests are allowed up to 2 minute ranges.", "id": "MinuteRange", "properties": {"endMinutesAgo": {"description": "The inclusive end minute for the query as a number of minutes before now. Cannot be before `startMinutesAgo`. For example, `\"endMinutesAgo\": 15` specifies the report should include event data from prior to 15 minutes ago. If unspecified, `endMinutesAgo` is defaulted to 0. Standard Analytics properties can request any minute in the last 30 minutes of event data (`endMinutesAgo <= 29`), and 360 Analytics properties can request any minute in the last 60 minutes of event data (`endMinutesAgo <= 59`).", "format": "int32", "type": "integer"}, "name": {"description": "Assigns a name to this minute range. The dimension `dateRange` is valued to this name in a report response. If set, cannot begin with `date_range_` or `RESERVED_`. If not set, minute ranges are named by their zero based index in the request: `date_range_0`, `date_range_1`, etc.", "type": "string"}, "startMinutesAgo": {"description": "The inclusive start minute for the query as a number of minutes before now. For example, `\"startMinutesAgo\": 29` specifies the report should include event data from 29 minutes ago and after. Cannot be after `endMinutesAgo`. If unspecified, `startMinutesAgo` is defaulted to 29. Standard Analytics properties can request up to the last 30 minutes of event data (`startMinutesAgo <= 29`), and 360 Analytics properties can request up to the last 60 minutes of event data (`startMinutesAgo <= 59`).", "format": "int32", "type": "integer"}}, "type": "object"}, "NumericFilter": {"description": "Filters for numeric or date values.", "id": "NumericFilter", "properties": {"operation": {"description": "The operation type for this filter.", "enum": ["OPERATION_UNSPECIFIED", "EQUAL", "LESS_THAN", "LESS_THAN_OR_EQUAL", "GREATER_THAN", "GREATER_THAN_OR_EQUAL"], "enumDescriptions": ["Unspecified.", "Equal", "Less than", "Less than or equal", "Greater than", "Greater than or equal"], "type": "string"}, "value": {"$ref": "NumericValue", "description": "A numeric value or a date value."}}, "type": "object"}, "NumericValue": {"description": "To represent a number.", "id": "NumericValue", "properties": {"doubleValue": {"description": "Double value", "format": "double", "type": "number"}, "int64Value": {"description": "Integer value", "format": "int64", "type": "string"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "OrderBy": {"description": "Order bys define how rows will be sorted in the response. For example, ordering rows by descending event count is one ordering, and ordering rows by the event name string is a different ordering.", "id": "OrderBy", "properties": {"desc": {"description": "If true, sorts by descending order.", "type": "boolean"}, "dimension": {"$ref": "DimensionOrderBy", "description": "Sorts results by a dimension's values."}, "metric": {"$ref": "MetricOrderBy", "description": "Sorts results by a metric's values."}, "pivot": {"$ref": "PivotOrderBy", "description": "Sorts results by a metric's values within a pivot column group."}}, "type": "object"}, "Pivot": {"description": "Describes the visible dimension columns and rows in the report response.", "id": "Pivot", "properties": {"fieldNames": {"description": "Dimension names for visible columns in the report response. Including \"dateRange\" produces a date range column; for each row in the response, dimension values in the date range column will indicate the corresponding date range from the request.", "items": {"type": "string"}, "type": "array"}, "limit": {"description": "The number of unique combinations of dimension values to return in this pivot. The `limit` parameter is required. A `limit` of 10,000 is common for single pivot requests. The product of the `limit` for each `pivot` in a `RunPivotReportRequest` must not exceed 250,000. For example, a two pivot request with `limit: 1000` in each pivot will fail because the product is `1,000,000`.", "format": "int64", "type": "string"}, "metricAggregations": {"description": "Aggregate the metrics by dimensions in this pivot using the specified metric_aggregations.", "items": {"enum": ["METRIC_AGGREGATION_UNSPECIFIED", "TOTAL", "MINIMUM", "MAXIMUM", "COUNT"], "enumDescriptions": ["Unspecified operator.", "SUM operator.", "Minimum operator.", "Maximum operator.", "Count operator."], "type": "string"}, "type": "array"}, "offset": {"description": "The row count of the start row. The first row is counted as row 0.", "format": "int64", "type": "string"}, "orderBys": {"description": "Specifies how dimensions are ordered in the pivot. In the first Pivot, the OrderBys determine Row and PivotDimensionHeader ordering; in subsequent Pivots, the OrderBys determine only PivotDimensionHeader ordering. Dimensions specified in these OrderBys must be a subset of Pivot.field_names.", "items": {"$ref": "OrderBy"}, "type": "array"}}, "type": "object"}, "PivotDimensionHeader": {"description": "Summarizes dimension values from a row for this pivot.", "id": "PivotDimensionHeader", "properties": {"dimensionValues": {"description": "Values of multiple dimensions in a pivot.", "items": {"$ref": "DimensionValue"}, "type": "array"}}, "type": "object"}, "PivotHeader": {"description": "Dimensions' values in a single pivot.", "id": "PivotHeader", "properties": {"pivotDimensionHeaders": {"description": "The size is the same as the cardinality of the corresponding dimension combinations.", "items": {"$ref": "PivotDimensionHeader"}, "type": "array"}, "rowCount": {"description": "The cardinality of the pivot. The total number of rows for this pivot's fields regardless of how the parameters `offset` and `limit` are specified in the request.", "format": "int32", "type": "integer"}}, "type": "object"}, "PivotOrderBy": {"description": "Sorts by a pivot column group.", "id": "PivotOrderBy", "properties": {"metricName": {"description": "In the response to order by, order rows by this column. Must be a metric name from the request.", "type": "string"}, "pivotSelections": {"description": "Used to select a dimension name and value pivot. If multiple pivot selections are given, the sort occurs on rows where all pivot selection dimension name and value pairs match the row's dimension name and value pair.", "items": {"$ref": "PivotSelection"}, "type": "array"}}, "type": "object"}, "PivotSelection": {"description": "A pair of dimension names and values. Rows with this dimension pivot pair are ordered by the metric's value. For example if pivots = {{\"browser\", \"Chrome\"}} and metric_name = \"Sessions\", then the rows will be sorted based on Sessions in Chrome. ---------|----------|----------------|----------|---------------- | Chrome | Chrome | Safari | Safari ---------|----------|----------------|----------|---------------- Country | Sessions | Pages/Sessions | Sessions | Pages/Sessions ---------|----------|----------------|----------|---------------- US | 2 | 2 | 3 | 1 ---------|----------|----------------|----------|---------------- Canada | 3 | 1 | 4 | 1 ---------|----------|----------------|----------|----------------", "id": "PivotSelection", "properties": {"dimensionName": {"description": "Must be a dimension name from the request.", "type": "string"}, "dimensionValue": {"description": "Order by only when the named dimension is this value.", "type": "string"}}, "type": "object"}, "PropertyQuota": {"description": "Current state of all quotas for this Analytics Property. If any quota for a property is exhausted, all requests to that property will return Resource Exhausted errors.", "id": "PropertyQuota", "properties": {"concurrentRequests": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Standard Analytics Properties can send up to 10 concurrent requests; Analytics 360 Properties can use up to 50 concurrent requests."}, "potentiallyThresholdedRequestsPerHour": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Analytics Properties can send up to 120 requests with potentially thresholded dimensions per hour. In a batch request, each report request is individually counted for this quota if the request contains potentially thresholded dimensions."}, "serverErrorsPerProjectPerHour": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Standard Analytics Properties and cloud project pairs can have up to 10 server errors per hour; Analytics 360 Properties and cloud project pairs can have up to 50 server errors per hour."}, "tokensPerDay": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Standard Analytics Properties can use up to 200,000 tokens per day; Analytics 360 Properties can use 2,000,000 tokens per day. Most requests consume fewer than 10 tokens."}, "tokensPerHour": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Standard Analytics Properties can use up to 40,000 tokens per hour; Analytics 360 Properties can use 400,000 tokens per hour. An API request consumes a single number of tokens, and that number is deducted from all of the hourly, daily, and per project hourly quotas."}, "tokensPerProjectPerHour": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Analytics Properties can use up to 35% of their tokens per project per hour. This amounts to standard Analytics Properties can use up to 14,000 tokens per project per hour, and Analytics 360 Properties can use 140,000 tokens per project per hour. An API request consumes a single number of tokens, and that number is deducted from all of the hourly, daily, and per project hourly quotas."}}, "type": "object"}, "QueryAudienceExportRequest": {"description": "A request to list users in an audience export.", "id": "QueryAudienceExportRequest", "properties": {"limit": {"description": "Optional. The number of rows to return. If unspecified, 10,000 rows are returned. The API returns a maximum of 250,000 rows per request, no matter how many you ask for. `limit` must be positive. The API can also return fewer rows than the requested `limit`, if there aren't as many dimension values as the `limit`. To learn more about this pagination parameter, see [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).", "format": "int64", "type": "string"}, "offset": {"description": "Optional. The row count of the start row. The first row is counted as row 0. When paging, the first request does not specify offset; or equivalently, sets offset to 0; the first request returns the first `limit` of rows. The second request sets offset to the `limit` of the first request; the second request returns the second `limit` of rows. To learn more about this pagination parameter, see [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).", "format": "int64", "type": "string"}}, "type": "object"}, "QueryAudienceExportResponse": {"description": "A list of users in an audience export.", "id": "QueryAudienceExportResponse", "properties": {"audienceExport": {"$ref": "AudienceExport", "description": "Configuration data about AudienceExport being queried. Returned to help interpret the audience rows in this response. For example, the dimensions in this AudienceExport correspond to the columns in the AudienceRows."}, "audienceRows": {"description": "Rows for each user in an audience export. The number of rows in this response will be less than or equal to request's page size.", "items": {"$ref": "V1betaAudienceRow"}, "type": "array"}, "rowCount": {"description": "The total number of rows in the AudienceExport result. `rowCount` is independent of the number of rows returned in the response, the `limit` request parameter, and the `offset` request parameter. For example if a query returns 175 rows and includes `limit` of 50 in the API request, the response will contain `rowCount` of 175 but only 50 rows. To learn more about this pagination parameter, see [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).", "format": "int32", "type": "integer"}}, "type": "object"}, "QuotaStatus": {"description": "Current state for a particular quota group.", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {"consumed": {"description": "<PERSON><PERSON><PERSON> consumed by this request.", "format": "int32", "type": "integer"}, "remaining": {"description": "<PERSON><PERSON><PERSON> remaining after this request.", "format": "int32", "type": "integer"}}, "type": "object"}, "ResponseMetaData": {"description": "Response's metadata carrying additional information about the report content.", "id": "ResponseMetaData", "properties": {"currencyCode": {"description": "The currency code used in this report. Intended to be used in formatting currency metrics like `purchaseRevenue` for visualization. If currency_code was specified in the request, this response parameter will echo the request parameter; otherwise, this response parameter is the property's current currency_code. Currency codes are string encodings of currency types from the ISO 4217 standard (https://en.wikipedia.org/wiki/ISO_4217); for example \"USD\", \"EUR\", \"JPY\". To learn more, see https://support.google.com/analytics/answer/9796179.", "type": "string"}, "dataLossFromOtherRow": {"description": "If true, indicates some buckets of dimension combinations are rolled into \"(other)\" row. This can happen for high cardinality reports. The metadata parameter dataLossFromOtherRow is populated based on the aggregated data table used in the report. The parameter will be accurately populated regardless of the filters and limits in the report. For example, the (other) row could be dropped from the report because the request contains a filter on sessionSource = google. This parameter will still be populated if data loss from other row was present in the input aggregate data used to generate this report. To learn more, see [About the (other) row and data sampling](https://support.google.com/analytics/answer/13208658#reports).", "type": "boolean"}, "emptyReason": {"description": "If empty reason is specified, the report is empty for this reason.", "type": "string"}, "samplingMetadatas": {"description": "If this report results is [sampled](https://support.google.com/analytics/answer/13331292), this describes the percentage of events used in this report. One `samplingMetadatas` is populated for each date range. Each `samplingMetadatas` corresponds to a date range in order that date ranges were specified in the request. However if the results are not sampled, this field will not be defined.", "items": {"$ref": "SamplingMetadata"}, "type": "array"}, "schemaRestrictionResponse": {"$ref": "SchemaRestrictionResponse", "description": "Describes the schema restrictions actively enforced in creating this report. To learn more, see [Access and data-restriction management](https://support.google.com/analytics/answer/10851388)."}, "subjectToThresholding": {"description": "If `subjectToThresholding` is true, this report is subject to thresholding and only returns data that meets the minimum aggregation thresholds. It is possible for a request to be subject to thresholding thresholding and no data is absent from the report, and this happens when all data is above the thresholds. To learn more, see [Data thresholds](https://support.google.com/analytics/answer/9383630).", "type": "boolean"}, "timeZone": {"description": "The property's current timezone. Intended to be used to interpret time-based dimensions like `hour` and `minute`. Formatted as strings from the IANA Time Zone database (https://www.iana.org/time-zones); for example \"America/New_York\" or \"Asia/Tokyo\".", "type": "string"}}, "type": "object"}, "Row": {"description": "Report data for each row. For example if RunReportRequest contains: ```none \"dimensions\": [ { \"name\": \"eventName\" }, { \"name\": \"countryId\" } ], \"metrics\": [ { \"name\": \"eventCount\" } ] ``` One row with 'in_app_purchase' as the eventName, 'JP' as the countryId, and 15 as the eventCount, would be: ```none \"dimensionValues\": [ { \"value\": \"in_app_purchase\" }, { \"value\": \"JP\" } ], \"metricValues\": [ { \"value\": \"15\" } ] ```", "id": "Row", "properties": {"dimensionValues": {"description": "List of requested dimension values. In a PivotReport, dimension_values are only listed for dimensions included in a pivot.", "items": {"$ref": "DimensionValue"}, "type": "array"}, "metricValues": {"description": "List of requested visible metric values.", "items": {"$ref": "MetricValue"}, "type": "array"}}, "type": "object"}, "RunPivotReportRequest": {"description": "The request to generate a pivot report.", "id": "RunPivotReportRequest", "properties": {"cohortSpec": {"$ref": "CohortSpec", "description": "Cohort group associated with this request. If there is a cohort group in the request the 'cohort' dimension must be present."}, "comparisons": {"description": "Optional. The configuration of comparisons requested and displayed. The request requires both a comparisons field and a comparisons dimension to receive a comparison column in the response.", "items": {"$ref": "Comparison"}, "type": "array"}, "currencyCode": {"description": "A currency code in ISO4217 format, such as \"AED\", \"USD\", \"JPY\". If the field is empty, the report uses the property's default currency.", "type": "string"}, "dateRanges": {"description": "The date range to retrieve event data for the report. If multiple date ranges are specified, event data from each date range is used in the report. A special dimension with field name \"dateRange\" can be included in a Pivot's field names; if included, the report compares between date ranges. In a cohort request, this `dateRanges` must be unspecified.", "items": {"$ref": "DateRange"}, "type": "array"}, "dimensionFilter": {"$ref": "FilterExpression", "description": "The filter clause of dimensions. Dimensions must be requested to be used in this filter. Metrics cannot be used in this filter."}, "dimensions": {"description": "The dimensions requested. All defined dimensions must be used by one of the following: dimension_expression, dimension_filter, pivots, order_bys.", "items": {"$ref": "Dimension"}, "type": "array"}, "keepEmptyRows": {"description": "If false or unspecified, each row with all metrics equal to 0 will not be returned. If true, these rows will be returned if they are not separately removed by a filter. Regardless of this `keep_empty_rows` setting, only data recorded by the Google Analytics property can be displayed in a report. For example if a property never logs a `purchase` event, then a query for the `eventName` dimension and `eventCount` metric will not have a row eventName: \"purchase\" and eventCount: 0.", "type": "boolean"}, "metricFilter": {"$ref": "FilterExpression", "description": "The filter clause of metrics. Applied at post aggregation phase, similar to SQL having-clause. Metrics must be requested to be used in this filter. Dimensions cannot be used in this filter."}, "metrics": {"description": "The metrics requested, at least one metric needs to be specified. All defined metrics must be used by one of the following: metric_expression, metric_filter, order_bys.", "items": {"$ref": "Metric"}, "type": "array"}, "pivots": {"description": "Describes the visual format of the report's dimensions in columns or rows. The union of the fieldNames (dimension names) in all pivots must be a subset of dimension names defined in Dimensions. No two pivots can share a dimension. A dimension is only visible if it appears in a pivot.", "items": {"$ref": "Pivot"}, "type": "array"}, "property": {"description": "A Google Analytics property identifier whose events are tracked. Specified in the URL path and not the body. To learn more, see [where to find your Property ID](https://developers.google.com/analytics/devguides/reporting/data/v1/property-id). Within a batch request, this property should either be unspecified or consistent with the batch-level property. Example: properties/1234", "type": "string"}, "returnPropertyQuota": {"description": "Toggles whether to return the current state of this Google Analytics property's quota. Quo<PERSON> is returned in [PropertyQuota](#PropertyQuota).", "type": "boolean"}}, "type": "object"}, "RunPivotReportResponse": {"description": "The response pivot report table corresponding to a pivot request.", "id": "RunPivotReportResponse", "properties": {"aggregates": {"description": "Aggregation of metric values. Can be totals, minimums, or maximums. The returned aggregations are controlled by the metric_aggregations in the pivot. The type of aggregation returned in each row is shown by the dimension_values which are set to \"RESERVED_\".", "items": {"$ref": "Row"}, "type": "array"}, "dimensionHeaders": {"description": "Describes dimension columns. The number of DimensionHeaders and ordering of DimensionHeaders matches the dimensions present in rows.", "items": {"$ref": "DimensionHeader"}, "type": "array"}, "kind": {"description": "Identifies what kind of resource this message is. This `kind` is always the fixed string \"analyticsData#runPivotReport\". Useful to distinguish between response types in JSON.", "type": "string"}, "metadata": {"$ref": "ResponseMetaData", "description": "<PERSON><PERSON><PERSON> for the report."}, "metricHeaders": {"description": "Describes metric columns. The number of MetricHeaders and ordering of MetricHeaders matches the metrics present in rows.", "items": {"$ref": "Metric<PERSON><PERSON><PERSON>"}, "type": "array"}, "pivotHeaders": {"description": "Summarizes the columns and rows created by a pivot. Each pivot in the request produces one header in the response. If we have a request like this: \"pivots\": [{ \"fieldNames\": [\"country\", \"city\"] }, { \"fieldNames\": \"eventName\" }] We will have the following `pivotHeaders` in the response: \"pivotHeaders\" : [{ \"dimensionHeaders\": [{ \"dimensionValues\": [ { \"value\": \"United Kingdom\" }, { \"value\": \"London\" } ] }, { \"dimensionValues\": [ { \"value\": \"Japan\" }, { \"value\": \"Osaka\" } ] }] }, { \"dimensionHeaders\": [{ \"dimensionValues\": [{ \"value\": \"session_start\" }] }, { \"dimensionValues\": [{ \"value\": \"scroll\" }] }] }]", "items": {"$ref": "PivotHeader"}, "type": "array"}, "propertyQuota": {"$ref": "PropertyQuota", "description": "This Google Analytics property's quota state including this request."}, "rows": {"description": "Rows of dimension value combinations and metric values in the report.", "items": {"$ref": "Row"}, "type": "array"}}, "type": "object"}, "RunRealtimeReportRequest": {"description": "The request to generate a realtime report.", "id": "RunRealtimeReportRequest", "properties": {"dimensionFilter": {"$ref": "FilterExpression", "description": "The filter clause of dimensions. Metrics cannot be used in this filter."}, "dimensions": {"description": "The dimensions requested and displayed.", "items": {"$ref": "Dimension"}, "type": "array"}, "limit": {"description": "The number of rows to return. If unspecified, 10,000 rows are returned. The API returns a maximum of 250,000 rows per request, no matter how many you ask for. `limit` must be positive. The API can also return fewer rows than the requested `limit`, if there aren't as many dimension values as the `limit`. For instance, there are fewer than 300 possible values for the dimension `country`, so when reporting on only `country`, you can't get more than 300 rows, even if you set `limit` to a higher value.", "format": "int64", "type": "string"}, "metricAggregations": {"description": "Aggregation of metrics. Aggregated metric values will be shown in rows where the dimension_values are set to \"RESERVED_(MetricAggregation)\".", "items": {"enum": ["METRIC_AGGREGATION_UNSPECIFIED", "TOTAL", "MINIMUM", "MAXIMUM", "COUNT"], "enumDescriptions": ["Unspecified operator.", "SUM operator.", "Minimum operator.", "Maximum operator.", "Count operator."], "type": "string"}, "type": "array"}, "metricFilter": {"$ref": "FilterExpression", "description": "The filter clause of metrics. Applied at post aggregation phase, similar to SQL having-clause. Dimensions cannot be used in this filter."}, "metrics": {"description": "The metrics requested and displayed.", "items": {"$ref": "Metric"}, "type": "array"}, "minuteRanges": {"description": "The minute ranges of event data to read. If unspecified, one minute range for the last 30 minutes will be used. If multiple minute ranges are requested, each response row will contain a zero based minute range index. If two minute ranges overlap, the event data for the overlapping minutes is included in the response rows for both minute ranges.", "items": {"$ref": "MinuteRange"}, "type": "array"}, "orderBys": {"description": "Specifies how rows are ordered in the response.", "items": {"$ref": "OrderBy"}, "type": "array"}, "returnPropertyQuota": {"description": "Toggles whether to return the current state of this Google Analytics property's Realtime quota. Quota is returned in [PropertyQuota](#PropertyQuota).", "type": "boolean"}}, "type": "object"}, "RunRealtimeReportResponse": {"description": "The response realtime report table corresponding to a request.", "id": "RunRealtimeReportResponse", "properties": {"dimensionHeaders": {"description": "Describes dimension columns. The number of DimensionHeaders and ordering of DimensionHeaders matches the dimensions present in rows.", "items": {"$ref": "DimensionHeader"}, "type": "array"}, "kind": {"description": "Identifies what kind of resource this message is. This `kind` is always the fixed string \"analyticsData#runRealtimeReport\". Useful to distinguish between response types in JSON.", "type": "string"}, "maximums": {"description": "If requested, the maximum values of metrics.", "items": {"$ref": "Row"}, "type": "array"}, "metricHeaders": {"description": "Describes metric columns. The number of MetricHeaders and ordering of MetricHeaders matches the metrics present in rows.", "items": {"$ref": "Metric<PERSON><PERSON><PERSON>"}, "type": "array"}, "minimums": {"description": "If requested, the minimum values of metrics.", "items": {"$ref": "Row"}, "type": "array"}, "propertyQuota": {"$ref": "PropertyQuota", "description": "This Google Analytics property's Realtime quota state including this request."}, "rowCount": {"description": "The total number of rows in the query result. `rowCount` is independent of the number of rows returned in the response and the `limit` request parameter. For example if a query returns 175 rows and includes `limit` of 50 in the API request, the response will contain `rowCount` of 175 but only 50 rows.", "format": "int32", "type": "integer"}, "rows": {"description": "Rows of dimension value combinations and metric values in the report.", "items": {"$ref": "Row"}, "type": "array"}, "totals": {"description": "If requested, the totaled values of metrics.", "items": {"$ref": "Row"}, "type": "array"}}, "type": "object"}, "RunReportRequest": {"description": "The request to generate a report.", "id": "RunReportRequest", "properties": {"cohortSpec": {"$ref": "CohortSpec", "description": "Cohort group associated with this request. If there is a cohort group in the request the 'cohort' dimension must be present."}, "comparisons": {"description": "Optional. The configuration of comparisons requested and displayed. The request only requires a comparisons field in order to receive a comparison column in the response.", "items": {"$ref": "Comparison"}, "type": "array"}, "currencyCode": {"description": "A currency code in ISO4217 format, such as \"AED\", \"USD\", \"JPY\". If the field is empty, the report uses the property's default currency.", "type": "string"}, "dateRanges": {"description": "Date ranges of data to read. If multiple date ranges are requested, each response row will contain a zero based date range index. If two date ranges overlap, the event data for the overlapping days is included in the response rows for both date ranges. In a cohort request, this `dateRanges` must be unspecified.", "items": {"$ref": "DateRange"}, "type": "array"}, "dimensionFilter": {"$ref": "FilterExpression", "description": "Dimension filters let you ask for only specific dimension values in the report. To learn more, see [Fundamentals of Dimension Filters](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#dimension_filters) for examples. Metrics cannot be used in this filter."}, "dimensions": {"description": "The dimensions requested and displayed.", "items": {"$ref": "Dimension"}, "type": "array"}, "keepEmptyRows": {"description": "If false or unspecified, each row with all metrics equal to 0 will not be returned. If true, these rows will be returned if they are not separately removed by a filter. Regardless of this `keep_empty_rows` setting, only data recorded by the Google Analytics property can be displayed in a report. For example if a property never logs a `purchase` event, then a query for the `eventName` dimension and `eventCount` metric will not have a row eventName: \"purchase\" and eventCount: 0.", "type": "boolean"}, "limit": {"description": "The number of rows to return. If unspecified, 10,000 rows are returned. The API returns a maximum of 250,000 rows per request, no matter how many you ask for. `limit` must be positive. The API can also return fewer rows than the requested `limit`, if there aren't as many dimension values as the `limit`. For instance, there are fewer than 300 possible values for the dimension `country`, so when reporting on only `country`, you can't get more than 300 rows, even if you set `limit` to a higher value. To learn more about this pagination parameter, see [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).", "format": "int64", "type": "string"}, "metricAggregations": {"description": "Aggregation of metrics. Aggregated metric values will be shown in rows where the dimension_values are set to \"RESERVED_(MetricAggregation)\". Aggregates including both comparisons and multiple date ranges will be aggregated based on the date ranges.", "items": {"enum": ["METRIC_AGGREGATION_UNSPECIFIED", "TOTAL", "MINIMUM", "MAXIMUM", "COUNT"], "enumDescriptions": ["Unspecified operator.", "SUM operator.", "Minimum operator.", "Maximum operator.", "Count operator."], "type": "string"}, "type": "array"}, "metricFilter": {"$ref": "FilterExpression", "description": "The filter clause of metrics. Applied after aggregating the report's rows, similar to SQL having-clause. Dimensions cannot be used in this filter."}, "metrics": {"description": "The metrics requested and displayed.", "items": {"$ref": "Metric"}, "type": "array"}, "offset": {"description": "The row count of the start row. The first row is counted as row 0. When paging, the first request does not specify offset; or equivalently, sets offset to 0; the first request returns the first `limit` of rows. The second request sets offset to the `limit` of the first request; the second request returns the second `limit` of rows. To learn more about this pagination parameter, see [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).", "format": "int64", "type": "string"}, "orderBys": {"description": "Specifies how rows are ordered in the response. Requests including both comparisons and multiple date ranges will have order bys applied on the comparisons.", "items": {"$ref": "OrderBy"}, "type": "array"}, "property": {"description": "A Google Analytics property identifier whose events are tracked. Specified in the URL path and not the body. To learn more, see [where to find your Property ID](https://developers.google.com/analytics/devguides/reporting/data/v1/property-id). Within a batch request, this property should either be unspecified or consistent with the batch-level property. Example: properties/1234", "type": "string"}, "returnPropertyQuota": {"description": "Toggles whether to return the current state of this Google Analytics property's quota. Quo<PERSON> is returned in [PropertyQuota](#PropertyQuota).", "type": "boolean"}}, "type": "object"}, "RunReportResponse": {"description": "The response report table corresponding to a request.", "id": "RunReportResponse", "properties": {"dimensionHeaders": {"description": "Describes dimension columns. The number of DimensionHeaders and ordering of DimensionHeaders matches the dimensions present in rows.", "items": {"$ref": "DimensionHeader"}, "type": "array"}, "kind": {"description": "Identifies what kind of resource this message is. This `kind` is always the fixed string \"analyticsData#runReport\". Useful to distinguish between response types in JSON.", "type": "string"}, "maximums": {"description": "If requested, the maximum values of metrics.", "items": {"$ref": "Row"}, "type": "array"}, "metadata": {"$ref": "ResponseMetaData", "description": "<PERSON><PERSON><PERSON> for the report."}, "metricHeaders": {"description": "Describes metric columns. The number of MetricHeaders and ordering of MetricHeaders matches the metrics present in rows.", "items": {"$ref": "Metric<PERSON><PERSON><PERSON>"}, "type": "array"}, "minimums": {"description": "If requested, the minimum values of metrics.", "items": {"$ref": "Row"}, "type": "array"}, "propertyQuota": {"$ref": "PropertyQuota", "description": "This Google Analytics property's quota state including this request."}, "rowCount": {"description": "The total number of rows in the query result. `rowCount` is independent of the number of rows returned in the response, the `limit` request parameter, and the `offset` request parameter. For example if a query returns 175 rows and includes `limit` of 50 in the API request, the response will contain `rowCount` of 175 but only 50 rows. To learn more about this pagination parameter, see [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).", "format": "int32", "type": "integer"}, "rows": {"description": "Rows of dimension value combinations and metric values in the report.", "items": {"$ref": "Row"}, "type": "array"}, "totals": {"description": "If requested, the totaled values of metrics.", "items": {"$ref": "Row"}, "type": "array"}}, "type": "object"}, "SamplingMetadata": {"description": "If this report results is [sampled](https://support.google.com/analytics/answer/13331292), this describes the percentage of events used in this report. Sampling is the practice of analyzing a subset of all data in order to uncover the meaningful information in the larger data set.", "id": "SamplingMetadata", "properties": {"samplesReadCount": {"description": "The total number of events read in this sampled report for a date range. This is the size of the subset this property's data that was analyzed in this report.", "format": "int64", "type": "string"}, "samplingSpaceSize": {"description": "The total number of events present in this property's data that could have been analyzed in this report for a date range. Sam<PERSON> uncovers the meaningful information about the larger data set, and this is the size of the larger data set. To calculate the percentage of available data that was used in this report, compute `samplesReadCount/samplingSpaceSize`.", "format": "int64", "type": "string"}}, "type": "object"}, "SchemaRestrictionResponse": {"description": "The schema restrictions actively enforced in creating this report. To learn more, see [Access and data-restriction management](https://support.google.com/analytics/answer/10851388).", "id": "SchemaRestrictionResponse", "properties": {"activeMetricRestrictions": {"description": "All restrictions actively enforced in creating the report. For example, `purchaseRevenue` always has the restriction type `REVENUE_DATA`. However, this active response restriction is only populated if the user's custom role disallows access to `REVENUE_DATA`.", "items": {"$ref": "ActiveMetricRestriction"}, "type": "array"}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "StringFilter": {"description": "The filter for string", "id": "StringFilter", "properties": {"caseSensitive": {"description": "If true, the string value is case sensitive.", "type": "boolean"}, "matchType": {"description": "The match type for this filter.", "enum": ["MATCH_TYPE_UNSPECIFIED", "EXACT", "BEGINS_WITH", "ENDS_WITH", "CONTAINS", "FULL_REGEXP", "PARTIAL_REGEXP"], "enumDescriptions": ["Unspecified", "Exact match of the string value.", "Begins with the string value.", "Ends with the string value.", "Contains the string value.", "Full match for the regular expression with the string value.", "Partial match for the regular expression with the string value."], "type": "string"}, "value": {"description": "The string value used for the matching.", "type": "string"}}, "type": "object"}, "V1betaAudienceDimension": {"description": "An audience dimension is a user attribute. Specific user attributed are requested and then later returned in the `QueryAudienceExportResponse`.", "id": "V1betaAudienceDimension", "properties": {"dimensionName": {"description": "Optional. The API name of the dimension. See the [API Dimensions](https://developers.google.com/analytics/devguides/reporting/data/v1/audience-list-api-schema#dimensions) for the list of dimension names.", "type": "string"}}, "type": "object"}, "V1betaAudienceDimensionValue": {"description": "The value of a dimension.", "id": "V1betaAudienceDimensionValue", "properties": {"value": {"description": "Value as a string if the dimension type is a string.", "type": "string"}}, "type": "object"}, "V1betaAudienceRow": {"description": "Dimension value attributes for the audience user row.", "id": "V1betaAudienceRow", "properties": {"dimensionValues": {"description": "Each dimension value attribute for an audience user. One dimension value will be added for each dimension column requested.", "items": {"$ref": "V1betaAudienceDimensionValue"}, "type": "array"}}, "type": "object"}}, "servicePath": "", "title": "Google Analytics Data API", "version": "v1beta", "version_module": true}