package com.bolt.dashboard.service;

import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
/**
 * 
 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.config.MongoAggregate;
import com.bolt.dashboard.core.model.BuildFailurePatternForProjectInJenkinsModel;
import com.bolt.dashboard.core.model.BuildTool;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.GitlabValueStream;
import com.bolt.dashboard.core.model.ValueStreamStep;
import com.bolt.dashboard.core.repository.BuildFailurePatternForProjectRepo;
import com.bolt.dashboard.core.repository.BuildToolRep;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.request.BuildFailureRequest;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.util.BuildCalculations;
import com.bolt.dashboard.util.ProjectHomeCalculation;

@Service
public class BuildToolServiceImplemantation implements BuildToolService {
	private BuildToolRep buildToolRepository;
	private BuildFailurePatternForProjectRepo buildFailurePatternForProjectRepo;
	private static final Logger LOG = LogManager.getLogger(BuildToolServiceImplemantation.class);
	private ConfigurationSettingRep configSettingRepo;
	private ConfigurationToolInfoMetric metric;
	String noDataConst="no datas found in DB for project  ";
	String buildConst="build";

	@Autowired
	public BuildToolServiceImplemantation(BuildToolRep buildToolRepository,
			BuildFailurePatternForProjectRepo buildFailurePatternForProjectRepo,ConfigurationSettingRep configSettingRepo) {

		this.buildToolRepository = buildToolRepository;
		this.buildFailurePatternForProjectRepo = buildFailurePatternForProjectRepo;
		this.configSettingRepo = configSettingRepo;
	}
	@Override
	public DataResponse<Iterable<BuildTool>> search(String projectName) {
	
		long lastUpdate = 1;
		Iterable<BuildTool> result = buildToolRepository.findByName(projectName);
		if (result.iterator().hasNext()) {
			return new DataResponse<Iterable<BuildTool>>(result, lastUpdate);
		} else {
			LOG.info(noDataConst + projectName);
			return null;
		}
	}


	public DataResponse<Iterable<BuildTool>> search(String projectName, String toolName) {
	
//		Iterable<BuildTool> result = buildToolRepository.findByNameAndToolNameAndTimestampBetween(projectName, sDate, eDate);
		long lastUpdate = 1;
		Iterable<BuildTool> result = buildToolRepository.findByNameAndBuildTypeIgnoreCase(projectName, toolName);
		if(!result.iterator().hasNext()) {
			result = buildToolRepository.findByName(projectName);
		}
		if (result.iterator().hasNext()) {
			return new DataResponse<Iterable<BuildTool>>(result, lastUpdate);
		} else {
			LOG.info(noDataConst + projectName);
			return null;
		}
	}

	@Override
//	@Cacheable(value="searchJobList", key ="'searchJobList'+#projectName+#buildType", cacheManager="timeoutCacheManager")
	public DataResponse<Iterable<BuildTool>> searchJobList(String buildType, String projectName) {
		long lastUpdate = 1;
		Iterable<BuildTool> result = buildToolRepository.findByBuildTypeAndName(buildType, projectName);
		if (result.iterator().hasNext()) {
			return new DataResponse<Iterable<BuildTool>>(result, lastUpdate);
		} else {
			LOG.info(noDataConst + projectName + "  and buildType  " + buildType);
			return null;
		}
	}

	@Override
	public Iterable<BuildTool> searchForTest(String buildType, String projectName) {

		Iterable<BuildTool> result = buildToolRepository.findByBuildTypeAndName(buildType, projectName);
		return result;

	}

	@Override
//	@Cacheable(value="search", key ="'search'+#projectName+#sDate+#eDate+#flag", cacheManager="timeoutCacheManager")
	public DataResponse<Iterable<BuildTool>> search(String projectName, long sDate, long eDate, boolean flag) {
		boolean flagnew = flag;
		String toolName = null;
		List<ConfigurationSetting> config = configSettingRepo.findByProjectName(projectName);
		 metric = new ConfigurationToolInfoMetric();
		for(ConfigurationToolInfoMetric m:config.get(0).getMetrics()) {
			if(m.getToolType().equalsIgnoreCase(buildConst)) {
				metric = m;
				if(m.getToolName().equals("GITLAB Pipeline")) {
					toolName = "GITLAB";
				}else if(m.getToolName().equals("BITBUCKET Pipeline")) {
					toolName = "BITBUCKET";
				}else if(m.getToolName().equals("Jenkins Pipeline")) {
					toolName = "JENKINS";
				}
				else {
					toolName = m.getToolName();
				}
				break;
			}
		}
	
		if (!flagnew) {
			return search(projectName, toolName);
		} else {
			MongoAggregate mongoAggr = new MongoAggregate();
			long lastUpdate = mongoAggr.getBuildsCount(projectName);


			Iterable<BuildTool> result = buildToolRepository.findByNameAndTimestampBetween(projectName, sDate, eDate);
			
			if (result.iterator().hasNext()) {
				return new DataResponse<Iterable<BuildTool>>(result, lastUpdate);
			} else {
				LOG.info(noDataConst+ projectName);
				return null;
			}
		}

	}

	@Override
//	@Caching(evict = {
//			@CacheEvict(value="fetchBuildData", key ="'fetchBuildData'", cacheManager="timeoutCacheManager"),
//			@CacheEvict("fetchFailurePatternData")
//		})
	public DataResponse<List<BuildFailureRequest>> buildFailurePattern(String toolName,
			List<BuildFailureRequest> failureRequestList) {

		BuildFailurePatternForProjectInJenkinsModel patternForproject = new BuildFailurePatternForProjectInJenkinsModel();

		for (BuildFailureRequest req : failureRequestList) {
			patternForproject.addPatternMetric(req.getPatternFromUser(), req.getPatternDisplay(), 0);
			patternForproject.setProjectName(req.getProjectName());
		}

		List<BuildFailurePatternForProjectInJenkinsModel> buildFailurePatternForProjectList = buildFailurePatternForProjectRepo
				.findByProjectName(failureRequestList.get(0).getProjectName());

		if (buildFailurePatternForProjectList.isEmpty()) {
			buildFailurePatternForProjectRepo.save(patternForproject);

		} else {

			BuildFailurePatternForProjectInJenkinsModel buildFailurePatternForProject = buildFailurePatternForProjectList
					.get(buildFailurePatternForProjectList.size() - 1);
			buildFailurePatternForProject.setProjectName(patternForproject.getProjectName());
			buildFailurePatternForProject.setPatternMetrics(patternForproject.getPatternMetrics());
			buildFailurePatternForProject.setTimestampOfCreation(System.currentTimeMillis());
			buildFailurePatternForProjectRepo.save(buildFailurePatternForProject);
		}
		return null;
	}

	@Override
//	@Cacheable(value="fetchBuildData", key ="'fetchBuildData'", cacheManager="timeoutCacheManager")
	public DataResponse<List<BuildFailurePatternForProjectInJenkinsModel>> fetchBuildData() {
		long lastUpdated = 1;
		List<BuildFailurePatternForProjectInJenkinsModel> result = buildFailurePatternForProjectRepo.findAll();
		if (!result.isEmpty()) {
			return new DataResponse<List<BuildFailurePatternForProjectInJenkinsModel>>(result, lastUpdated);
		} else {
			LOG.info("no datas found in DB for fetchBuildData() ");
			return null;
		}
	}
	@Override
//	@Cacheable(value="fetchFailurePatternData", key ="'fetchFailurePatternData'+#projectName", cacheManager="timeoutCacheManager")
	public DataResponse<List<BuildFailurePatternForProjectInJenkinsModel>> fetchFailurePatternData(String projectName) {
		long lastUpdated = 1;
		List<BuildFailurePatternForProjectInJenkinsModel> response = buildFailurePatternForProjectRepo
				.findByProjectName(projectName);
		if (!response.isEmpty()) {
			return new DataResponse<List<BuildFailurePatternForProjectInJenkinsModel>>(response, lastUpdated);
		} else {
			LOG.info(noDataConst + projectName);
			return new DataResponse<List<BuildFailurePatternForProjectInJenkinsModel>>(response, 0);
		}
	}

	@Override
//	@Cacheable(value="getOneByProjectName", key ="'getOneByProjectName'+#projName", cacheManager="timeoutCacheManager")
	public BuildTool getOneByProjectName(String projName) {
		
		BuildTool result = buildToolRepository.findOneByNameOrderByBuildIDDesc(projName);
		return result;
	}

	@Override
//	@Cacheable(value="getBuildDetailsHome", key ="'getBuildDetailsHome'+#projName+#almType", cacheManager="timeoutCacheManager")
	public List<Map<String, String>> getBuildDetailsHome(String projName, String almType) {
		
		ProjectHomeCalculation projHomeCalc = new ProjectHomeCalculation();
		List<Map<String, String>> result = projHomeCalc.getBuildDetailsHome(projName, almType);
		return result;
	}

	@Override
//	@Cacheable(value="getValueStream", key ="'getValueStream'+#proName", cacheManager="timeoutCacheManager")
	public List<ValueStreamStep> getValueStream(String proName) {
		
		List<ConfigurationSetting> config = configSettingRepo.findByProjectName(proName);
		 metric = new ConfigurationToolInfoMetric();
		for(ConfigurationToolInfoMetric m:config.get(0).getMetrics()) {
			if(m.getToolType().equalsIgnoreCase(buildConst)) {
				metric = m;
				break;
			}
		}
		BuildCalculations buildCalculations = new BuildCalculations();
		if(metric.getToolName().toLowerCase().contains("bitbucket") )
			return buildCalculations.getValueStreamSteps(proName,metric.getToolName(), null,null);
		else
			return buildCalculations.getValueStreamPipelineJobs(proName, proName);
		
	}

	@Override
//	@Cacheable(value="getGitlabValueStream", key ="'getGitlabValueStream'+#proName", cacheManager="timeoutCacheManager")
	public List<GitlabValueStream> getGitlabValueStream(String proName) {
		
		List<ConfigurationSetting> config = configSettingRepo.findByProjectName(proName);
		 metric = new ConfigurationToolInfoMetric();
		for(ConfigurationToolInfoMetric m:config.get(0).getMetrics()) {
			if(m.getToolType().equalsIgnoreCase(buildConst)) {
				metric = m;
				break;
			}
		}
		BuildCalculations buildCalculations = new BuildCalculations();
	   if(metric.getToolName().toLowerCase().contains("gitlab"))
	   {
		   
			return buildCalculations.getGitlabValueStreamSteps(proName,"GITLAB"); // null
	   }
	
	   else
		   return null;
	
		
	}



	}
