{"cells": [{"cell_type": "code", "execution_count": 12, "id": "setup", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Setup complete\n"]}], "source": ["import os\n", "import re\n", "import json\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "import pandas as pd\n", "from collections import defaultdict\n", "\n", "from tree_sitter import Language, Parser\n", "import tree_sitter_java as tsjava\n", "\n", "from langchain_community.document_loaders import TextLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language\n", "from langchain_google_genai import ChatGoogleGenerativeAI\n", "from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "from langchain_community.graphs import Neo4jGraph\n", "from langchain_openai import AzureChatOpenAI\n", "\n", "BASE_PATH = Path(r\"C:/Shaik/sample/OneInsights\")\n", "NEO4J_URI = \"bolt://localhost:7687\"\n", "NEO4J_USER = \"neo4j\"\n", "NEO4J_PASSWORD = \"Test@7889\"\n", "NEO4J_DB = \"final-v11\"\n", "\n", "graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)\n", "JAVA_LANGUAGE = Language(tsjava.language())\n", "parser = Parser(JAVA_LANGUAGE)\n", "\n", "llm = AzureChatOpenAI(\n", "    api_key=\"********************************\",\n", "    azure_endpoint=\"https://azureopenaibrsc.openai.azure.com/\",\n", "    azure_deployment=\"gpt-4o\",\n", "    api_version=\"2024-12-01-preview\"\n", ")\n", "\n", "all_relationships = []\n", "class_registry = {}\n", "application_mapping = {}\n", "\n", "# NODE TYPE COLORS FOR CONSISTENT NEO4J VISUALIZATION\n", "NODE_COLORS = {\n", "    'FOLDERS': '#4CAF50',      # Green\n", "    'FILE': '#2196F3',         # Blue  \n", "    'CLASS': '#FF9800',        # Orange\n", "    'METHOD': '#9C27B0',       # Purple\n", "    'INTERFACE': '#E91E63',    # Pink\n", "    'VARIABLE': '#00BCD4',     # <PERSON>an\n", "    'ENDPOINT': '#FF5722',     # Deep Orange\n", "    'DATA': '#795548',         # Brown\n", "    'OPERATION': '#607D8B',    # Blue Grey\n", "    'VALUE': '#FFC107',        # Amber\n", "    'EXPRESSION': '#8BC34A',   # Light Green\n", "    'TRANSFORMATION': '#3F51B5' # Indigo\n", "}\n", "\n", "# Global variable transformation records\n", "variable_transformation_records = []\n", "\n", "print(\"✅ Setup complete\")"]}, {"cell_type": "code", "execution_count": 13, "id": "naming_utilities", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Naming utilities loaded\n"]}], "source": ["def to_camel_case(text):\n", "    \"\"\"Convert text to camelCase consistently across all stages\"\"\"\n", "    if not text or pd.isna(text):\n", "        return text\n", "    \n", "    text_str = str(text).strip()\n", "    \n", "    # Handle Java files - keep .java extension\n", "    if text_str.lower().endswith('.java'):\n", "        base_name = text_str[:-5]\n", "        camel_name = convert_to_camel_case(base_name)\n", "        return f\"{camel_name}.java\"\n", "    \n", "    return convert_to_camel_case(text_str)\n", "\n", "def convert_to_camel_case(text):\n", "    \"\"\"Core camelCase conversion logic\"\"\"\n", "    if not text:\n", "        return text\n", "    \n", "    # Remove special characters and split into words\n", "    import re\n", "    \n", "    # Split on various delimiters and capital letters\n", "    words = re.findall(r'[A-Z]?[a-z]+|[A-Z]+(?=[A-Z][a-z]|\\b)|[0-9]+', text)\n", "    \n", "    if not words:\n", "        return text.lower()\n", "    \n", "    # First word lowercase, rest capitalized\n", "    camel_case = words[0].lower() + ''.join(word.capitalize() for word in words[1:])\n", "    return camel_case\n", "\n", "def clean_folder_path(name):\n", "    \"\"\"Clean folder paths and apply camelCase\"\"\"\n", "    if not name or pd.isna(name):\n", "        return name\n", "    \n", "    name_str = str(name)\n", "    \n", "    # Remove path separators, keep only folder name\n", "    if '\\\\' in name_str:\n", "        name_str = name_str.split('\\\\')[-1]\n", "    if '/' in name_str:\n", "        name_str = name_str.split('/')[-1]\n", "    \n", "    return to_camel_case(name_str)\n", "\n", "def is_temp_variable(var_name):\n", "    \"\"\"Check if variable is temporary and should be filtered\"\"\"\n", "    if not var_name or len(var_name) == 0:\n", "        return True\n", "    \n", "    temp_patterns = [\n", "        r'^[ijklmnpqr]$', r'^temp\\w*$', r'^tmp\\w*$', r'^_\\w*$',\n", "        r'^\\w*temp$', r'^\\w*tmp$', r'^counter\\d*$', r'^index\\d*$',\n", "        r'^idx\\d*$', r'^\\$\\w*$', r'^this$', r'^super$'\n", "    ]\n", "    \n", "    for pattern in temp_patterns:\n", "        if re.match(pattern, var_name, re.IGNORECASE):\n", "            return True\n", "    return False\n", "\n", "def format_variable_name(method_name, var_name):\n", "    \"\"\"Format variable name with method context in camelCase\"\"\"\n", "    if not method_name or not var_name:\n", "        return to_camel_case(var_name) if var_name else var_name\n", "    \n", "    method_camel = to_camel_case(method_name)\n", "    var_camel = to_camel_case(var_name)\n", "    return f\"{method_camel}.{var_camel}\"\n", "\n", "def get_application_from_path(file_path):\n", "    \"\"\"Get application name in camelCase\"\"\"\n", "    try:\n", "        rel_path = os.path.relpath(file_path, BASE_PATH)\n", "        path_parts = rel_path.split(os.sep)\n", "        if len(path_parts) > 0:\n", "            return to_camel_case(path_parts[0])\n", "    except:\n", "        pass\n", "    return \"unknown\"\n", "\n", "def normalize_all_names(df):\n", "    \"\"\"Apply consistent camelCase naming to entire dataframe\"\"\"\n", "    df_normalized = df.copy()\n", "    \n", "    # Apply camelCase to node names\n", "    df_normalized['source_node'] = df_normalized['source_node'].apply(clean_folder_path)\n", "    df_normalized['destination_node'] = df_normalized['destination_node'].apply(clean_folder_path)\n", "    \n", "    # Ensure node types are uppercase\n", "    df_normalized['source_type'] = df_normalized['source_type'].str.upper()\n", "    df_normalized['destination_type'] = df_normalized['destination_type'].str.upper()\n", "    \n", "    # Ensure relationships are uppercase\n", "    df_normalized['relationship'] = df_normalized['relationship'].str.upper()\n", "    \n", "    # Apply camelCase to application names\n", "    df_normalized['application'] = df_normalized['application'].apply(to_camel_case)\n", "    \n", "    return df_normalized\n", "\n", "print(\"✅ Naming utilities loaded\")"]}, {"cell_type": "code", "execution_count": 16, "id": "stage1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Stage 1: 27 relationships\n", "✅ All names in camelCase format\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>source_node</th>\n", "      <th>source_type</th>\n", "      <th>destination_node</th>\n", "      <th>destination_type</th>\n", "      <th>relationship</th>\n", "      <th>file_path</th>\n", "      <th>application</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>oneInsights</td>\n", "      <td>FOLDERS</td>\n", "      <td>serviceBolt</td>\n", "      <td>FOLDERS</td>\n", "      <td>CONTAINS</td>\n", "      <td>None</td>\n", "      <td>serviceBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>oneInsights</td>\n", "      <td>FOLDERS</td>\n", "      <td>unifiedBolt</td>\n", "      <td>FOLDERS</td>\n", "      <td>CONTAINS</td>\n", "      <td>None</td>\n", "      <td>unifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>serviceBolt</td>\n", "      <td>FOLDERS</td>\n", "      <td>api</td>\n", "      <td>FOLDERS</td>\n", "      <td>CONTAINS</td>\n", "      <td>None</td>\n", "      <td>serviceBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>api</td>\n", "      <td>FOLDERS</td>\n", "      <td>buildToolController.java</td>\n", "      <td>FILE</td>\n", "      <td>CONTAINS</td>\n", "      <td>ServiceBolt\\api\\BuildToolController.java</td>\n", "      <td>serviceBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>serviceBolt</td>\n", "      <td>FOLDERS</td>\n", "      <td>service</td>\n", "      <td>FOLDERS</td>\n", "      <td>CONTAINS</td>\n", "      <td>None</td>\n", "      <td>serviceBolt</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   source_node source_type          destination_node destination_type  \\\n", "0  oneInsights     FOLDERS               serviceBolt          FOLDERS   \n", "1  oneInsights     FOLDERS               unifiedBolt          FOLDERS   \n", "2  serviceBolt     FOLDERS                       api          FOLDERS   \n", "3          api     FOLDERS  buildToolController.java             FILE   \n", "4  serviceBolt     FOLDERS                   service          FOLDERS   \n", "\n", "  relationship                                 file_path  application  \n", "0     CONTAINS                                      None  serviceBolt  \n", "1     CONTAINS                                      None  unifiedBolt  \n", "2     CONTAINS                                      None  serviceBolt  \n", "3     CONTAINS  ServiceBolt\\api\\BuildToolController.java  serviceBolt  \n", "4     CONTAINS                                      None  serviceBolt  "]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# STAGE 1: SIMPLIFIED FOLDER HIERARCHY WITH CAMELCASE\n", "all_relationships = []\n", "\n", "def extract_simplified_hierarchy():\n", "    project_name = to_camel_case(BASE_PATH.name)\n", "    \n", "    for root, dirs, files in os.walk(BASE_PATH):\n", "        rel_root = os.path.relpath(root, BASE_PATH)\n", "        \n", "        if rel_root == '.':\n", "            # Direct connection: oneInsights -> serviceBolt, unifiedBolt\n", "            for d in dirs:\n", "                folder_name = to_camel_case(d)\n", "                all_relationships.append({\n", "                    'source_node': project_name,\n", "                    'source_type': 'FOLDERS',\n", "                    'destination_node': folder_name,\n", "                    'destination_type': 'FOLDERS',\n", "                    'relationship': 'CONTAINS',\n", "                    'file_path': None,\n", "                    'application': folder_name\n", "                })\n", "        else:\n", "            # Handle nested folder structure\n", "            path_parts = rel_root.split(os.sep)\n", "            current_app = to_camel_case(path_parts[0])\n", "            \n", "            # Create folder-to-folder connections for nested paths\n", "            if len(path_parts) > 1:\n", "                for i in range(len(path_parts) - 1):\n", "                    parent = to_camel_case(path_parts[i])\n", "                    child = to_camel_case(path_parts[i + 1])\n", "                    \n", "                    all_relationships.append({\n", "                        'source_node': parent,\n", "                        'source_type': 'FOLDERS',\n", "                        'destination_node': child,\n", "                        'destination_type': 'FOLDERS',\n", "                        'relationship': 'CONTAINS',\n", "                        'file_path': None,\n", "                        'application': current_app\n", "                    })\n", "        \n", "        # Handle files\n", "        current_folder = to_camel_case(os.path.basename(root)) if rel_root != '.' else project_name\n", "        current_app = get_application_from_path(root)\n", "        \n", "        for file in files:\n", "            if file.endswith('.java'):\n", "                file_rel_path = os.path.relpath(os.path.join(root, file), BASE_PATH)\n", "                application_mapping[file_rel_path] = current_app\n", "                \n", "                file_name = to_camel_case(file)  # Apply camelCase to filename\n", "                \n", "                all_relationships.append({\n", "                    'source_node': current_folder,\n", "                    'source_type': 'FOLDERS',\n", "                    'destination_node': file_name,\n", "                    'destination_type': 'FILE',\n", "                    'relationship': 'CONTAINS',\n", "                    'file_path': file_rel_path,\n", "                    'application': current_app\n", "                })\n", "\n", "# Execute Stage 1\n", "extract_simplified_hierarchy()\n", "df_stage1 = pd.DataFrame(all_relationships)\n", "df_stage1 = normalize_all_names(df_stage1)\n", "\n", "# Remove duplicates\n", "df_stage1 = df_stage1.drop_duplicates(subset=['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship'])\n", "df_stage1 = df_stage1.reset_index(drop=True)\n", "\n", "df_stage1.to_csv('stage1_hierarchy.csv', index=False)\n", "print(f\"Stage 1: {len(df_stage1)} relationships\")\n", "print(\"✅ All names in camelCase format\")\n", "df_stage1.head()"]}, {"cell_type": "code", "execution_count": 17, "id": "stage2_functions", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Stage 2 functions loaded\n"]}], "source": ["def extract_package_and_imports(source_code_str):\n", "    package_pattern = r'package\\s+([\\w\\.]+);'\n", "    import_pattern = r'import\\s+([\\w\\.]+);'\n", "    package_match = re.search(package_pattern, source_code_str)\n", "    package_name = package_match.group(1) if package_match else None\n", "    import_matches = re.findall(import_pattern, source_code_str)\n", "    return package_name, import_matches\n", "\n", "def extract_api_endpoints(source_code_str):\n", "    endpoints = []\n", "    mapping_patterns = {\n", "        'requestMapping': [r'@RequestMapping\\s*\\(\\s*value\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']', r'@RequestMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']'],\n", "        'getMapping': [r'@GetMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']'],\n", "        'postMapping': [r'@PostMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']'],\n", "        'putMapping': [r'@PutMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']'],\n", "        'deleteMapping': [r'@DeleteMapping\\s*\\(\\s*[\"\\']([^\"\\']+)[\"\\']']\n", "    }\n", "    \n", "    for mapping_type, patterns in mapping_patterns.items():\n", "        for pattern in patterns:\n", "            matches = re.findall(pattern, source_code_str, re.MULTILINE | re.DOTALL)\n", "            for match in matches:\n", "                if match.strip():\n", "                    endpoint_name = to_camel_case(f\"{mapping_type}_{match.replace('/', '_').replace('-', '_')}\")\n", "                    endpoints.append({\n", "                        'type': 'ENDPOINT',\n", "                        'name': endpoint_name,\n", "                        'path': match.strip(),\n", "                        'method': mapping_type.replace('Mapping', '').upper() if mapping_type != 'requestMapping' else 'GET'\n", "                    })\n", "    return endpoints\n", "\n", "def extract_database_entities(source_code_str):\n", "    entities = []\n", "    entity_patterns = [r'@Entity\\s*(?:\\([^)]*\\))?', r'@Table\\s*\\(\\s*name\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']']\n", "    \n", "    for pattern in entity_patterns:\n", "        if re.search(pattern, source_code_str, re.MULTILINE):\n", "            table_matches = re.findall(r'@Table\\s*\\(\\s*name\\s*=\\s*[\"\\']([^\"\\']+)[\"\\']', source_code_str)\n", "            for table_name in table_matches:\n", "                if table_name.strip():\n", "                    entities.append({'type': 'DATA', 'name': to_camel_case(table_name.strip())})\n", "            \n", "            if not table_matches:\n", "                class_match = re.search(r'public\\s+class\\s+(\\w+)', source_code_str)\n", "                if class_match:\n", "                    class_name = class_match.group(1)\n", "                    table_name = re.sub('([a-z0-9])([A-Z])', r'\\1_\\2', class_name).lower()\n", "                    entities.append({'type': 'DATA', 'name': to_camel_case(table_name)})\n", "    return entities\n", "\n", "def extract_class_relationships(source_code_str):\n", "    relationships = []\n", "    \n", "    # Class extends\n", "    class_extends_pattern = r'class\\s+(\\w+)\\s+extends\\s+([\\w<>]+)'\n", "    class_matches = re.findall(class_extends_pattern, source_code_str)\n", "    for child_class, parent_class in class_matches:\n", "        parent_class = re.sub(r'<.*?>', '', parent_class).strip()\n", "        if parent_class:\n", "            relationships.append({\n", "                'child': to_camel_case(child_class), \n", "                'parent': to_camel_case(parent_class), \n", "                'type': 'EXTENDS'\n", "            })\n", "    \n", "    # Class implements\n", "    implements_pattern = r'class\\s+(\\w+)(?:\\s+extends\\s+\\w+)?\\s+implements\\s+([\\w<>,\\s]+)'\n", "    impl_matches = re.findall(implements_pattern, source_code_str)\n", "    for class_name, implements_clause in impl_matches:\n", "        interfaces = [part.strip().split('<')[0].strip() for part in implements_clause.split(',')]\n", "        for interface in interfaces:\n", "            if interface:\n", "                relationships.append({\n", "                    'child': to_camel_case(class_name), \n", "                    'parent': to_camel_case(interface), \n", "                    'type': 'IMPLEMENTS'\n", "                })\n", "    \n", "    return relationships\n", "\n", "print(\"✅ Stage 2 functions loaded\")"]}, {"cell_type": "code", "execution_count": 18, "id": "variable_transformation", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Variable transformation functions loaded\n"]}], "source": ["def extract_variable_transformations(source_code_str, file_path):\n", "    \"\"\"Extract variable transformations and operations from Java code\"\"\"\n", "    transformations = []\n", "    application = get_application_from_path(file_path)\n", "    \n", "    # Parse assignment patterns: x = y + z, result = method(a, b), etc.\n", "    assignment_patterns = [\n", "        # Simple assignment: x = y\n", "        r'(\\w+)\\s*=\\s*(\\w+)\\s*;',\n", "        # Arithmetic operations: z = x + y\n", "        r'(\\w+)\\s*=\\s*(\\w+)\\s*([+\\-*/])\\s*(\\w+)\\s*;',\n", "        # Method calls: result = method(param)\n", "        r'(\\w+)\\s*=\\s*(\\w+)\\.(\\w+)\\s*\\(([^)]*)\\)\\s*;',\n", "        # Constructor calls: obj = new Class(params)\n", "        r'(\\w+)\\s*=\\s*new\\s+(\\w+)\\s*\\(([^)]*)\\)\\s*;',\n", "        # String concatenation: str = str1 + str2\n", "        r'(\\w+)\\s*=\\s*(\\w+)\\s*\\+\\s*[\"\\']([^\"\\']*)[\"\\'\\s*;',\n", "    ]\n", "    \n", "    lines = source_code_str.split('\\n')\n", "    current_method = None\n", "    \n", "    for line_num, line in enumerate(lines, 1):\n", "        line = line.strip()\n", "        \n", "        # Track current method context\n", "        method_match = re.search(r'(public|private|protected)\\s+.*\\s+(\\w+)\\s*\\(', line)\n", "        if method_match:\n", "            current_method = to_camel_case(method_match.group(2))\n", "        \n", "        # Skip if no method context\n", "        if not current_method:\n", "            continue\n", "        \n", "        # Check each assignment pattern\n", "        for pattern in assignment_patterns:\n", "            matches = re.findall(pattern, line)\n", "            for match in matches:\n", "                if isinstance(match, tuple) and len(match) >= 2:\n", "                    target_var = match[0]\n", "                    \n", "                    # Skip temp variables\n", "                    if is_temp_variable(target_var):\n", "                        continue\n", "                    \n", "                    target_formatted = format_variable_name(current_method, target_var)\n", "                    \n", "                    if len(match) == 2:  # Simple assignment: x = y\n", "                        source_var = match[1]\n", "                        if not is_temp_variable(source_var):\n", "                            source_formatted = format_variable_name(current_method, source_var)\n", "                            \n", "                            # Create transformation relationship\n", "                            transformations.append({\n", "                                'source_node': source_formatted,\n", "                                'source_type': 'VARIABLE',\n", "                                'destination_node': target_formatted,\n", "                                'destination_type': 'VARIABLE',\n", "                                'relationship': 'TRANSFORMS_TO',\n", "                                'file_path': os.path.relpath(file_path, BASE_PATH),\n", "                                'application': application,\n", "                                'operation_type': 'assignment',\n", "                                'line_number': line_num\n", "                            })\n", "                    \n", "                    <PERSON><PERSON>(match) == 4:  # Arithmetic: z = x + y\n", "                        left_var, operator, right_var = match[1], match[2], match[3]\n", "                        \n", "                        # Create operation node\n", "                        operation_name = to_camel_case(f\"{current_method}_{operator}_operation_{line_num}\")\n", "                        \n", "                        # Input variables -> Operation\n", "                        if not is_temp_variable(left_var):\n", "                            left_formatted = format_variable_name(current_method, left_var)\n", "                            transformations.append({\n", "                                'source_node': left_formatted,\n", "                                'source_type': 'VARIABLE',\n", "                                'destination_node': operation_name,\n", "                                'destination_type': 'OPERATION',\n", "                                'relationship': 'USES',\n", "                                'file_path': os.path.relpath(file_path, BASE_PATH),\n", "                                'application': application,\n", "                                'operation_type': operator,\n", "                                'line_number': line_num\n", "                            })\n", "                        \n", "                        if not is_temp_variable(right_var):\n", "                            right_formatted = format_variable_name(current_method, right_var)\n", "                            transformations.append({\n", "                                'source_node': right_formatted,\n", "                                'source_type': 'VARIABLE',\n", "                                'destination_node': operation_name,\n", "                                'destination_type': 'OPERATION',\n", "                                'relationship': 'USES',\n", "                                'file_path': os.path.relpath(file_path, BASE_PATH),\n", "                                'application': application,\n", "                                'operation_type': operator,\n", "                                'line_number': line_num\n", "                            })\n", "                        \n", "                        # Operation -> Output variable\n", "                        transformations.append({\n", "                            'source_node': operation_name,\n", "                            'source_type': 'OPERATION',\n", "                            'destination_node': target_formatted,\n", "                            'destination_type': 'VARIABLE',\n", "                            'relationship': 'ASSIGNS_VALUE',\n", "                            'file_path': os.path.relpath(file_path, BASE_PATH),\n", "                            'application': application,\n", "                            'operation_type': operator,\n", "                            'line_number': line_num\n", "                        })\n", "                    \n", "                    elif len(match) >= 3:  # Method calls or constructors\n", "                        operation_name = to_camel_case(f\"{current_method}_method_call_{line_num}\")\n", "                        \n", "                        # Method call -> Output variable\n", "                        transformations.append({\n", "                            'source_node': operation_name,\n", "                            'source_type': 'OPERATION',\n", "                            'destination_node': target_formatted,\n", "                            'destination_type': 'VARIABLE',\n", "                            'relationship': 'ASSIGNS_VALUE',\n", "                            'file_path': os.path.relpath(file_path, BASE_PATH),\n", "                            'application': application,\n", "                            'operation_type': 'method_call',\n", "                            'line_number': line_num\n", "                        })\n", "    \n", "    return transformations\n", "\n", "print(\"✅ Variable transformation functions loaded\")"]}, {"cell_type": "code", "execution_count": null, "id": "comprehensive_variable_transformation", "metadata": {}, "outputs": [], "source": ["def get_text_from_node(source_code, node):\n", "    \"\"\"Extract text from a tree-sitter node\"\"\"\n", "    if node is None:\n", "        return None\n", "    return source_code[node.start_byte:node.end_byte]\n", "\n", "def classify_transformation_type(expression_text):\n", "    \"\"\"Classify the type of variable transformation based on expression\"\"\"\n", "    if not expression_text:\n", "        return \"unknown\"\n", "    \n", "    expr = expression_text.strip().lower()\n", "    \n", "    # Classification rules\n", "    if 'new ' in expr:\n", "        return \"object_creation\"\n", "    elif '.substring(' in expr or '.split(' in expr or '.replace(' in expr:\n", "        return \"string_manipulation\"\n", "    elif '+ \"' in expr or '\" +' in expr or 'string.format' in expr:\n", "        return \"string_concatenation\"\n", "    elif 'http' in expr or 'url' in expr or 'api' in expr:\n", "        return \"api_formatting\"\n", "    elif '+' in expr or '-' in expr or '*' in expr or '/' in expr:\n", "        return \"arithmetic\"\n", "    elif '.get(' in expr or '.set(' in expr:\n", "        return \"data_access\"\n", "    elif '(' in expr and ')' in expr:\n", "        return \"method_call\"\n", "    else:\n", "        return \"assignment\"\n", "\n", "def extract_comprehensive_variable_transformations(source_code, file_path):\n", "    \"\"\"Extract comprehensive variable transformations using Tree-sitter AST\"\"\"\n", "    records = []\n", "    application = get_application_from_path(file_path)\n", "    \n", "    try:\n", "        # Parse with tree-sitter\n", "        tree = parser.parse(bytes(source_code, \"utf8\"))\n", "        root = tree.root_node\n", "        \n", "        current_class = None\n", "        current_method = None\n", "        \n", "        def traverse(node):\n", "            nonlocal current_class, current_method\n", "            \n", "            # Track class context\n", "            if node.type == \"class_declaration\":\n", "                for child in node.children:\n", "                    if child.type == \"identifier\":\n", "                        current_class = to_camel_case(get_text_from_node(source_code, child))\n", "                        break\n", "            \n", "            # Track method context\n", "            elif node.type == \"method_declaration\":\n", "                for child in node.children:\n", "                    if child.type == \"identifier\":\n", "                        current_method = to_camel_case(get_text_from_node(source_code, child))\n", "                        break\n", "            \n", "            # Variable declarations: int x = 5;\n", "            elif node.type == \"variable_declarator\":\n", "                name_node = node.child_by_field_name(\"name\")\n", "                value_node = node.child_by_field_name(\"value\")\n", "                \n", "                if name_node and current_class and current_method:\n", "                    var_name = get_text_from_node(source_code, name_node)\n", "                    \n", "                    if not is_temp_variable(var_name):\n", "                        var_formatted = format_variable_name(current_method, var_name)\n", "                        transformed_to = get_text_from_node(source_code, value_node) if value_node else None\n", "                        transformation_type = classify_transformation_type(transformed_to)\n", "                        \n", "                        # Add to CSV records\n", "                        variable_transformation_records.append({\n", "                            'variable_name': var_formatted,\n", "                            'class_name': current_class,\n", "                            'method_name': current_method,\n", "                            'file_path': os.path.basename(file_path),\n", "                            'line_number': node.start_point[0] + 1,\n", "                            'transformation_type': transformation_type,\n", "                            'transformed_from': None,\n", "                            'transformed_to': transformed_to or 'null',\n", "                            'application': application\n", "                        })\n", "                        \n", "                        # Add graph relationships\n", "                        if transformed_to:\n", "                            value_node_name = to_camel_case(f\"{current_method}_{var_name}_value_{node.start_point[0]}\")\n", "                            \n", "                            records.append({\n", "                                'source_node': value_node_name,\n", "                                'source_type': 'VALUE',\n", "                                'destination_node': var_formatted,\n", "                                'destination_type': 'VARIABLE',\n", "                                'relationship': 'ASSIGNS_TO',\n", "                                'file_path': os.path.relpath(file_path, BASE_PATH),\n", "                                'application': application,\n", "                                'transformation_type': transformation_type,\n", "                                'line_number': node.start_point[0] + 1\n", "                            })\n", "            \n", "            # Assignment expressions: x = y + z;\n", "            elif node.type == \"assignment_expression\":\n", "                left_node = node.child_by_field_name(\"left\")\n", "                right_node = node.child_by_field_name(\"right\")\n", "                \n", "                if left_node and right_node and current_class and current_method:\n", "                    left_text = get_text_from_node(source_code, left_node)\n", "                    right_text = get_text_from_node(source_code, right_node)\n", "                    \n", "                    if not is_temp_variable(left_text):\n", "                        var_formatted = format_variable_name(current_method, left_text)\n", "                        transformation_type = classify_transformation_type(right_text)\n", "                        \n", "                        # Add to CSV records\n", "                        variable_transformation_records.append({\n", "                            'variable_name': var_formatted,\n", "                            'class_name': current_class,\n", "                            'method_name': current_method,\n", "                            'file_path': os.path.basename(file_path),\n", "                            'line_number': node.start_point[0] + 1,\n", "                            'transformation_type': transformation_type,\n", "                            'transformed_from': left_text,\n", "                            'transformed_to': right_text,\n", "                            'application': application\n", "                        })\n", "                        \n", "                        # Add graph relationships\n", "                        if transformation_type in ['arithmetic', 'string_concatenation', 'method_call']:\n", "                            # Create transformation node\n", "                            transform_node = to_camel_case(f\"{current_method}_{transformation_type}_{node.start_point[0]}\")\n", "                            \n", "                            records.append({\n", "                                'source_node': var_formatted,\n", "                                'source_type': 'VARIABLE',\n", "                                'destination_node': transform_node,\n", "                                'destination_type': 'TRANSFORMATION',\n", "                                'relationship': 'UNDERGOES',\n", "                                'file_path': os.path.relpath(file_path, BASE_PATH),\n", "                                'application': application,\n", "                                'transformation_type': transformation_type,\n", "                                'line_number': node.start_point[0] + 1\n", "                            })\n", "                            \n", "                            records.append({\n", "                                'source_node': transform_node,\n", "                                'source_type': 'TRANSFORMATION',\n", "                                'destination_node': var_formatted,\n", "                                'destination_type': 'VARIABLE',\n", "                                'relationship': 'PRODUCES',\n", "                                'file_path': os.path.relpath(file_path, BASE_PATH),\n", "                                'application': application,\n", "                                'transformation_type': transformation_type,\n", "                                'line_number': node.start_point[0] + 1\n", "                            })\n", "            \n", "            # Recursively process children\n", "            for child in node.children:\n", "                traverse(child)\n", "        \n", "        traverse(root)\n", "        return records\n", "        \n", "    except Exception as e:\n", "        print(f\"⚠️ Error in comprehensive variable transformation extraction: {e}\")\n", "        return []\n", "\n", "print(\"✅ Comprehensive variable transformation functions loaded\")"]}, {"cell_type": "code", "execution_count": 19, "id": "stage2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Enhanced Stage 2 functions loaded\n"]}], "source": ["# STAGE 2: CLA<PERSON> REGISTRY & AST EXTRACTION WITH VARIABLE TRANSFORMATIONS\n", "def build_class_registry():\n", "    global class_registry\n", "    \n", "    for root, _, files in os.walk(BASE_PATH):\n", "        for file in files:\n", "            if file.endswith('.java'):\n", "                file_path = os.path.join(root, file)\n", "                application = get_application_from_path(file_path)\n", "                \n", "                try:\n", "                    with open(file_path, 'r', encoding='utf-8') as f:\n", "                        source_code_str = f.read()\n", "                    \n", "                    class_name = to_camel_case(os.path.splitext(file)[0])\n", "                    package_name, imports = extract_package_and_imports(source_code_str)\n", "                    fqcn = f\"{package_name}.{class_name}\" if package_name else class_name\n", "                    \n", "                    endpoints = extract_api_endpoints(source_code_str)\n", "                    db_entities = extract_database_entities(source_code_str)\n", "                    class_relationships = extract_class_relationships(source_code_str)\n", "                    \n", "                    class_registry[fqcn] = {\n", "                        'fqcn': fqcn, 'class_name': class_name, 'package': package_name,\n", "                        'file_path': file_path, 'application': application, 'imports': imports,\n", "                        'endpoints': endpoints, 'db_entities': db_entities, 'class_relationships': class_relationships\n", "                    }\n", "                    \n", "                    # Add relationships with camelCase naming\n", "                    if endpoints:\n", "                        for ep in endpoints:\n", "                            all_relationships.append({\n", "                                'source_node': class_name, 'source_type': 'CLASS',\n", "                                'destination_node': ep['name'], 'destination_type': 'ENDPOINT',\n", "                                'relationship': 'EXPOSES', 'file_path': os.path.relpath(file_path, BASE_PATH),\n", "                                'application': application\n", "                            })\n", "                    \n", "                    if db_entities:\n", "                        for entity in db_entities:\n", "                            all_relationships.append({\n", "                                'source_node': class_name, 'source_type': 'CLASS',\n", "                                'destination_node': entity['name'], 'destination_type': 'DATA',\n", "                                'relationship': 'DATA_FIND', 'file_path': os.path.relpath(file_path, BASE_PATH),\n", "                                'application': application\n", "                            })\n", "                    \n", "                    if class_relationships:\n", "                        for rel in class_relationships:\n", "                            all_relationships.append({\n", "                                'source_node': rel['child'], 'source_type': 'CLASS',\n", "                                'destination_node': rel['parent'], \n", "                                'destination_type': 'INTERFACE' if rel['type'] == 'IMPLEMENTS' else 'CLASS',\n", "                                'relationship': rel['type'], 'file_path': os.path.relpath(file_path, BASE_PATH),\n", "                                'application': application\n", "                            })\n", "                \n", "                except Exception as e:\n", "                    print(f\"❌ Error processing {file_path}: {e}\")\n", "                    continue\n", "    \n", "    return class_registry\n", "\n", "def read_source_code(file_path):\n", "    with open(file_path, 'rb') as f:\n", "        return f.read()\n", "\n", "def extract_enhanced_ast_structure(file_path):\n", "    \"\"\"Enhanced AST extraction with variable transformations\"\"\"\n", "    records = []\n", "    source_code = read_source_code(file_path)\n", "    tree = parser.parse(source_code)\n", "    root_node = tree.root_node\n", "    file_name = to_camel_case(os.path.basename(file_path))\n", "    application = get_application_from_path(file_path)\n", "\n", "    def traverse(node, parent_type=None, parent_name=None, method_context=None):\n", "        if node.type == 'class_declaration':\n", "            class_name = None\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    class_name = to_camel_case(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "                    records.append({\n", "                        'source_node': file_name, 'source_type': 'FILE',\n", "                        'destination_node': class_name, 'destination_type': 'CLASS',\n", "                        'relationship': 'DECLARES', 'file_path': os.path.relpath(file_path, BASE_PATH),\n", "                        'application': application\n", "                    })\n", "                    break\n", "            for child in node.children:\n", "                traverse(child, 'class', class_name, None)\n", "\n", "        elif node.type == 'interface_declaration':\n", "            interface_name = None\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    interface_name = to_camel_case(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "                    records.append({\n", "                        'source_node': file_name, 'source_type': 'FILE',\n", "                        'destination_node': interface_name, 'destination_type': 'INTERFACE',\n", "                        'relationship': 'DECLARES', 'file_path': os.path.relpath(file_path, BASE_PATH),\n", "                        'application': application\n", "                    })\n", "                    break\n", "            for child in node.children:\n", "                traverse(child, 'interface', interface_name, None)\n", "\n", "        elif node.type == 'method_declaration':\n", "            method_name = None\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    method_name = to_camel_case(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "                    if parent_name and parent_type in ['class', 'interface']:\n", "                        records.append({\n", "                            'source_node': parent_name, 'source_type': parent_type.upper(),\n", "                            'destination_node': method_name, 'destination_type': 'METHOD',\n", "                            'relationship': 'DECLARES', 'file_path': os.path.relpath(file_path, BASE_PATH),\n", "                            'application': application\n", "                        })\n", "                    break\n", "            for child in node.children:\n", "                traverse(child, 'method', method_name, method_name)\n", "\n", "        elif node.type == 'field_declaration':\n", "            for child in node.children:\n", "                if child.type == 'variable_declarator':\n", "                    for grandchild in child.children:\n", "                        if grandchild.type == 'identifier':\n", "                            field_name = to_camel_case(source_code[grandchild.start_byte:grandchild.end_byte].decode('utf-8'))\n", "                            if parent_name and parent_type == 'class' and not is_temp_variable(field_name):\n", "                                formatted_var_name = format_variable_name(parent_name, field_name)\n", "                                records.append({\n", "                                    'source_node': parent_name, 'source_type': 'CLASS',\n", "                                    'destination_node': formatted_var_name, 'destination_type': 'VARIABLE',\n", "                                    'relationship': 'HAS_FIELD', 'file_path': os.path.relpath(file_path, BASE_PATH),\n", "                                    'application': application\n", "                                })\n", "\n", "        elif node.type in ['assignment_expression', 'variable_declarator'] and parent_type == 'method':\n", "            for child in node.children:\n", "                if child.type == 'identifier':\n", "                    var_name = to_camel_case(source_code[child.start_byte:child.end_byte].decode('utf-8'))\n", "                    if var_name and var_name != 'this' and not is_temp_variable(var_name) and method_context:\n", "                        formatted_var_name = format_variable_name(method_context, var_name)\n", "                        records.append({\n", "                            'source_node': parent_name, 'source_type': 'METHOD',\n", "                            'destination_node': formatted_var_name, 'destination_type': 'VARIABLE',\n", "                            'relationship': 'DECLARES_VARIABLE', 'file_path': os.path.relpath(file_path, BASE_PATH),\n", "                            'application': application\n", "                        })\n", "\n", "        for child in node.children:\n", "            traverse(child, parent_type, parent_name, method_context)\n", "\n", "    traverse(root_node)\n", "    \n", "    # Add comprehensive variable transformations\n", "    try:\n", "        with open(file_path, 'r', encoding='utf-8') as f:\n", "            source_code_str = f.read()\n", "        transformations = extract_comprehensive_variable_transformations(source_code_str, file_path)\n", "        records.extend(transformations)\n", "    except Exception as e:\n", "        print(f\"⚠️ Could not extract comprehensive transformations from {file_path}: {e}\")\n", "    \n", "    return records\n", "\n", "print(\"✅ Enhanced Stage 2 functions loaded\")"]}, {"cell_type": "code", "execution_count": 20, "id": "stage2_execution", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 Extracting AST structure with variable transformations...\n", "⚠️ Could not extract transformations from C:\\Shaik\\sample\\OneInsights\\ServiceBolt\\api\\BuildToolController.java: unterminated character set at position 39\n", "⚠️ Could not extract transformations from C:\\Shaik\\sample\\OneInsights\\ServiceBolt\\service\\BuildToolServiceImplemantation.java: unterminated character set at position 39\n", "⚠️ Could not extract transformations from C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\core\\model\\BaseModel.java: unterminated character set at position 39\n", "⚠️ Could not extract transformations from C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\core\\model\\BuildFailurePatternForProjectInJenkinsModel.java: unterminated character set at position 39\n", "⚠️ Could not extract transformations from C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\core\\model\\BuildFailurePatternMetrics.java: unterminated character set at position 39\n", "⚠️ Could not extract transformations from C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\core\\model\\BuildFileInfo.java: unterminated character set at position 39\n", "⚠️ Could not extract transformations from C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\core\\model\\BuildInfo.java: unterminated character set at position 39\n", "⚠️ Could not extract transformations from C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\core\\model\\BuildSteps.java: unterminated character set at position 39\n", "⚠️ Could not extract transformations from C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\core\\model\\BuildTool.java: unterminated character set at position 39\n", "⚠️ Could not extract transformations from C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\core\\model\\BuildToolMetric.java: unterminated character set at position 39\n", "⚠️ Could not extract transformations from C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\core\\model\\ConfigurationSetting.java: unterminated character set at position 39\n", "⚠️ Could not extract transformations from C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\core\\model\\ConfigurationToolInfoMetric.java: unterminated character set at position 39\n", "⚠️ Could not extract transformations from C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\githubaction\\GithubActionApplication.java: unterminated character set at position 39\n", "⚠️ Could not extract transformations from C:\\Shaik\\sample\\OneInsights\\UnifiedBolt\\githubaction\\GithubActionImplementation.java: unterminated character set at position 39\n", "\n", "📊 Stage 2 Complete:\n", "  • Total relationships: 891\n", "  • Variable transformations: 0\n", "  • Operation nodes: 0\n", "  • Relationship types: ['CONTAINS', 'DECLARES', 'DECLARES_VARIABLE', 'EXPOSES', 'EXTENDS', 'HAS_FIELD', 'IMPLEMENTS']\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>source_node</th>\n", "      <th>source_type</th>\n", "      <th>destination_node</th>\n", "      <th>destination_type</th>\n", "      <th>relationship</th>\n", "      <th>file_path</th>\n", "      <th>application</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>886</th>\n", "      <td>createHeaders</td>\n", "      <td>METHOD</td>\n", "      <td>createHeadersAuth</td>\n", "      <td>VARIABLE</td>\n", "      <td>DECLARES_VARIABLE</td>\n", "      <td>UnifiedBolt\\githubaction\\GithubActionImplement...</td>\n", "      <td>unifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>887</th>\n", "      <td>createHeaders</td>\n", "      <td>METHOD</td>\n", "      <td>createHeadersEncodedAuth</td>\n", "      <td>VARIABLE</td>\n", "      <td>DECLARES_VARIABLE</td>\n", "      <td>UnifiedBolt\\githubaction\\GithubActionImplement...</td>\n", "      <td>unifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>888</th>\n", "      <td>createHeaders</td>\n", "      <td>METHOD</td>\n", "      <td>createHeadersAuthHeader</td>\n", "      <td>VARIABLE</td>\n", "      <td>DECLARES_VARIABLE</td>\n", "      <td>UnifiedBolt\\githubaction\\GithubActionImplement...</td>\n", "      <td>unifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>889</th>\n", "      <td>createHeaders</td>\n", "      <td>METHOD</td>\n", "      <td>createHeadersHeaders</td>\n", "      <td>VARIABLE</td>\n", "      <td>DECLARES_VARIABLE</td>\n", "      <td>UnifiedBolt\\githubaction\\GithubActionImplement...</td>\n", "      <td>unifiedBolt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>890</th>\n", "      <td>get</td>\n", "      <td>METHOD</td>\n", "      <td>getRequestFactory</td>\n", "      <td>VARIABLE</td>\n", "      <td>DECLARES_VARIABLE</td>\n", "      <td>UnifiedBolt\\githubaction\\GithubActionImplement...</td>\n", "      <td>unifiedBolt</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       source_node source_type          destination_node destination_type  \\\n", "886  createHeaders      METHOD         createHeadersAuth         VARIABLE   \n", "887  createHeaders      METHOD  createHeadersEncodedAuth         VARIABLE   \n", "888  createHeaders      METHOD   createHeadersAuthHeader         VARIABLE   \n", "889  createHeaders      METHOD      createHeadersHeaders         VARIABLE   \n", "890            get      METHOD         getRequestFactory         VARIABLE   \n", "\n", "          relationship                                          file_path  \\\n", "886  DECLARES_VARIABLE  UnifiedBolt\\githubaction\\GithubActionImplement...   \n", "887  DECLARES_VARIABLE  UnifiedBolt\\githubaction\\GithubActionImplement...   \n", "888  DECLARES_VARIABLE  UnifiedBolt\\githubaction\\GithubActionImplement...   \n", "889  DECLARES_VARIABLE  UnifiedBolt\\githubaction\\GithubActionImplement...   \n", "890  DECLARES_VARIABLE  UnifiedBolt\\githubaction\\GithubActionImplement...   \n", "\n", "     application  \n", "886  unifiedBolt  \n", "887  unifiedBolt  \n", "888  unifiedBolt  \n", "889  unifiedBolt  \n", "890  unifiedBolt  "]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["# Execute Stage 2 with Variable Transformations\n", "build_class_registry()\n", "ast_records = []\n", "\n", "print(\"🔍 Extracting AST structure with variable transformations...\")\n", "for root, _, files in os.walk(BASE_PATH):\n", "    for file in files:\n", "        if file.endswith('.java'):\n", "            file_path = os.path.join(root, file)\n", "            try:\n", "                file_records = extract_enhanced_ast_structure(file_path)\n", "                ast_records.extend(file_records)\n", "                \n", "                # Count transformations for this file\n", "                transform_count = len([r for r in file_records if r.get('operation_type')])\n", "                if transform_count > 0:\n", "                    print(f\"  ✅ {os.path.basename(file_path)}: {transform_count} variable transformations\")\n", "                    \n", "            except Exception as e:\n", "                print(f'❌ Failed to parse {file}: {e}')\n", "\n", "all_relationships.extend(ast_records)\n", "df_stage2 = pd.DataFrame(all_relationships)\n", "df_stage2 = normalize_all_names(df_stage2)\n", "df_stage2.to_csv('stage2_class_ast.csv', index=False)\n", "\n", "# Show transformation statistics\n", "transformation_count = len([r for r in ast_records if 'operation_type' in r])\n", "operation_count = len(df_stage2[df_stage2['destination_type'] == 'OPERATION'])\n", "\n", "# Generate Variable Transformations CSV\n", "if variable_transformation_records:\n", "    df_variable_transformations = pd.DataFrame(variable_transformation_records)\n", "    df_variable_transformations.to_csv('variable_transformations.csv', index=False)\n", "    print(f\"\\n📁 Variable Transformations CSV Generated:\")\n", "    print(f\"  • File: variable_transformations.csv\")\n", "    print(f\"  • Records: {len(df_variable_transformations)}\")\n", "    print(f\"  • Transformation types: {sorted(df_variable_transformations['transformation_type'].unique())}\")\n", "    print(f\"\\n📋 Sample variable transformations:\")\n", "    print(df_variable_transformations.head())\n", "else:\n", "    print(\"\\n⚠️ No variable transformations found\")\n", "\n", "print(f\"\\n📊 Stage 2 Complete:\")\n", "print(f\"  • Total relationships: {len(df_stage2)}\")\n", "print(f\"  • Variable transformations: {transformation_count}\")\n", "print(f\"  • Operation nodes: {operation_count}\")\n", "print(f\"  • Relationship types: {sorted(df_stage2['relationship'].unique())}\")\n", "df_stage2.tail()"]}, {"cell_type": "code", "execution_count": null, "id": "stage3_functions", "metadata": {}, "outputs": [], "source": ["def build_enhanced_system_prompt(file_path, ast_context, class_registry):\n", "    application = get_application_from_path(file_path)\n", "    \n", "    ast_context_str = \"\\n\".join([\n", "        f\"- {item['source_type']} '{item['source_node']}' {item['relationship']} {item['destination_type']} '{item['destination_node']}'\"\n", "        for item in ast_context[:20]\n", "    ])\n", "    \n", "    # Use class registry to get additional context\n", "    class_info = \"\"\n", "    file_name = to_camel_case(os.path.basename(file_path).replace('.java', ''))\n", "    for fqcn, info in class_registry.items():\n", "        if info['class_name'] == file_name:\n", "            if info.get('endpoints'):\n", "                class_info += f\"\\nEndpoints: {[ep['name'] for ep in info['endpoints']]}\"\n", "            if info.get('db_entities'):\n", "                class_info += f\"\\nDB Entities: {[e['name'] for e in info['db_entities']]}\"\n", "            break\n", "    \n", "    return f\"\"\"\n", "You are analyzing Java code for lineage extraction in application: {application}.\n", "\n", "CONTEXT FROM AST ANALYSIS:\n", "{ast_context_str}\n", "\n", "CLASS REGISTRY CONTEXT:\n", "{class_info}\n", "\n", "CRITICAL RULES - <PERSON><PERSON><PERSON><PERSON> EXACTLY:\n", "1. Use camelCase naming for ALL nodes (e.g., buildToolController, not BuildToolController)\n", "2. <PERSON><PERSON><PERSON><PERSON><PERSON> NODE TYPES: FOLDERS, FILE, CLASS, METHOD, INTERFACE, VARIABLE, ENDPOINT, DATA, OPERATION, VALUE\n", "3. <PERSON><PERSON><PERSON><PERSON><PERSON> RELATIONSHIPS: <PERSON><PERSON><PERSON><PERSON><PERSON>, DATA_FIND, DECLARES, DECLARES_VARIABLE, EXPOSES, EXTENDS, HAS_FIELD, IMPLEMENTS, TRA<PERSON><PERSON>ORMS_TO, USES, ASSIGNS_VALUE, UPDATED_BY\n", "4. VARIABLE NAMING: Format as methodName.variableName (e.g., \"getUserData.userId\")\n", "5. FILTER OUT: Loop counters (i, j, k), temp variables, variables starting with _ or $\n", "6. VARIABLE TRANSFORMATIONS: Track operations like:\n", "   - x = 10 → CREATE variable x with VALUE 10\n", "   - z = x + y → x USES operation, y USES operation, operation ASSIGNS_VALUE z\n", "   - result = method(param) → param USES operation, operation ASSIGNS_VALUE result\n", "7. OPERATION NODES: Create for arithmetic (+, -, *, /), method calls, assignments\n", "8. FOCUS ON: Variable transformations, method calls, data operations, assignments\n", "9. Application context: {application}\n", "\n", "Extract ALL meaningful relationships following these exact patterns with camelCase naming.\n", "\"\"\"\n", "\n", "def get_file_size_lines(file_path):\n", "    try:\n", "        with open(file_path, 'r', encoding='utf-8') as f:\n", "            return len(f.readlines())\n", "    except:\n", "        return 0\n", "\n", "def extract_class_metadata(file_path):\n", "    try:\n", "        with open(file_path, 'r', encoding='utf-8') as f:\n", "            content = f.read()\n", "        \n", "        package_match = re.search(r'package\\s+([\\w\\.]+);', content)\n", "        package = package_match.group(1) if package_match else None\n", "        \n", "        imports = re.findall(r'import\\s+([\\w\\.]+);', content)\n", "        class_names = re.findall(r'(?:public\\s+)?(?:class|interface)\\s+(\\w+)', content)\n", "        \n", "        return {\n", "            'package': package,\n", "            'imports': [to_camel_case(imp.split('.')[-1]) for imp in imports[:10]],\n", "            'classes': [to_camel_case(cls) for cls in class_names],\n", "            'file_name': to_camel_case(os.path.basename(file_path))\n", "        }\n", "    except:\n", "        return {'package': None, 'imports': [], 'classes': [], 'file_name': to_camel_case(os.path.basename(file_path))}\n", "\n", "print(\"✅ Stage 3 functions loaded\")"]}, {"cell_type": "code", "execution_count": null, "id": "stage3_execution", "metadata": {}, "outputs": [], "source": ["# STAGE 3: HYBRID LLM PROCESSING (Simplified for demo)\n", "llm_relationships = []\n", "java_files = []\n", "\n", "for root, _, files in os.walk(BASE_PATH):\n", "    for file in files:\n", "        if file.endswith('.java'):\n", "            java_files.append(os.path.join(root, file))\n", "\n", "print(f\"Processing {len(java_files)} Java files with LLM (first 3 for demo)...\")\n", "\n", "# Process first 3 files for demonstration\n", "for file_path in tqdm(java_files[:3], desc=\"LLM Processing\"):\n", "    try:\n", "        file_ast_context = [rel for rel in ast_records if rel.get('file_path') == os.path.relpath(file_path, BASE_PATH)]\n", "        \n", "        # Simple LLM processing (using existing logic)\n", "        loader = TextLoader(file_path)\n", "        docs = loader.load()\n", "        \n", "        splitter = RecursiveCharacterTextSplitter.from_language(\n", "            language=LC_Language.JAVA, chunk_size=4000, chunk_overlap=200\n", "        )\n", "        split_docs = splitter.split_documents(docs)\n", "        \n", "        system_prompt = build_enhanced_system_prompt(file_path, file_ast_context, class_registry)\n", "        application = get_application_from_path(file_path)\n", "\n", "        transformer = LLMGraphTransformer(\n", "            llm=llm, additional_instructions=system_prompt,\n", "            allowed_nodes=['FOLDERS', 'FILE', 'CLASS', 'METHOD', 'INTERFACE', 'VARIABLE', 'ENDPOINT', 'DATA', 'OPERATION', 'VALUE', 'EXPRESSION', 'TRANSFORMATION'],\n", "            allowed_relationships=[\n", "                ('FOLDERS', 'CONTAINS', 'FILE'), ('FILE', 'DECLARES', 'CLASS'), ('FILE', 'DECLARES', 'INTERFACE'),\n", "                ('CLASS', 'DECLARES', 'METHOD'), ('INTERFACE', 'DECLARES', 'METHOD'),\n", "                ('CLASS', 'HAS_FIELD', 'VARIABLE'), ('METHOD', 'DECLARES_VARIABLE', 'VARIABLE'),\n", "                ('METHOD', 'USES', 'VARIABLE'), ('VARIABLE', 'TRANSFORMS_TO', 'VARIABLE'),\n", "                ('OPERATION', 'ASSIGNS_VALUE', 'VARIABLE'), ('VARIABLE', 'UPDATED_BY', 'OPERATION'),\n", "                ('CLASS', 'EXTENDS', 'CLASS'), ('CLASS', 'IMPLEMENTS', 'INTERFACE'),\n", "                ('CLASS', 'EXPOSES', 'ENDPOINT'), ('CLASS', 'DATA_FIND', 'DATA'),\n", "                ('VALUE', 'ASSIGNS_TO', 'VARIABLE'), ('VARIABLE', 'UNDERGOES', 'TRANSFORMATION'),\n", "                ('TRANSFORMATION', 'PRODUCES', 'VARIABLE'), ('EXPRESSION', 'EVALUATES_TO', 'VALUE')\n", "            ]\n", "        )\n", "        \n", "        graph_docs = transformer.convert_to_graph_documents(split_docs)\n", "        \n", "        file_llm_rels = []\n", "        for doc in graph_docs:\n", "            for rel in doc.relationships:\n", "                file_llm_rels.append({\n", "                    'source_node': to_camel_case(rel.source.id), 'source_type': rel.source.type,\n", "                    'destination_node': to_camel_case(rel.target.id), 'destination_type': rel.target.type,\n", "                    'relationship': rel.type, 'file_path': os.path.relpath(file_path, BASE_PATH),\n", "                    'application': application\n", "                })\n", "        \n", "        llm_relationships.extend(file_llm_rels)\n", "        print(f\"✅ Processed {os.path.basename(file_path)}: {len(file_llm_rels)} relationships\")\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error processing {os.path.basename(file_path)}: {e}\")\n", "\n", "all_relationships.extend(llm_relationships)\n", "df_stage3 = pd.DataFrame(llm_relationships)\n", "if len(df_stage3) > 0:\n", "    df_stage3 = normalize_all_names(df_stage3)\n", "    df_stage3.to_csv('stage3_llm.csv', index=False)\n", "\n", "print(f\"\\n📊 Stage 3 Complete: {len(df_stage3)} LLM relationships\")\n", "if len(df_stage3) > 0:\n", "    df_stage3.head()"]}, {"cell_type": "code", "execution_count": null, "id": "final_processing", "metadata": {}, "outputs": [], "source": ["# FINAL: COMBINE STAGE 1 + STAGE 3 WITH CONSISTENT NAMING\n", "stage1_rels = df_stage1.to_dict('records')\n", "stage3_rels = df_stage3.to_dict('records') if len(df_stage3) > 0 else []\n", "\n", "# Combine only Stage 1 and Stage 3 (excluding Stage 2 AST data)\n", "final_relationships = stage1_rels + stage3_rels\n", "df_final = pd.DataFrame(final_relationships)\n", "\n", "if len(df_final) > 0:\n", "    df_final = normalize_all_names(df_final)\n", "    \n", "    # Remove duplicates\n", "    df_final = df_final.drop_duplicates(subset=['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship'])\n", "    \n", "    # Save final combined data\n", "    df_final.to_csv('final_combined_v11.csv', index=False)\n", "    \n", "    print(f\"📊 Final Combined Data:\")\n", "    print(f\"  • Total relationships: {len(df_final)}\")\n", "    print(f\"  • Applications: {df_final['application'].nunique()}\")\n", "    print(f\"  • Node types: {sorted(df_final['destination_type'].unique())}\")\n", "    print(f\"  • Relationship types: {sorted(df_final['relationship'].unique())}\")\n", "    print(f\"  • All names in camelCase format ✅\")\n", "    \n", "    df_final.sample(10) if len(df_final) >= 10 else df_final.head()\n", "else:\n", "    print(\"⚠️ No final data to process\")"]}, {"cell_type": "code", "execution_count": null, "id": "neo4j_push", "metadata": {}, "outputs": [], "source": ["# NEO4J PUSH WITH CONSISTENT COLORS\n", "if len(df_final) > 0:\n", "    print(\"🧹 Clearing existing Neo4j data...\")\n", "    graph.query(\"MATCH (n) DETACH DELETE n\")\n", "\n", "    print(\"📤 Pushing relationships to Neo4j with consistent colors...\")\n", "\n", "    # Create unique nodes first with consistent colors\n", "    nodes = set()\n", "    for _, row in df_final.iterrows():\n", "        nodes.add((row['source_node'], row['source_type'], row.get('application', 'unknown')))\n", "        nodes.add((row['destination_node'], row['destination_type'], row.get('application', 'unknown')))\n", "\n", "    # Create nodes with consistent colors\n", "    for node_id, node_type, app in tqdm(nodes, desc=\"Creating nodes\"):\n", "        color = NODE_COLORS.get(node_type, '#9E9E9E')  # Default grey if type not found\n", "        \n", "        query = f\"\"\"\n", "        MERGE (n:{node_type} {{id: $node_id, application: $app}})\n", "        SET n.name = $node_id, n.color = $color, n.type = $node_type\n", "        \"\"\"\n", "        graph.query(query, {\"node_id\": node_id, \"app\": app, \"color\": color})\n", "\n", "    # Create relationships\n", "    for _, row in tqdm(df_final.iterrows(), desc=\"Creating relationships\", total=len(df_final)):\n", "        query = f\"\"\"\n", "        MATCH (source:{row['source_type']} {{id: $source_id}})\n", "        MATCH (target:{row['destination_type']} {{id: $target_id}})\n", "        MERGE (source)-[r:{row['relationship']}]->(target)\n", "        SET r.file_path = $file_path, r.application = $application\n", "        \"\"\"\n", "        graph.query(query, {\n", "            \"source_id\": row['source_node'],\n", "            \"target_id\": row['destination_node'],\n", "            \"file_path\": row.get('file_path', ''),\n", "            \"application\": row.get('application', 'unknown')\n", "        })\n", "\n", "    # Verify data\n", "    node_count = graph.query(\"MATCH (n) RETURN count(n) as count\")[0]['count']\n", "    rel_count = graph.query(\"MATCH ()-[r]->() RETURN count(r) as count\")[0]['count']\n", "    \n", "    # Get node type distribution\n", "    node_types = graph.query(\"\"\"\n", "        MATCH (n) \n", "        RETURN labels(n)[0] as type, count(n) as count, n.color as color\n", "        ORDER BY count DESC\n", "    \"\"\")\n", "\n", "    # Load Variable Transformations CSV into Neo4j\n", "    if variable_transformation_records:\n", "        print(\"\\n📊 Loading Variable Transformations into Neo4j...\")\n", "        \n", "        for record in tqdm(variable_transformation_records, desc=\"Loading variable transformations\"):\n", "            # Create variable node\n", "            var_color = NODE_COLORS.get('VARIABLE', '#00BCD4')\n", "            graph.query(\"\"\"\n", "                MERGE (v:VARIABLE {id: $var_name, class: $class_name, method: $method_name})\n", "                SET v.name = $var_name, v.color = $var_color, v.type = 'VARIABLE'\n", "            \"\"\", {\n", "                \"var_name\": record['variable_name'],\n", "                \"class_name\": record['class_name'],\n", "                \"method_name\": record['method_name'],\n", "                \"var_color\": var_color\n", "            })\n", "            \n", "            # Create transformation node if complex transformation\n", "            if record['transformation_type'] in ['string_concatenation', 'api_formatting', 'arithmetic', 'method_call']:\n", "                transform_color = NODE_COLORS.get('TRANSFORMATION', '#3F51B5')\n", "                transform_id = f\"{record['class_name']}_{record['method_name']}_{record['transformation_type']}_{record['line_number']}\"\n", "                \n", "                graph.query(\"\"\"\n", "                    MERGE (t:TRANSFORMATION {id: $transform_id})\n", "                    SET t.name = $transform_id, t.type = $transform_type, t.color = $transform_color,\n", "                        t.line_number = $line_number, t.file_path = $file_path\n", "                \"\"\", {\n", "                    \"transform_id\": transform_id,\n", "                    \"transform_type\": record['transformation_type'],\n", "                    \"transform_color\": transform_color,\n", "                    \"line_number\": record['line_number'],\n", "                    \"file_path\": record['file_path']\n", "                })\n", "                \n", "                # Create relationships: Variable -> Transformation -> Variable\n", "                graph.query(\"\"\"\n", "                    MATCH (v:VARIABLE {id: $var_name})\n", "                    MATCH (t:TRANSFORMATION {id: $transform_id})\n", "                    MERGE (v)-[r1:UNDERGOES]->(t)\n", "                    MERGE (t)-[r2:PRODUCES]->(v)\n", "                    SET r1.line_number = $line_number, r2.line_number = $line_number\n", "                \"\"\", {\n", "                    \"var_name\": record['variable_name'],\n", "                    \"transform_id\": transform_id,\n", "                    \"line_number\": record['line_number']\n", "                })\n", "        \n", "        var_transform_count = len(variable_transformation_records)\n", "        print(f\"✅ Loaded {var_transform_count} variable transformations into Neo4j\")\n", "    \n", "    print(f\"\\n✅ Neo4j push complete!\")\n", "    print(f\"📊 Created {node_count} nodes and {rel_count} relationships\")\n", "    print(f\"🎨 Node types with consistent colors:\")\n", "    for nt in node_types:\n", "        print(f\"  • {nt['type']}: {nt['count']} nodes (Color: {nt['color']})\")\n", "    \n", "    print(f\"\\n🌐 Access Neo4j Browser at: http://localhost:7474\")\n", "    print(f\"🔍 Database: {NEO4J_DB}\")\n", "    print(f\"\\n🎉 FINAL_V11 PIPELINE COMPLETE!\")\n", "    print(f\"\\n✅ All 3 issues fixed + Variable Tracking:\")\n", "    print(f\"  1. ✅ Consistent camelCase naming across all stages\")\n", "    print(f\"  2. ✅ Fixed node colors in Neo4j (no more changing colors)\")\n", "    print(f\"  3. ✅ Variable transformations captured (x->operation->z)\")\n", "    print(f\"  4. ✅ Comprehensive variable tracking CSV generated\")\n", "    print(f\"  5. ✅ Variable transformations loaded into Neo4j\")\n", "else:\n", "    print(\"⚠️ No data to push to Neo4j\")"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 4}