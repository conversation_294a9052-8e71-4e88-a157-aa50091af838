variable_name,class_name,method_name,file_path,line_number,transformation_type,transformed_from,transformed_to,application
jobList.buildType,buildToolController,jobList,BuildToolController.java,93,method_call,,getBuildType(proName[0]),serviceBolt
saveBuildFailurePattern.obj,buildToolController,saveBuildFailurePattern,BuildToolController.java,101,object_creation,,new BuildToolFailureSettingReq(),serviceBolt
fetchFailurePatternData.obj,buildToolController,fetchFailurePatternData,BuildToolController.java,120,object_creation,,new BuildFailureRequest(),serviceBolt
fetchFailurePatternDataCopy.obj,buildToolController,fetchFailurePatternDataCopy,BuildToolController.java,130,object_creation,,new BuildFailureRequest(),serviceBolt
getBuildType.buildType,buildToolController,getBuildType,BuildToolController.java,147,assignment,,"""""",serviceBolt
getBuildType.context,buildToolController,getBuildType,BuildToolController.java,148,object_creation,,new AnnotationConfigApplicationContext(DataConfig.class),serviceBolt
getBuildType.configurationRepo,buildToolController,getBuildType,BuildToolController.java,149,method_call,,context.getBean(ConfigurationSettingRep.class),serviceBolt
getBuildType.configurationColection,buildToolController,getBuildType,BuildToolController.java,150,data_access,,configurationRepo.findByProjectName(projectName).get(0),serviceBolt
getBuildType.metric,buildToolController,getBuildType,BuildToolController.java,151,method_call,,configurationColection.getMetrics(),serviceBolt
getBuildType.iter,buildToolController,getBuildType,BuildToolController.java,152,method_call,,metric.iterator(),serviceBolt
getBuildType.configuration1,buildToolController,getBuildType,BuildToolController.java,154,method_call,,iter.next(),serviceBolt
getBuildType.metric1,buildToolController,getBuildType,BuildToolController.java,155,method_call,,(ConfigurationToolInfoMetric) configuration1,serviceBolt
getBuildType.buildType,buildToolController,getBuildType,BuildToolController.java,158,assignment,buildType,"""TEAMCITY""",serviceBolt
getBuildType.buildType,buildToolController,getBuildType,BuildToolController.java,161,assignment,buildType,"""JENKINS""",serviceBolt
getBuildType.buildType,buildToolController,getBuildType,BuildToolController.java,164,assignment,buildType,"""CircleCI""",serviceBolt
getBuildType.buildType,buildToolController,getBuildType,BuildToolController.java,167,assignment,buildType,"""TFSBUILD""",serviceBolt
search.lastUpdate,buildToolServiceImplemantation,search,BuildToolServiceImplemantation.java,53,assignment,,1,serviceBolt
search.result,buildToolServiceImplemantation,search,BuildToolServiceImplemantation.java,54,method_call,,buildToolRepository.findByName(projectName),serviceBolt
search.lastUpdate,buildToolServiceImplemantation,search,BuildToolServiceImplemantation.java,67,assignment,,1,serviceBolt
search.result,buildToolServiceImplemantation,search,BuildToolServiceImplemantation.java,68,method_call,,"buildToolRepository.findByNameAndBuildTypeIgnoreCase(projectName, toolName)",serviceBolt
search.result,buildToolServiceImplemantation,search,BuildToolServiceImplemantation.java,70,method_call,result,buildToolRepository.findByName(projectName),serviceBolt
searchJobList.lastUpdate,buildToolServiceImplemantation,searchJobList,BuildToolServiceImplemantation.java,83,assignment,,1,serviceBolt
searchJobList.result,buildToolServiceImplemantation,searchJobList,BuildToolServiceImplemantation.java,84,method_call,,"buildToolRepository.findByBuildTypeAndName(buildType, projectName)",serviceBolt
searchForTest.result,buildToolServiceImplemantation,searchForTest,BuildToolServiceImplemantation.java,96,method_call,,"buildToolRepository.findByBuildTypeAndName(buildType, projectName)",serviceBolt
search.flagnew,buildToolServiceImplemantation,search,BuildToolServiceImplemantation.java,104,assignment,,flag,serviceBolt
search.toolName,buildToolServiceImplemantation,search,BuildToolServiceImplemantation.java,105,assignment,,null,serviceBolt
search.config,buildToolServiceImplemantation,search,BuildToolServiceImplemantation.java,106,method_call,,configSettingRepo.findByProjectName(projectName),serviceBolt
search.metric,buildToolServiceImplemantation,search,BuildToolServiceImplemantation.java,107,object_creation,metric,new ConfigurationToolInfoMetric(),serviceBolt
search.metric,buildToolServiceImplemantation,search,BuildToolServiceImplemantation.java,110,assignment,metric,m,serviceBolt
search.toolName,buildToolServiceImplemantation,search,BuildToolServiceImplemantation.java,112,assignment,toolName,"""GITLAB""",serviceBolt
search.toolName,buildToolServiceImplemantation,search,BuildToolServiceImplemantation.java,114,assignment,toolName,"""BITBUCKET""",serviceBolt
search.toolName,buildToolServiceImplemantation,search,BuildToolServiceImplemantation.java,116,assignment,toolName,"""JENKINS""",serviceBolt
search.toolName,buildToolServiceImplemantation,search,BuildToolServiceImplemantation.java,119,method_call,toolName,m.getToolName(),serviceBolt
search.mongoAggr,buildToolServiceImplemantation,search,BuildToolServiceImplemantation.java,128,object_creation,,new MongoAggregate(),serviceBolt
search.lastUpdate,buildToolServiceImplemantation,search,BuildToolServiceImplemantation.java,129,method_call,,mongoAggr.getBuildsCount(projectName),serviceBolt
search.result,buildToolServiceImplemantation,search,BuildToolServiceImplemantation.java,132,method_call,,"buildToolRepository.findByNameAndTimestampBetween(projectName, sDate, eDate)",serviceBolt
buildFailurePattern.patternForproject,buildToolServiceImplemantation,buildFailurePattern,BuildToolServiceImplemantation.java,152,object_creation,,new BuildFailurePatternForProjectInJenkinsModel(),serviceBolt
buildFailurePattern.buildFailurePatternForProjectList,buildToolServiceImplemantation,buildFailurePattern,BuildToolServiceImplemantation.java,159,data_access,,"buildFailurePatternForProjectRepo
				.findByProjectName(failureRequestList.get(0).getProjectName())",serviceBolt
buildFailurePattern.buildFailurePatternForProject,buildToolServiceImplemantation,buildFailurePattern,BuildToolServiceImplemantation.java,167,arithmetic,,"buildFailurePatternForProjectList
					.get(buildFailurePatternForProjectList.size() - 1)",serviceBolt
fetchBuildData.lastUpdated,buildToolServiceImplemantation,fetchBuildData,BuildToolServiceImplemantation.java,180,assignment,,1,serviceBolt
fetchBuildData.result,buildToolServiceImplemantation,fetchBuildData,BuildToolServiceImplemantation.java,181,method_call,,buildFailurePatternForProjectRepo.findAll(),serviceBolt
fetchFailurePatternData.lastUpdated,buildToolServiceImplemantation,fetchFailurePatternData,BuildToolServiceImplemantation.java,192,assignment,,1,serviceBolt
fetchFailurePatternData.response,buildToolServiceImplemantation,fetchFailurePatternData,BuildToolServiceImplemantation.java,193,method_call,,"buildFailurePatternForProjectRepo
				.findByProjectName(projectName)",serviceBolt
getOneByProjectName.result,buildToolServiceImplemantation,getOneByProjectName,BuildToolServiceImplemantation.java,207,method_call,,buildToolRepository.findOneByNameOrderByBuildIDDesc(projName),serviceBolt
getBuildDetailsHome.projHomeCalc,buildToolServiceImplemantation,getBuildDetailsHome,BuildToolServiceImplemantation.java,215,object_creation,,new ProjectHomeCalculation(),serviceBolt
getBuildDetailsHome.result,buildToolServiceImplemantation,getBuildDetailsHome,BuildToolServiceImplemantation.java,216,method_call,,"projHomeCalc.getBuildDetailsHome(projName, almType)",serviceBolt
getValueStream.config,buildToolServiceImplemantation,getValueStream,BuildToolServiceImplemantation.java,224,method_call,,configSettingRepo.findByProjectName(proName),serviceBolt
getValueStream.metric,buildToolServiceImplemantation,getValueStream,BuildToolServiceImplemantation.java,225,object_creation,metric,new ConfigurationToolInfoMetric(),serviceBolt
getValueStream.metric,buildToolServiceImplemantation,getValueStream,BuildToolServiceImplemantation.java,228,assignment,metric,m,serviceBolt
getValueStream.buildCalculations,buildToolServiceImplemantation,getValueStream,BuildToolServiceImplemantation.java,232,object_creation,,new BuildCalculations(),serviceBolt
getGitlabValueStream.config,buildToolServiceImplemantation,getGitlabValueStream,BuildToolServiceImplemantation.java,244,method_call,,configSettingRepo.findByProjectName(proName),serviceBolt
getGitlabValueStream.metric,buildToolServiceImplemantation,getGitlabValueStream,BuildToolServiceImplemantation.java,245,object_creation,metric,new ConfigurationToolInfoMetric(),serviceBolt
getGitlabValueStream.metric,buildToolServiceImplemantation,getGitlabValueStream,BuildToolServiceImplemantation.java,248,assignment,metric,m,serviceBolt
getGitlabValueStream.buildCalculations,buildToolServiceImplemantation,getGitlabValueStream,BuildToolServiceImplemantation.java,252,object_creation,,new BuildCalculations(),serviceBolt
setId.thisId,baseModel,setId,BaseModel.java,16,assignment,this.id,id,unifiedBolt
setProjectName.thisProjectName,buildFailurePatternForProjectInJenkinsModel,setProjectName,BuildFailurePatternForProjectInJenkinsModel.java,42,assignment,this.projectName,projectName,unifiedBolt
setPatternMetrics.thisPatternMetrics,buildFailurePatternForProjectInJenkinsModel,setPatternMetrics,BuildFailurePatternForProjectInJenkinsModel.java,61,assignment,this.patternMetrics,patternMetrics,unifiedBolt
setTimestampOfCreation.thisTimestampOfCreation,buildFailurePatternForProjectInJenkinsModel,setTimestampOfCreation,BuildFailurePatternForProjectInJenkinsModel.java,76,assignment,this.timestampOfCreation,timestampOfCreation,unifiedBolt
addPatternMetric.buildFailurePatternMetrics,buildFailurePatternForProjectInJenkinsModel,addPatternMetric,BuildFailurePatternForProjectInJenkinsModel.java,86,object_creation,,new BuildFailurePatternMetrics(),unifiedBolt
addPatternMetric.buildFailurePatternMetrics,buildFailurePatternForProjectInJenkinsModel,addPatternMetric,BuildFailurePatternForProjectInJenkinsModel.java,94,object_creation,,new BuildFailurePatternMetrics(),unifiedBolt
setUserName.thisUserName,buildFailurePatternForProjectInJenkinsModel,setUserName,BuildFailurePatternForProjectInJenkinsModel.java,105,assignment,this.userName,userName,unifiedBolt
setPatternDefined.thisPatternDefined,buildFailurePatternMetrics,setPatternDefined,BuildFailurePatternMetrics.java,36,assignment,this.patternDefined,patternDefined,unifiedBolt
setPatternDisplayed.thisPatternDisplayed,buildFailurePatternMetrics,setPatternDisplayed,BuildFailurePatternMetrics.java,51,assignment,this.patternDisplayed,patternDisplayed,unifiedBolt
setPatternCount.thisPatternCount,buildFailurePatternMetrics,setPatternCount,BuildFailurePatternMetrics.java,66,assignment,this.patternCount,patternCount,unifiedBolt
setRepoName.thisRepoName,buildFailurePatternMetrics,setRepoName,BuildFailurePatternMetrics.java,74,assignment,this.repoName,repoName,unifiedBolt
setFileNames.thisFileNames,buildFileInfo,setFileNames,BuildFileInfo.java,12,assignment,this.fileNames,fileNames,unifiedBolt
setEditType.thisEditType,buildFileInfo,setEditType,BuildFileInfo.java,20,assignment,this.editType,editType,unifiedBolt
setMessage.thisMessage,buildInfo,setMessage,BuildInfo.java,15,assignment,this.message,message,unifiedBolt
setCommitter.thisCommitter,buildInfo,setCommitter,BuildInfo.java,23,assignment,this.committer,committer,unifiedBolt
setBuildFileInfoList.thisBuildFileInfoList,buildInfo,setBuildFileInfoList,BuildInfo.java,31,assignment,this.buildFileInfoList,buildFileInfoList,unifiedBolt
setStepName.thisStepName,buildSteps,setStepName,BuildSteps.java,15,assignment,this.stepName,stepName,unifiedBolt
setDuration.thisDuration,buildSteps,setDuration,BuildSteps.java,23,assignment,this.duration,duration,unifiedBolt
setResult.thisResult,buildSteps,setResult,BuildSteps.java,31,assignment,this.result,result,unifiedBolt
setStartedTime.thisStartedTime,buildSteps,setStartedTime,BuildSteps.java,39,assignment,this.startedTime,startedTime,unifiedBolt
setCompletedTime.thisCompletedTime,buildSteps,setCompletedTime,BuildSteps.java,47,assignment,this.completedTime,completedTime,unifiedBolt
setTimeString.thisTimeString,buildTool,setTimeString,BuildTool.java,48,assignment,this.timeString,timeString,unifiedBolt
setCollectorItemId.thisCollectorItemId,buildTool,setCollectorItemId,BuildTool.java,56,assignment,this.collectorItemId,collectorItemId,unifiedBolt
setStepsList.thisStepsList,buildTool,setStepsList,BuildTool.java,64,assignment,this.stepsList,stepsList,unifiedBolt
setCreatedBy.thisCreatedBy,buildTool,setCreatedBy,BuildTool.java,72,assignment,this.createdBy,createdBy,unifiedBolt
setBranchName.thisBranchName,buildTool,setBranchName,BuildTool.java,80,assignment,this.branchName,branchName,unifiedBolt
setMetrics.thisMetrics,buildTool,setMetrics,BuildTool.java,84,assignment,this.metrics,metrics,unifiedBolt
setMetrics.patternDetails,buildTool,setMetrics,BuildTool.java,86,unknown,,null,unifiedBolt
setBuildId.thisBuildId,buildTool,setBuildId,BuildTool.java,93,assignment,this.buildID,buildID,unifiedBolt
setJenkinsItemId.thisCollectorItemId,buildTool,setJenkinsItemId,BuildTool.java,109,assignment,this.collectorItemId,jenkinsItemId,unifiedBolt
setTimestamp.thisTimestamp,buildTool,setTimestamp,BuildTool.java,127,assignment,this.timestamp,timestamp,unifiedBolt
setName.thisName,buildTool,setName,BuildTool.java,143,assignment,this.name,name,unifiedBolt
setUrl.thisUrl,buildTool,setUrl,BuildTool.java,161,api_formatting,this.url,url,unifiedBolt
setVersion.thisVersion,buildTool,setVersion,BuildTool.java,177,assignment,this.version,version,unifiedBolt
setBuildType.thisBuildType,buildTool,setBuildType,BuildTool.java,192,assignment,this.buildType,buildType,unifiedBolt
setBuildInfoList.thisBuildInfoList,buildTool,setBuildInfoList,BuildTool.java,204,assignment,this.buildInfoList,buildInfoList,unifiedBolt
setJobName.thisJobName,buildTool,setJobName,BuildTool.java,212,assignment,this.jobName,jobName,unifiedBolt
setJobList.thisJobList,buildTool,setJobList,BuildTool.java,220,assignment,this.jobList,jobList,unifiedBolt
setJobCount.thisJobCount,buildTool,setJobCount,BuildTool.java,228,assignment,this.jobCount,jobCount,unifiedBolt
setPatternDetails.thisPatternDetails,buildTool,setPatternDetails,BuildTool.java,243,assignment,this.patternDetails,patternDetails,unifiedBolt
setTriggerType.thisTriggerType,buildTool,setTriggerType,BuildTool.java,255,assignment,this.triggerType,triggerType,unifiedBolt
setRepoName.thisRepoName,buildTool,setRepoName,BuildTool.java,263,assignment,this.repoName,repoName,unifiedBolt
setGroupName.thisGroupName,buildTool,setGroupName,BuildTool.java,271,assignment,this.groupName,groupName,unifiedBolt
setDefinitionId.thisDefinitionId,buildTool,setDefinitionId,BuildTool.java,279,assignment,this.definitionId,definitionId,unifiedBolt
setName.thisName,buildToolMetric,setName,BuildToolMetric.java,31,assignment,this.name,name,unifiedBolt
setValue.thisValueBuildTool,buildToolMetric,setValue,BuildToolMetric.java,46,assignment,this.valueBuildTool,value,unifiedBolt
setFormattedValue.thisFormattedValueBuildTool,buildToolMetric,setFormattedValue,BuildToolMetric.java,61,assignment,this.formattedValueBuildTool,formattedValue,unifiedBolt
setStatusMessage.thisStatusMessageBuildTool,buildToolMetric,setStatusMessage,BuildToolMetric.java,76,assignment,this.statusMessageBuildTool,statusMessage,unifiedBolt
setManualData.thisManualData,configurationSetting,setManualData,ConfigurationSetting.java,25,assignment,this.manualData,manualData,unifiedBolt
setManualData.thisManualData,configurationSetting,setManualData,ConfigurationSetting.java,30,assignment,this.manualData,false,unifiedBolt
setTimestamp.thisTimestamp,configurationSetting,setTimestamp,ConfigurationSetting.java,41,assignment,this.timestamp,timestamp,unifiedBolt
setProjectName.thisProjectName,configurationSetting,setProjectName,ConfigurationSetting.java,49,assignment,this.projectName,projectName,unifiedBolt
setAddFlag.thisAddFlag,configurationSetting,setAddFlag,ConfigurationSetting.java,61,assignment,this.addFlag,addFlag,unifiedBolt
setBaseline.thisBaseline,configurationSetting,setBaseline,ConfigurationSetting.java,69,assignment,this.baseline,baseline,unifiedBolt
setProjectType.thisProjectType,configurationSetting,setProjectType,ConfigurationSetting.java,77,assignment,this.projectType,projectType,unifiedBolt
setId.thisId,configurationToolInfoMetric,setId,ConfigurationToolInfoMetric.java,36,assignment,this.id,id,unifiedBolt
setToolName.thisToolName,configurationToolInfoMetric,setToolName,ConfigurationToolInfoMetric.java,51,assignment,this.toolName,toolName,unifiedBolt
setUrl.thisUrl,configurationToolInfoMetric,setUrl,ConfigurationToolInfoMetric.java,66,api_formatting,this.url,url,unifiedBolt
setUserName.thisUserName,configurationToolInfoMetric,setUserName,ConfigurationToolInfoMetric.java,81,assignment,this.userName,userName,unifiedBolt
setPassword.thisPassword,configurationToolInfoMetric,setPassword,ConfigurationToolInfoMetric.java,96,assignment,this.password,password,unifiedBolt
setToolType.thisToolType,configurationToolInfoMetric,setToolType,ConfigurationToolInfoMetric.java,104,assignment,this.toolType,toolType,unifiedBolt
setWidgetName.thisWidgetName,configurationToolInfoMetric,setWidgetName,ConfigurationToolInfoMetric.java,112,assignment,this.widgetName,widgetName,unifiedBolt
setJobName.thisJobName,configurationToolInfoMetric,setJobName,ConfigurationToolInfoMetric.java,120,assignment,this.jobName,jobName,unifiedBolt
setProjectCode.thisProjectCode,configurationToolInfoMetric,setProjectCode,ConfigurationToolInfoMetric.java,128,assignment,this.projectCode,projectCode,unifiedBolt
setSelected.thisSelected,configurationToolInfoMetric,setSelected,ConfigurationToolInfoMetric.java,136,assignment,this.selected,selected,unifiedBolt
setDomain.thisDomain,configurationToolInfoMetric,setDomain,ConfigurationToolInfoMetric.java,144,assignment,this.domain,domain,unifiedBolt
setHost.thisHost,configurationToolInfoMetric,setHost,ConfigurationToolInfoMetric.java,152,assignment,this.host,host,unifiedBolt
setPort.thisPort,configurationToolInfoMetric,setPort,ConfigurationToolInfoMetric.java,160,assignment,this.port,port,unifiedBolt
setDbType.thisDbType,configurationToolInfoMetric,setDbType,ConfigurationToolInfoMetric.java,168,assignment,this.dbType,dbType,unifiedBolt
setSchema.thisSchema,configurationToolInfoMetric,setSchema,ConfigurationToolInfoMetric.java,176,assignment,this.schema,schema,unifiedBolt
setRepoName.thisRepoName,configurationToolInfoMetric,setRepoName,ConfigurationToolInfoMetric.java,184,assignment,this.repoName,repoName,unifiedBolt
setSecret.thisSecret,configurationToolInfoMetric,setSecret,ConfigurationToolInfoMetric.java,192,assignment,this.secret,secret,unifiedBolt
githubActionMain.applicationContext,githubActionApplication,githubActionMain,GithubActionApplication.java,50,method_call,applicationContext,DataConfig.getContext(),unifiedBolt
githubActionMain.repo,githubActionApplication,githubActionMain,GithubActionApplication.java,51,method_call,repo,applicationContext.getBean(BuildToolRep.class),unifiedBolt
githubActionMain.instanceUrl,githubActionApplication,githubActionMain,GithubActionApplication.java,52,assignment,,"""""",unifiedBolt
githubActionMain.branch,githubActionApplication,githubActionMain,GithubActionApplication.java,53,assignment,,"""""",unifiedBolt
githubActionMain.username,githubActionApplication,githubActionMain,GithubActionApplication.java,54,assignment,,"""""",unifiedBolt
githubActionMain.password,githubActionApplication,githubActionMain,GithubActionApplication.java,55,assignment,,null,unifiedBolt
githubActionMain.repoName,githubActionApplication,githubActionMain,GithubActionApplication.java,56,assignment,,null,unifiedBolt
githubActionMain.firstRun,githubActionApplication,githubActionMain,GithubActionApplication.java,57,assignment,,false,unifiedBolt
githubActionMain.apiToken,githubActionApplication,githubActionMain,GithubActionApplication.java,58,assignment,,null,unifiedBolt
githubActionMain.configurationRepo,githubActionApplication,githubActionMain,GithubActionApplication.java,59,method_call,configurationRepo,applicationContext.getBean(ConfigurationSettingRep.class),unifiedBolt
githubActionMain.configuration,githubActionApplication,githubActionMain,GithubActionApplication.java,60,data_access,configuration,configurationRepo.findByProjectName(projectName).get(0),unifiedBolt
githubActionMain.metric,githubActionApplication,githubActionMain,GithubActionApplication.java,61,method_call,metric,configuration.getMetrics(),unifiedBolt
githubActionMain.iter,githubActionApplication,githubActionMain,GithubActionApplication.java,63,method_call,,metric.iterator(),unifiedBolt
githubActionMain.githubActionMetrics,githubActionApplication,githubActionMain,GithubActionApplication.java,67,object_creation,githubActionMetrics,new GithubActionImplementation(),unifiedBolt
githubActionMain.splitUrl,githubActionApplication,githubActionMain,GithubActionApplication.java,68,assignment,,null,unifiedBolt
githubActionMain.splitRepoName,githubActionApplication,githubActionMain,GithubActionApplication.java,69,assignment,,null,unifiedBolt
githubActionMain.configuration1,githubActionApplication,githubActionMain,GithubActionApplication.java,73,method_call,,iter.next(),unifiedBolt
githubActionMain.metric1,githubActionApplication,githubActionMain,GithubActionApplication.java,74,method_call,metric1,(ConfigurationToolInfoMetric) configuration1,unifiedBolt
githubActionMain.instanceUrl,githubActionApplication,githubActionMain,GithubActionApplication.java,78,api_formatting,instanceURL,metric1.getUrl(),unifiedBolt
githubActionMain.username,githubActionApplication,githubActionMain,GithubActionApplication.java,79,method_call,username,metric1.getUserName(),unifiedBolt
githubActionMain.password,githubActionApplication,githubActionMain,GithubActionApplication.java,80,method_call,password,"EncryptionDecryptionAES.decrypt(metric1.getPassword(), ConstantVariable.SECRET_KEY)",unifiedBolt
githubActionMain.repoName,githubActionApplication,githubActionMain,GithubActionApplication.java,81,method_call,repoName,metric1.getRepoName(),unifiedBolt
githubActionMain.repoName,githubActionApplication,githubActionMain,GithubActionApplication.java,88,assignment,repoName,projectName,unifiedBolt
githubActionMain.splitUrl,githubActionApplication,githubActionMain,GithubActionApplication.java,90,string_manipulation,splitUrl,"instanceURL.split(Pattern.quote("",""))",unifiedBolt
githubActionMain.splitRepoName,githubActionApplication,githubActionMain,GithubActionApplication.java,91,string_manipulation,splitRepoName,"repoName.split("","")",unifiedBolt
githubActionMain.splitUrl,githubActionApplication,githubActionMain,GithubActionApplication.java,93,object_creation,splitUrl,new String[1],unifiedBolt
githubActionMain.splitUrl0,githubActionApplication,githubActionMain,GithubActionApplication.java,94,api_formatting,splitUrl[0],instanceURL,unifiedBolt
githubActionMain.splitRepoName,githubActionApplication,githubActionMain,GithubActionApplication.java,95,object_creation,splitRepoName,new String[1],unifiedBolt
githubActionMain.splitRepoName0,githubActionApplication,githubActionMain,GithubActionApplication.java,96,assignment,splitRepoName[0],repoName,unifiedBolt
githubActionMain.result,githubActionApplication,githubActionMain,GithubActionApplication.java,107,assignment,result,"""FAIL""",unifiedBolt
cleanObject.repo,githubActionApplication,cleanObject,GithubActionApplication.java,120,assignment,repo,null,unifiedBolt
cleanObject.githubActionMetrics,githubActionApplication,cleanObject,GithubActionApplication.java,121,assignment,githubActionMetrics,null,unifiedBolt
makeRestCall.requestFactory,githubActionApplication,makeRestCall,GithubActionApplication.java,129,object_creation,,new HttpComponentsClientHttpRequestFactory(),unifiedBolt
makeRestCall.rest,githubActionApplication,makeRestCall,GithubActionApplication.java,132,object_creation,,new RestTemplate(requestFactory),unifiedBolt
makeRestCall.response,githubActionApplication,makeRestCall,GithubActionApplication.java,135,api_formatting,,"rest.exchange(url, HttpMethod.GET, null, String.class)",unifiedBolt
getBuildToolData.githubUrl,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,74,api_formatting,,baseUrl,unifiedBolt
getBuildToolData.githubUrl,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,78,string_manipulation,githubUrl,"githubUrl.substring(0, githubUrl.lastIndexOf("".git""))",unifiedBolt
getBuildToolData.url,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,80,assignment,,null,unifiedBolt
getBuildToolData.hostName,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,81,assignment,,"""""",unifiedBolt
getBuildToolData.protocol,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,82,assignment,,"""""",unifiedBolt
getBuildToolData.port,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,83,unknown,,null,unifiedBolt
getBuildToolData.url,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,85,object_creation,url,new URL(githubUrl),unifiedBolt
getBuildToolData.hostName,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,86,api_formatting,hostName,url.getHost(),unifiedBolt
getBuildToolData.protocol,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,87,api_formatting,protocol,url.getProtocol(),unifiedBolt
getBuildToolData.port,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,88,api_formatting,port,url.getPort(),unifiedBolt
getBuildToolData.hostUrl,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,93,string_concatenation,,"protocol + ""://"" + hostName + ""/""",unifiedBolt
getBuildToolData.repoName,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,96,string_manipulation,repoName,"temp.split(""/"")[1]",unifiedBolt
getBuildToolData.githubUrl,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,101,string_manipulation,githubUrl,"protocol + ""://"" + hostName + ""/api/v3"" + ""/repos/""
					+ githubUrl.substring(hostUrl.length(), githubUrl.length())",unifiedBolt
getBuildToolData.githubUrl,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,104,string_manipulation,githubUrl,"protocol + ""://"" + PUBLIC_GITHUB_REPO_HOST + ""/repos/""
					+ githubUrl.substring(hostUrl.length(), githubUrl.length())",unifiedBolt
getBuildToolData.thisProjectName,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,109,assignment,this.projectName,projectName,unifiedBolt
getBuildToolData.thisBuildRepo,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,110,assignment,this.buildRepo,repo,unifiedBolt
getBuildToolData.thisRepoName,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,111,assignment,this.repoName,repoName,unifiedBolt
getBuildToolData.page,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,112,assignment,page,0,unifiedBolt
getBuildToolData.tool,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,113,method_call,,"repo.findByBuildTypeAndRepoNameAndNameOrderByTimestampDesc(""GITHUB"", repoName,
				projectName)",unifiedBolt
getBuildToolData.delta,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,115,assignment,,"""""",unifiedBolt
getBuildToolData.timeString,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,117,assignment,,"""""",unifiedBolt
getBuildToolData.count,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,118,assignment,,0,unifiedBolt
getBuildToolData.timeString,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,120,data_access,timeString,tool.get(count).getTimeString(),unifiedBolt
getBuildToolData.delta,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,124,string_concatenation,delta,"""&created=>"" + timeString",unifiedBolt
getBuildToolData.response,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,128,string_concatenation,,"makeRestCall(githubUrl + ""/actions/runs?"" + delta, user, pass)",unifiedBolt
getBuildToolData.runs,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,131,object_creation,,new JSONObject(response.getBody()),unifiedBolt
getBuildToolData.totalRuns,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,132,method_call,,"runs.getInt(""total_count"")",unifiedBolt
getBuildToolData.page,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,133,assignment,,1,unifiedBolt
getBuildToolData.runsCollected,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,134,assignment,,0,unifiedBolt
getBuildToolData.pipelineUrl,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,139,string_concatenation,pipelineUrl,"githubUrl + ""/actions/runs?"" + ""page="" + page + ""&per_page="" + per_page + delta",unifiedBolt
getBuildToolData.response,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,141,api_formatting,response,"makeRestCall(pipelineUrl, user, pass)",unifiedBolt
getBuildToolData.runs,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,143,object_creation,runs,new JSONObject(response.getBody()),unifiedBolt
getBuildToolData.valueArray,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,144,method_call,,"runs.getJSONArray(""workflow_runs"")",unifiedBolt
getBuildToolData.obj,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,146,data_access,,(JSONObject) valueArray.get(0),unifiedBolt
getBuildToolData.builds,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,147,api_formatting,,"processPipelineData(valueArray, user, pass, projectName, repoName,
								githubUrl)",unifiedBolt
getBuildToolData.runsCollected,githubActionImplementation,getBuildToolData,GithubActionImplementation.java,150,method_call,runsCollected,valueArray.length(),unifiedBolt
processPipelineData.builds,githubActionImplementation,processPipelineData,GithubActionImplementation.java,165,object_creation,,new ArrayList<>(),unifiedBolt
processPipelineData.pipelineObj,githubActionImplementation,processPipelineData,GithubActionImplementation.java,167,method_call,,pipelineValues.getJSONObject(i),unifiedBolt
processPipelineData.timestamp,githubActionImplementation,processPipelineData,GithubActionImplementation.java,169,method_call,timestamp,"getTimeInMiliseconds(pipeline_Obj.getString(""created_at""))",unifiedBolt
processPipelineData.build,githubActionImplementation,processPipelineData,GithubActionImplementation.java,171,object_creation,build,new BuildTool(),unifiedBolt
processPipelineData.id,githubActionImplementation,processPipelineData,GithubActionImplementation.java,172,method_call,,"pipeline_Obj.getLong(""id"")",unifiedBolt
processPipelineData.thisPipelineId,githubActionImplementation,processPipelineData,GithubActionImplementation.java,176,string_concatenation,this.pipelineId,""""" + id",unifiedBolt
processPipelineData.jobsUrl,githubActionImplementation,processPipelineData,GithubActionImplementation.java,179,string_concatenation,jobsUrl,"githubUrl + ""/actions/runs/"" + this.pipelineId + ""/jobs""",unifiedBolt
processPipelineData.jobResponse,githubActionImplementation,processPipelineData,GithubActionImplementation.java,181,api_formatting,,"makeRestCall(jobsUrl, user, pass)",unifiedBolt
processPipelineData.createTime,githubActionImplementation,processPipelineData,GithubActionImplementation.java,200,assignment,,0,unifiedBolt
processPipelineData.createTime,githubActionImplementation,processPipelineData,GithubActionImplementation.java,202,method_call,createTime,getTimeInMiliseconds(temp_date),unifiedBolt
processPipelineData.updatedTime,githubActionImplementation,processPipelineData,GithubActionImplementation.java,208,assignment,,0,unifiedBolt
processPipelineData.updatedTime,githubActionImplementation,processPipelineData,GithubActionImplementation.java,210,method_call,updatedTime,getTimeInMiliseconds(temp_date1),unifiedBolt
processPipelineData.completeTime,githubActionImplementation,processPipelineData,GithubActionImplementation.java,217,assignment,,0,unifiedBolt
processPipelineData.completeTime,githubActionImplementation,processPipelineData,GithubActionImplementation.java,219,method_call,completeTime,getTimeInMiliseconds(temp_date),unifiedBolt
processPipelineData.durationMetric,githubActionImplementation,processPipelineData,GithubActionImplementation.java,223,object_creation,,"new BuildToolMetric(""duration"")",unifiedBolt
processPipelineData.duration,githubActionImplementation,processPipelineData,GithubActionImplementation.java,224,assignment,,0,unifiedBolt
processPipelineData.duration,githubActionImplementation,processPipelineData,GithubActionImplementation.java,226,arithmetic,duration,updatedTime - createTime,unifiedBolt
processPipelineData.resultMetric,githubActionImplementation,processPipelineData,GithubActionImplementation.java,228,object_creation,,"new BuildToolMetric(""result"")",unifiedBolt
processPipelineData.timestampMetric,githubActionImplementation,processPipelineData,GithubActionImplementation.java,230,object_creation,,"new BuildToolMetric(""timestamp"")",unifiedBolt
processPipelineData.jobsArray1,githubActionImplementation,processPipelineData,GithubActionImplementation.java,236,object_creation,,new JSONObject(jobResponse.getBody()),unifiedBolt
processPipelineData.jobsArray,githubActionImplementation,processPipelineData,GithubActionImplementation.java,237,method_call,,"jobsArray1.getJSONArray(""jobs"")",unifiedBolt
processPipelineData.singleJobsobj,githubActionImplementation,processPipelineData,GithubActionImplementation.java,239,method_call,,jobsArray.getJSONObject(0),unifiedBolt
processPipelineData.commitObj,githubActionImplementation,processPipelineData,GithubActionImplementation.java,240,method_call,,"pipeline_Obj.getJSONObject(""head_commit"")",unifiedBolt
processPipelineData.authorObj,githubActionImplementation,processPipelineData,GithubActionImplementation.java,241,method_call,,"commitObj.getJSONObject(""author"")",unifiedBolt
processPipelineData.jobsList,githubActionImplementation,processPipelineData,GithubActionImplementation.java,244,object_creation,jobsList,new ArrayList<>(),unifiedBolt
processPipelineData.stepsObj,githubActionImplementation,processPipelineData,GithubActionImplementation.java,248,method_call,,jobsArray.getJSONObject(j),unifiedBolt
processPipelineData.completeDate,githubActionImplementation,processPipelineData,GithubActionImplementation.java,249,assignment,,null,unifiedBolt
processPipelineData.completed,githubActionImplementation,processPipelineData,GithubActionImplementation.java,250,assignment,,0,unifiedBolt
processPipelineData.started,githubActionImplementation,processPipelineData,GithubActionImplementation.java,251,assignment,,0,unifiedBolt
processPipelineData.completeDate,githubActionImplementation,processPipelineData,GithubActionImplementation.java,253,method_call,completeDate,"stepsObj.getString(""completed_at"")",unifiedBolt
processPipelineData.completed,githubActionImplementation,processPipelineData,GithubActionImplementation.java,259,assignment,completed,tempDate,unifiedBolt
processPipelineData.completeDate,githubActionImplementation,processPipelineData,GithubActionImplementation.java,262,assignment,completeDate,null,unifiedBolt
processPipelineData.completeDate,githubActionImplementation,processPipelineData,GithubActionImplementation.java,264,method_call,completeDate,"stepsObj.getString(""started_at"")",unifiedBolt
processPipelineData.started,githubActionImplementation,processPipelineData,GithubActionImplementation.java,268,assignment,started,tempDate,unifiedBolt
processPipelineData.result,githubActionImplementation,processPipelineData,GithubActionImplementation.java,273,method_call,,"stepsObj.optString(""conclusion"")",unifiedBolt
processFailure.ctx,githubActionImplementation,processFailure,GithubActionImplementation.java,299,method_call,ctx,DataConfig.getContext(),unifiedBolt
processFailure.failurePatternRepo,githubActionImplementation,processFailure,GithubActionImplementation.java,300,method_call,failurePatternRepo,ctx.getBean(BuildFailurePatternForProjectRepo.class),unifiedBolt
processFailure.failurePattern,githubActionImplementation,processFailure,GithubActionImplementation.java,301,method_call,,"failurePatternRepo
				.findByProjectName(projectName)",unifiedBolt
processFailure.failureMetrics,githubActionImplementation,processFailure,GithubActionImplementation.java,306,method_call,,tempBuildFailure.getPatternMetrics(),unifiedBolt
processFailure.newList,githubActionImplementation,processFailure,GithubActionImplementation.java,307,object_creation,,new ArrayList<>(),unifiedBolt
processFailure.url,githubActionImplementation,processFailure,GithubActionImplementation.java,308,string_concatenation,,"githubUrl + ""/actions/jobs/"" + id + ""/logs""",unifiedBolt
processFailure.logResponseData,githubActionImplementation,processFailure,GithubActionImplementation.java,309,api_formatting,,"makeRestCall(url, user, pass)",unifiedBolt
processFailure.failureLog,githubActionImplementation,processFailure,GithubActionImplementation.java,311,method_call,,logResponseData.getBody(),unifiedBolt
processFailure.flag,githubActionImplementation,processFailure,GithubActionImplementation.java,318,assignment,,true,unifiedBolt
processFailure.flag,githubActionImplementation,processFailure,GithubActionImplementation.java,326,assignment,flag,false,unifiedBolt
processFailure.b,githubActionImplementation,processFailure,GithubActionImplementation.java,331,object_creation,,new BuildFailurePatternMetrics(),unifiedBolt
getTimeInMiliseconds.splitDate,githubActionImplementation,getTimeInMiliseconds,GithubActionImplementation.java,364,string_manipulation,,"temp_date.split(""T"")",unifiedBolt
getTimeInMiliseconds.fmt,githubActionImplementation,getTimeInMiliseconds,GithubActionImplementation.java,369,arithmetic,,"DateTimeFormat.forPattern(""yyyy-MM-dd HH:mm:ss"")",unifiedBolt
getTimeInMiliseconds.createdDate,githubActionImplementation,getTimeInMiliseconds,GithubActionImplementation.java,370,method_call,,fmt.parseDateTime(tempTime),unifiedBolt
createHeaders.auth,githubActionImplementation,createHeaders,GithubActionImplementation.java,392,string_concatenation,,"userId + "":"" + password",unifiedBolt
createHeaders.encodedAuth,githubActionImplementation,createHeaders,GithubActionImplementation.java,393,method_call,,Base64.encodeBase64(auth.getBytes(StandardCharsets.US_ASCII)),unifiedBolt
createHeaders.authHeader,githubActionImplementation,createHeaders,GithubActionImplementation.java,394,object_creation,,"""Basic "" + new String(encodedAuth)",unifiedBolt
createHeaders.headers,githubActionImplementation,createHeaders,GithubActionImplementation.java,396,object_creation,,new HttpHeaders(),unifiedBolt
get.requestFactory,githubActionImplementation,get,GithubActionImplementation.java,402,object_creation,,new HttpComponentsClientHttpRequestFactory(),unifiedBolt
