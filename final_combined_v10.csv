source_node,source_type,destination_node,destination_type,relationship,file_path,application
OneInsights,FOLDERS,ServiceBolt,FOLDERS,CONTAINS,,ServiceBolt
OneInsights,FOLDERS,UnifiedBolt,FOLDERS,CONTAINS,,UnifiedBolt
ServiceBolt,FOLDERS,api,FOLDERS,CONTAINS,,ServiceBolt
api,FOLDERS,BuildToolController.java,FILE,CONTAINS,ServiceBolt\api\BuildToolController.java,ServiceBolt
ServiceBolt,FOLDERS,service,FOLDERS,CONTAINS,,ServiceBolt
service,FOLDERS,BuildToolService.java,FILE,CONTAINS,ServiceBolt\service\BuildToolService.java,ServiceBolt
service,FOLDERS,BuildToolServiceImplemantation.java,FILE,CONTAINS,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
UnifiedBolt,FOLDERS,core,FOLDERS,CONTAINS,,UnifiedBolt
core,FOLDERS,model,FOLDERS,CONTAINS,,UnifiedBolt
model,FOLDERS,BaseModel.java,FILE,CONTAINS,UnifiedBolt\core\model\BaseModel.java,UnifiedBolt
model,FOLDERS,BuildFailurePatternForProjectInJenkinsModel.java,FILE,CONTAINS,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
model,FOLDERS,BuildFailurePatternMetrics.java,FILE,CONTAINS,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
model,FOLDERS,BuildFileInfo.java,FILE,CONTAINS,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
model,FOLDERS,BuildInfo.java,FILE,CONTAINS,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
model,FOLDERS,BuildSteps.java,FILE,CONTAINS,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
model,FOLDERS,BuildTool.java,FILE,CONTAINS,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
model,FOLDERS,BuildToolMetric.java,FILE,CONTAINS,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
model,FOLDERS,ConfigurationSetting.java,FILE,CONTAINS,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
model,FOLDERS,ConfigurationToolInfoMetric.java,FILE,CONTAINS,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
core,FOLDERS,repository,FOLDERS,CONTAINS,,UnifiedBolt
repository,FOLDERS,BuildFailurePatternForProjectRepo.java,FILE,CONTAINS,UnifiedBolt\core\repository\BuildFailurePatternForProjectRepo.java,UnifiedBolt
repository,FOLDERS,BuildToolRep.java,FILE,CONTAINS,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
repository,FOLDERS,ConfigurationSettingRep.java,FILE,CONTAINS,UnifiedBolt\core\repository\ConfigurationSettingRep.java,UnifiedBolt
UnifiedBolt,FOLDERS,githubaction,FOLDERS,CONTAINS,,UnifiedBolt
githubaction,FOLDERS,GithubAction.java,FILE,CONTAINS,UnifiedBolt\githubaction\GithubAction.java,UnifiedBolt
githubaction,FOLDERS,GithubActionApplication.java,FILE,CONTAINS,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
githubaction,FOLDERS,GithubActionImplementation.java,FILE,CONTAINS,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Buildtoolcontroller.Java,FILE,Buildtoolcontroller,CLASS,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Buildtoolcontroller.Buildtoolservice,VARIABLE,HAS_FIELD,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Getonejob,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Getbuilddetailshome,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Builddata,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Joblist,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Joblist,METHOD,Joblist.Buildtype,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Savebuildfailurepattern,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Savebuildfailurepattern,METHOD,Savebuildfailurepattern.Obj,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Fetchdata,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Fetchfailurepatterndata,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Fetchfailurepatterndata,METHOD,Fetchfailurepatterndata.Obj,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Fetchfailurepatterndatacopy,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Fetchfailurepatterndatacopy,METHOD,Fetchfailurepatterndatacopy.Obj,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Gitlabbuilddata,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Getbuildtype,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Getbuildtype,METHOD,Getbuildtype.Buildtype,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
Getbuildtype,METHOD,Getbuildtype.Context,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
Getbuildtype,METHOD,Getbuildtype.Configurationrepo,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Buildjoblist,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Builddetailshome,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Builddetails,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Jobslist,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Jenkinsbuildfailure,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Jenkinsbuildfailuredata,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Jenkinsbuildfailurepatternfetchdata,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Jenkinsbuildfailurepatternfetchdataconfig,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Getbuildvaluestream,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Getgitlabbuildvaluestream,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Buildtoolservice,VARIABLE,HAS_FIELD,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolservice.Java,FILE,Buildtoolservice,INTERFACE,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolservice,INTERFACE,Searchfortest,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolservice,INTERFACE,Search,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolservice,INTERFACE,Searchjoblist,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolservice,INTERFACE,Buildfailurepattern,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolservice,INTERFACE,Fetchfailurepatterndata,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolservice,INTERFACE,Fetchbuilddata,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolservice,INTERFACE,Getonebyprojectname,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolservice,INTERFACE,Getbuilddetailshome,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolservice,INTERFACE,Getvaluestream,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolservice,INTERFACE,Getgitlabvaluestream,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolserviceimplemantation.Java,FILE,Buildtoolserviceimplemantation,CLASS,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Buildtoolrepository,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Buildfailurepatternforprojectrepo,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Log,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Configsettingrepo,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Metric,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Nodataconst,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Buildconst,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Search,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Search,METHOD,Search.Lastupdate,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Search,METHOD,Search.Result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Searchjoblist,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Searchjoblist,METHOD,Searchjoblist.Lastupdate,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Searchjoblist,METHOD,Searchjoblist.Result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Searchfortest,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Searchfortest,METHOD,Searchfortest.Result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildfailurepattern,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildfailurepattern,METHOD,Buildfailurepattern.Patternforproject,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildfailurepattern,METHOD,Buildfailurepattern.Buildfailurepatternforprojectlist,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Fetchbuilddata,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Fetchbuilddata,METHOD,Fetchbuilddata.Lastupdated,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Fetchbuilddata,METHOD,Fetchbuilddata.Result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Fetchfailurepatterndata,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Fetchfailurepatterndata,METHOD,Fetchfailurepatterndata.Lastupdated,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Fetchfailurepatterndata,METHOD,Fetchfailurepatterndata.Response,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Getonebyprojectname,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Getonebyprojectname,METHOD,Getonebyprojectname.Result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Getbuilddetailshome,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Getbuilddetailshome,METHOD,Getbuilddetailshome.Result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Getvaluestream,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Getvaluestream,METHOD,Getvaluestream.Proname,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Getvaluestream,METHOD,Getvaluestream.Config,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Getvaluestream,METHOD,Getvaluestream.Metric,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Getgitlabvaluestream,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Getgitlabvaluestream,METHOD,Getgitlabvaluestream.Proname,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Getgitlabvaluestream,METHOD,Getgitlabvaluestream.Config,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Getgitlabvaluestream,METHOD,Getgitlabvaluestream.Metric,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Basemodel.Java,FILE,Basemodel,CLASS,DECLARES,UnifiedBolt\core\model\BaseModel.java,UnifiedBolt
Basemodel,CLASS,Basemodel.Id,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BaseModel.java,UnifiedBolt
Basemodel,CLASS,Getid,METHOD,DECLARES,UnifiedBolt\core\model\BaseModel.java,UnifiedBolt
Basemodel,CLASS,Setid,METHOD,DECLARES,UnifiedBolt\core\model\BaseModel.java,UnifiedBolt
Setid,METHOD,Setid.Id,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BaseModel.java,UnifiedBolt
Setid,METHOD,Basemodel.Id,VARIABLE,USES,UnifiedBolt\core\model\BaseModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel.Java,FILE,Buildfailurepatternforprojectinjenkinsmodel,CLASS,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Buildfailurepatternforprojectinjenkinsmodel.Projectname,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Buildfailurepatternforprojectinjenkinsmodel.Username,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Buildfailurepatternforprojectinjenkinsmodel.Patternmetrics,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Buildfailurepatternforprojectinjenkinsmodel.Timestampofcreation,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Getprojectname,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Setprojectname,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Setprojectname,METHOD,Setprojectname.Projectname,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Getpatternmetrics,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Setpatternmetrics,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Setpatternmetrics,METHOD,Setpatternmetrics.Patternmetrics,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Gettimestampofcreation,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Settimestampofcreation,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Settimestampofcreation,METHOD,Settimestampofcreation.Timestampofcreation,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Addpatternmetric,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Addpatternmetric,METHOD,Addpatternmetric.Buildfailurepatternmetrics,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Getusername,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Setusername,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternmetrics.Java,FILE,Buildfailurepatternmetrics,CLASS,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Buildfailurepatternmetrics.Patterndefined,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Buildfailurepatternmetrics.Patterndisplayed,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Buildfailurepatternmetrics.Patterncount,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Buildfailurepatternmetrics.Reponame,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Getpatterndefined,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Setpatterndefined,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Setpatterndefined,METHOD,Setpatterndefined.Patterndefined,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Getpatterndisplayed,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Setpatterndisplayed,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Setpatterndisplayed,METHOD,Setpatterndisplayed.Patterndisplayed,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Getpatterncount,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Setpatterncount,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Setpatterncount,METHOD,Setpatterncount.Patterncount,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Getreponame,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Setreponame,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Setreponame,METHOD,Setreponame.Reponame,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfileinfo.Java,FILE,Buildfileinfo,CLASS,DECLARES,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
Buildfileinfo,CLASS,Filenames,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
Buildfileinfo,CLASS,Edittype,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
Buildfileinfo,CLASS,Getfilenames,METHOD,DECLARES,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
Buildfileinfo,CLASS,Setfilenames,METHOD,DECLARES,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
Buildfileinfo,CLASS,Getedittype,METHOD,DECLARES,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
Buildfileinfo,CLASS,Setedittype,METHOD,DECLARES,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
Setfilenames,METHOD,Setfilenames.Filenames,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
Setedittype,METHOD,Setedittype.Edittype,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
Buildinfo.Java,FILE,Buildinfo,CLASS,DECLARES,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Buildinfo,CLASS,Buildinfo.Message,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Buildinfo,CLASS,Buildinfo.Committer,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Buildinfo,CLASS,Buildinfo.Buildfileinfolist,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Buildinfo,CLASS,Getmessage,METHOD,DECLARES,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Buildinfo,CLASS,Setmessage,METHOD,DECLARES,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Buildinfo,CLASS,Getcommitter,METHOD,DECLARES,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Buildinfo,CLASS,Setcommitter,METHOD,DECLARES,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Buildinfo,CLASS,Getbuildfileinfolist,METHOD,DECLARES,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Buildinfo,CLASS,Setbuildfileinfolist,METHOD,DECLARES,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Setmessage,METHOD,Setmessage.Message,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Setcommitter,METHOD,Setcommitter.Committer,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Setbuildfileinfolist,METHOD,Setbuildfileinfolist.Buildfileinfolist,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Buildsteps.Java,FILE,Buildsteps,CLASS,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Buildsteps.Stepname,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Buildsteps.Duration,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Buildsteps.Result,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Buildsteps.Startedtime,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Buildsteps.Completedtime,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Getstepname,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Setstepname,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Setstepname,METHOD,Setstepname.Stepname,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Getduration,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Setduration,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Setduration,METHOD,Setduration.Duration,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Getresult,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Setresult,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Setresult,METHOD,Setresult.Result,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Getstartedtime,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Setstartedtime,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Setstartedtime,METHOD,Setstartedtime.Startedtime,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Getcompletedtime,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Setcompletedtime,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildtool.Java,FILE,Buildtool,CLASS,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Collectoritemid,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Timestamp,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Timestring,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Name,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Jobname,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Url,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Version,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Buildtype,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Buildid,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Buildinfolist,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Metrics,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Stepslist,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Joblist,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Jobcount,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Createdby,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Branchname,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Reponame,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Groupname,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Triggertype,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Gettimestring,METHOD,Buildtool.Timestring,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getcollectoritemid,METHOD,Buildtool.Collectoritemid,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getstepslist,METHOD,Buildtool.Stepslist,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getcreatedby,METHOD,Buildtool.Createdby,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getbranchname,METHOD,Buildtool.Branchname,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getbuildid,METHOD,Buildtool.Buildid,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getjenkinsitemid,METHOD,Buildtool.Collectoritemid,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Gettimestamp,METHOD,Buildtool.Timestamp,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getname,METHOD,Buildtool.Name,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Geturl,METHOD,Buildtool.Url,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getversion,METHOD,Buildtool.Version,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getmetrics,METHOD,Buildtool.Metrics,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getbuildtype,METHOD,Buildtool.Buildtype,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getbuildinfolist,METHOD,Buildtool.Buildinfolist,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getjobname,METHOD,Buildtool.Jobname,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getjoblist,METHOD,Buildtool.Joblist,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getjobcount,METHOD,Buildtool.Jobcount,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Patterndetails,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Definitionid,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Compareto,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Getjobcount,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Setjobcount,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Gettriggertype,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Settriggertype,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Getreponame,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Setreponame,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Getgroupname,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Setgroupname,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Getdefinitionid,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Setdefinitionid,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Getpatterndetails,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Setpatterndetails,METHOD,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Compareto,METHOD,Compareto.Object,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Setjobcount,METHOD,Buildtool.Jobcount,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Gettriggertype,METHOD,Buildtool.Triggertype,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Settriggertype,METHOD,Buildtool.Triggertype,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getreponame,METHOD,Buildtool.Reponame,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Setreponame,METHOD,Buildtool.Reponame,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getgroupname,METHOD,Buildtool.Groupname,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Setgroupname,METHOD,Buildtool.Groupname,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getdefinitionid,METHOD,Buildtool.Definitionid,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Setdefinitionid,METHOD,Buildtool.Definitionid,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Getpatterndetails,METHOD,Buildtool.Patterndetails,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Setpatterndetails,METHOD,Buildtool.Patterndetails,VARIABLE,USES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtoolmetric.Java,FILE,Buildtoolmetric,CLASS,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Buildtoolmetric.Name,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Buildtoolmetric.Valuebuildtool,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Buildtoolmetric.Formattedvaluebuildtool,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Buildtoolmetric.Statusmessagebuildtool,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Getname,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Setname,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Setname,METHOD,Setname.Name,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Getvalue,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Setvalue,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Setvalue,METHOD,Setvalue.Value,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Getformattedvalue,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Setformattedvalue,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Setformattedvalue,METHOD,Setformattedvalue.Formattedvalue,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Getstatusmessage,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Setstatusmessage,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Setstatusmessage,METHOD,Setstatusmessage.Statusmessage,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Equals,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Hashcode,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Configurationsetting.Java,FILE,Configurationsetting,CLASS,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Ismanualdata,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Setmanualdata,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Setmanualdata,METHOD,Manualdata,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Gettimestamp,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Settimestamp,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Settimestamp,METHOD,Timestamp,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Getprojectname,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Setprojectname,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Setprojectname,METHOD,Projectname,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Getmetrics,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Isaddflag,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Setaddflag,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Isbaseline,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Setbaseline,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Getprojecttype,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Setprojecttype,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Manualdata,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Timestamp,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Projectname,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Metric,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Addflag,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Baseline,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Projecttype,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationtoolinfometric.Java,FILE,Configurationtoolinfometric,CLASS,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Configurationtoolinfometric.Selected,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Configurationtoolinfometric.Id,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Configurationtoolinfometric.Toolname,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Configurationtoolinfometric.Url,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Configurationtoolinfometric.Username,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Configurationtoolinfometric.Password,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Configurationtoolinfometric.Tooltype,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Configurationtoolinfometric.Widgetname,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Configurationtoolinfometric.Jobname,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Configurationtoolinfometric.Projectcode,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Configurationtoolinfometric.Domain,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Configurationtoolinfometric.Host,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Configurationtoolinfometric.Port,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Configurationtoolinfometric.Dbtype,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Configurationtoolinfometric.Schema,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Configurationtoolinfometric.Reponame,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Configurationtoolinfometric.Secret,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Configurationtoolinfometric.Manualdata,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getid,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setid,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Gettoolname,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Settoolname,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Geturl,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Seturl,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getusername,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setusername,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getpassword,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setpassword,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Gettooltype,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Settooltype,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getwidgetname,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setwidgetname,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getjobname,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setjobname,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getprojectcode,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setprojectcode,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getselected,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setselected,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getdomain,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setdomain,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Gethost,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Sethost,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getport,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setport,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getdbtype,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setdbtype,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getschema,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setschema,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getreponame,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setreponame,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getsecret,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setsecret,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Buildfailurepatternforprojectrepo.Java,FILE,Buildfailurepatternforprojectrepo,INTERFACE,DECLARES,UnifiedBolt\core\repository\BuildFailurePatternForProjectRepo.java,UnifiedBolt
Buildfailurepatternforprojectrepo,INTERFACE,Findbyprojectname,METHOD,DECLARES,UnifiedBolt\core\repository\BuildFailurePatternForProjectRepo.java,UnifiedBolt
Buildfailurepatternforprojectrepo,INTERFACE,Findall,METHOD,DECLARES,UnifiedBolt\core\repository\BuildFailurePatternForProjectRepo.java,UnifiedBolt
Buildtoolrep.Java,FILE,Buildtoolrep,INTERFACE,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbybuildtype,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Countbybuildtype,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbybuildtypeandname,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbynameandjobname,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Getidbybuildtype,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbybuildtypeandnameandtimestampbetween,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbynameandtimestampbetween,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbyname,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findonebyname,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findonebynameorderbybuildiddesc,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbybuildtypeandreponameandgroupnameorderbybuildiddesc,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbynameandbuildtypeignorecase,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbynameandreponameandbranchname,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbynameanddefinitionid,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbybuildtypeandreponameandnameorderbybuildiddesc,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbybuildtypeandreponameandnameorderbytimestampdesc,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Configurationsettingrep.Java,FILE,Configurationsettingrep,INTERFACE,DECLARES,UnifiedBolt\core\repository\ConfigurationSettingRep.java,UnifiedBolt
Configurationsettingrep,INTERFACE,Findbyprojectname,METHOD,DECLARES,UnifiedBolt\core\repository\ConfigurationSettingRep.java,UnifiedBolt
Configurationsettingrep,INTERFACE,Findbyprojectnamein,METHOD,DECLARES,UnifiedBolt\core\repository\ConfigurationSettingRep.java,UnifiedBolt
Configurationsettingrep,INTERFACE,Deletebyprojectname,METHOD,DECLARES,UnifiedBolt\core\repository\ConfigurationSettingRep.java,UnifiedBolt
Githubaction.Java,FILE,Githubaction,INTERFACE,DECLARES,UnifiedBolt\githubaction\GithubAction.java,UnifiedBolt
Githubaction,INTERFACE,Getbuildtooldata,METHOD,DECLARES,UnifiedBolt\githubaction\GithubAction.java,UnifiedBolt
Githubactionapplication.Java,FILE,Githubactionapplication,CLASS,DECLARES,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Githubactionapplication.Logger,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Githubactionapplication.Applicationcontext,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Githubactionapplication.Repo,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Githubactionapplication.Githubactionmetrics,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Githubactionapplication.Result,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Githubactionapplication.Buildtype,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Githubactionapplication.Pagelimit,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Githubactionapplication.Configurationrepo,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Githubactionapplication.Configuration,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Githubactionapplication.Metric,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Githubactionapplication.Metric1,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Githubactionmain,METHOD,DECLARES,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,Githubactionmain.Applicationcontext,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,Githubactionmain.Repo,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,Githubactionmain.Instanceurl,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,Githubactionmain.Branch,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,Githubactionmain.Username,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,Githubactionmain.Password,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,Githubactionmain.Reponame,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionimplementation.Java,FILE,Githubactionimplementation,CLASS,DECLARES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Logger,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Segment_Api,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Public_Github_Repo_Host,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Public_Github_Host_Name,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Ctx,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Projectname,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Username,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Password,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Time,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Buildrepo,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Jobcollection,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Pipelineurl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Singlepipelineurl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Githuburl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Jobsurl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Size,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Lastpage,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Lastbuildid,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Newbuildpipelinetimestamp,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Getbuildtooldata,METHOD,DECLARES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Reponame,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Getbuildtooldata,METHOD,Page,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Processpipelinedata,METHOD,DECLARES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,Processpipelinedata.Pipelinevalues,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,Processpipelinedata.User,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,Processpipelinedata.Pass,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,Processpipelinedata.Projectname,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,Processpipelinedata.Reponame,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,Processpipelinedata.Githuburl,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,Processpipelinedata.Builds,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Makerestcall,METHOD,Processpipelinedata.Jobsurl,VARIABLE,USES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Makerestcall,METHOD,Processpipelinedata.User,VARIABLE,USES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Makerestcall,METHOD,Processpipelinedata.Pass,VARIABLE,USES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,Processpipelinedata.Lastbuildid,VARIABLE,USES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata.Githuburl,VARIABLE,Processpipelinedata.Timestamp,VARIABLE,TRANSFORMS_TO,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Logger,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Segment_Api,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Public_Github_Repo_Host,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Public_Github_Host_Name,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Ctx,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Projectname,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Username,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Password,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Time,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Buildrepo,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Jobcollection,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Pipelineurl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Singlepipelineurl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githuburl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Jobsurl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Size,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Lastpage,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Lastbuildid,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Newbuildpipelinetimestamp,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Temp_Date,VARIABLE,Splitdate,VARIABLE,TRANSFORMS_TO,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Splitdate,VARIABLE,Temp,VARIABLE,TRANSFORMS_TO,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Temp,VARIABLE,Temptime,VARIABLE,TRANSFORMS_TO,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Fmt,VARIABLE,Createddate,VARIABLE,TRANSFORMS_TO,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Createddate,VARIABLE,Responseentity,VARIABLE,TRANSFORMS_TO,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Auth,VARIABLE,Encodedauth,VARIABLE,TRANSFORMS_TO,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Encodedauth,VARIABLE,Authheader,VARIABLE,TRANSFORMS_TO,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
