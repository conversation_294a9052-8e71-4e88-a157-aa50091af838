package com.bolt.dashboard.core.model;

import java.util.List;

public class BuildInfo {
    private String message;
    private String committer;
    private List<BuildFileInfo> buildFileInfoList;

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getCommitter() {
        return committer;
    }

    public void setCommitter(String committer) {
        this.committer = committer;
    }

    public List<BuildFileInfo> getBuildFileInfoList() {
        return buildFileInfoList;
    }

    public void setBuildFileInfoList(List<BuildFileInfo> buildFileInfoList) {
        this.buildFileInfoList = buildFileInfoList;
    }

}
