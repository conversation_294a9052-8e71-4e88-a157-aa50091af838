source_node,source_type,destination_node,destination_type,relationship,file_path,application
Servicebolt,PROJECT,Servicebolt_Applications,APPLICATIONS,CONTAINS,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller.Java,FILE,Buildtoolcontroller,CLASS,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Buildtoolcontroller.Buildtoolservice,VARIABLE,HAS_FIELD,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Getonejob,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Getbuilddetailshome,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Builddata,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Joblist,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Joblist,METHOD,Joblist.Buildtype,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Savebuildfailurepattern,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Savebuildfailurepattern,METHOD,Savebuildfailurepattern.Obj,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Fetchdata,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Fetchfailurepatterndata,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Fetchfailurepatterndata,METHOD,Fetchfailurepatterndata.Obj,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Fetchfailurepatterndatacopy,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Fetchfailurepatterndatacopy,METHOD,Fetchfailurepatterndatacopy.Obj,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Gitlabbuilddata,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Getbuildtype,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Getbuildtype,METHOD,Getbuildtype.Buildtype,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
Getbuildtype,METHOD,Getbuildtype.Context,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
Getbuildtype,METHOD,Getbuildtype.Configurationrepo,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Buildjoblist,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Builddetailshome,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Builddetails,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Jobslist,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Jenkinsbuildfailure,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Jenkinsbuildfailuredata,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Jenkinsbuildfailurepatternfetchdata,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Jenkinsbuildfailurepatternfetchdataconfig,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Getbuildvaluestream,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Getgitlabbuildvaluestream,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller.Java,FILE,Buildtoolcontroller,CLASS,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Buildtoolcontroller.Buildtoolservice,VARIABLE,HAS_FIELD,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Getonejob,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Getbuilddetailshome,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Builddata,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Joblist,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Joblist,METHOD,Joblist.Buildtype,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Savebuildfailurepattern,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Savebuildfailurepattern,METHOD,Savebuildfailurepattern.Obj,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Fetchdata,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Buildjoblist,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Builddetailshome,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Builddetails,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Jobslist,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Jenkinsbuildfailure,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Jenkinsbuildfailuredata,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller.Java,FILE,Buildtoolcontroller,CLASS,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Buildtoolservice,VARIABLE,HAS_FIELD,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Getonejob,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Getbuilddetailshome,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Builddata,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Joblist,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Joblist,METHOD,Joblist.Buildtype,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Savebuildfailurepattern,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Savebuildfailurepattern,METHOD,Savebuildfailurepattern.Obj,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Fetchdata,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Fetchfailurepatterndata,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Fetchfailurepatterndata,METHOD,Fetchfailurepatterndata.Obj,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Fetchfailurepatterndatacopy,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Fetchfailurepatterndatacopy,METHOD,Fetchfailurepatterndatacopy.Obj,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Gitlabbuilddata,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,Getbuildtype,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Getbuildtype,METHOD,Getbuildtype.Buildtype,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
Getbuildtype,METHOD,Getbuildtype.Context,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
Getbuildtype,METHOD,Getbuildtype.Configurationrepo,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Buildjoblist,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Builddetailshome,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Builddetails,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Jobslist,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Jenkinsbuildfailure,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Jenkinsbuildfailuredata,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Jenkinsbuildfailurepatternfetchdata,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Jenkinsbuildfailurepatternfetchdataconfig,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Getbuildvaluestream,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Buildtoolcontroller,CLASS,/Getgitlabbuildvaluestream,Endpoint,EXPOSES,ServiceBolt\api\BuildToolController.java,ServiceBolt
Servicebolt,APPLICATIONS,Com.Bolt.Dashboard.Service,FOLDERS,CONTAINS,ServiceBolt\service\BuildToolService.java,ServiceBolt
Com.Bolt.Dashboard.Service,FOLDERS,Buildtoolservice.Java,FILE,CONTAINS,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolservice.Java,FILE,Buildtoolservice,INTERFACE,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolservice,INTERFACE,Searchfortest,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolservice,INTERFACE,Search,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolservice,INTERFACE,Searchjoblist,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolservice,INTERFACE,Buildfailurepattern,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolservice,INTERFACE,Fetchfailurepatterndata,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolservice,INTERFACE,Fetchbuilddata,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolservice,INTERFACE,Getonebyprojectname,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolservice,INTERFACE,Getbuilddetailshome,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolservice,INTERFACE,Getvaluestream,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolservice,INTERFACE,Getgitlabvaluestream,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,ServiceBolt
Buildtoolserviceimplemantation.Java,FILE,Buildtoolserviceimplemantation,CLASS,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Buildtoolrepository,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Buildfailurepatternforprojectrepo,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Log,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Configsettingrepo,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Metric,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Nodataconst,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Buildconst,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Search,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Search,METHOD,Search.Lastupdate,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Search,METHOD,Search.Result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Searchjoblist,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Searchjoblist,METHOD,Searchjoblist.Lastupdate,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Searchjoblist,METHOD,Searchjoblist.Result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Searchfortest,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Searchfortest,METHOD,Searchfortest.Result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation.Java,FILE,Buildtoolserviceimplemantation,CLASS,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Buildtoolrepository,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Buildfailurepatternforprojectrepo,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Log,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Configsettingrepo,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Metric,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Nodataconst,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Buildconst,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Search,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Search,METHOD,Search.Lastupdate,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Search,METHOD,Search.Result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Searchjoblist,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Searchjoblist,METHOD,Searchjoblist.Lastupdate,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Searchjoblist,METHOD,Searchjoblist.Result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Searchfortest,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Searchfortest,METHOD,Searchfortest.Result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation.Java,FILE,Buildtoolserviceimplemantation,CLASS,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Buildtoolrepository,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Buildfailurepatternforprojectrepo,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Log,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Configsettingrepo,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Metric,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Nodataconst,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Buildconst,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildfailurepattern,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildfailurepattern,METHOD,Buildfailurepattern.Patternforproject,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildfailurepattern,METHOD,Buildfailurepattern.Buildfailurepatternforprojectlist,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Fetchbuilddata,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Fetchbuilddata,METHOD,Fetchbuilddata.Result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Fetchbuilddata,METHOD,Fetchbuilddata.Lastupdated,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Fetchfailurepatterndata,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Fetchfailurepatterndata,METHOD,Fetchfailurepatterndata.Response,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Getonebyprojectname,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Getbuilddetailshome,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation.Java,FILE,Buildtoolserviceimplemantation,CLASS,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Buildtoolrepository,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Buildfailurepatternforprojectrepo,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Log,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Configsettingrepo,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Metric,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Nodataconst,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Buildtoolserviceimplemantation.Buildconst,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Search,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Search,METHOD,Search.Lastupdate,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Search,METHOD,Search.Result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Searchjoblist,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Searchjoblist,METHOD,Searchjoblist.Lastupdate,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Searchjoblist,METHOD,Searchjoblist.Result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Searchfortest,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Searchfortest,METHOD,Searchfortest.Result,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Getvaluestream,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Getvaluestream,METHOD,Getvaluestream.Proname,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Getvaluestream,METHOD,Getvaluestream.Config,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Getvaluestream,METHOD,Getvaluestream.Metric,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Getvaluestream,METHOD,Getvaluestream.Buildcalculations,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Buildtoolserviceimplemantation,CLASS,Getgitlabvaluestream,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Getgitlabvaluestream,METHOD,Getgitlabvaluestream.Proname,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Getgitlabvaluestream,METHOD,Getgitlabvaluestream.Config,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Getgitlabvaluestream,METHOD,Getgitlabvaluestream.Metric,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Getgitlabvaluestream,METHOD,Getgitlabvaluestream.Buildcalculations,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,ServiceBolt
Basemodel.Java,FILE,Basemodel,CLASS,DECLARES,UnifiedBolt\core\model\BaseModel.java,UnifiedBolt
Basemodel,CLASS,Basemodel.Id,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BaseModel.java,UnifiedBolt
Basemodel,CLASS,Getid,METHOD,DECLARES,UnifiedBolt\core\model\BaseModel.java,UnifiedBolt
Basemodel,CLASS,Setid,METHOD,DECLARES,UnifiedBolt\core\model\BaseModel.java,UnifiedBolt
Setid,METHOD,Setid.Id,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BaseModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel.Java,FILE,Buildfailurepatternforprojectinjenkinsmodel,CLASS,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Basemodel,CLASS,EXTENDS,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Buildfailurepatternforprojectinjenkinsmodel.Projectname,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Buildfailurepatternforprojectinjenkinsmodel.Username,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Buildfailurepatternforprojectinjenkinsmodel.Patternmetrics,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Buildfailurepatternforprojectinjenkinsmodel.Timestampofcreation,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Getprojectname,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Setprojectname,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Setprojectname,METHOD,Setprojectname.Projectname,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Getpatternmetrics,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Setpatternmetrics,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Setpatternmetrics,METHOD,Setpatternmetrics.Patternmetrics,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Gettimestampofcreation,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Settimestampofcreation,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Settimestampofcreation,METHOD,Settimestampofcreation.Timestampofcreation,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Addpatternmetric,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Addpatternmetric,METHOD,Addpatternmetric.Buildfailurepatternmetrics,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Addpatternmetric,METHOD,Buildfailurepatternforprojectinjenkinsmodel.Patternmetrics,VARIABLE,USES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Getusername,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternforprojectinjenkinsmodel,CLASS,Setusername,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Setusername,METHOD,Setusername.Username,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternForProjectInJenkinsModel.java,UnifiedBolt
Buildfailurepatternmetrics.Java,FILE,Buildfailurepatternmetrics,CLASS,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Buildfailurepatternmetrics.Patterndefined,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Buildfailurepatternmetrics.Patterndisplayed,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Buildfailurepatternmetrics.Patterncount,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Buildfailurepatternmetrics.Reponame,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Getpatterndefined,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Setpatterndefined,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Setpatterndefined,METHOD,Setpatterndefined.Patterndefined,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Getpatterndisplayed,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Setpatterndisplayed,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Setpatterndisplayed,METHOD,Setpatterndisplayed.Patterndisplayed,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Getpatterncount,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Setpatterncount,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Setpatterncount,METHOD,Setpatterncount.Patterncount,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Getreponame,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfailurepatternmetrics,CLASS,Setreponame,METHOD,DECLARES,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Setreponame,METHOD,Setreponame.Reponame,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFailurePatternMetrics.java,UnifiedBolt
Buildfileinfo.Java,FILE,Buildfileinfo,CLASS,DECLARES,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
Buildfileinfo,CLASS,Buildfileinfo.Filenames,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
Buildfileinfo,CLASS,Buildfileinfo.Edittype,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
Buildfileinfo,CLASS,Getfilenames,METHOD,DECLARES,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
Buildfileinfo,CLASS,Setfilenames,METHOD,DECLARES,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
Buildfileinfo,CLASS,Getedittype,METHOD,DECLARES,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
Buildfileinfo,CLASS,Setedittype,METHOD,DECLARES,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
Setfilenames,METHOD,Setfilenames.Filenames,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
Setedittype,METHOD,Setedittype.Edittype,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
Setfilenames,METHOD,Buildfileinfo.Filenames,VARIABLE,USES,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
Setedittype,METHOD,Buildfileinfo.Edittype,VARIABLE,USES,UnifiedBolt\core\model\BuildFileInfo.java,UnifiedBolt
Buildinfo.Java,FILE,Buildinfo,CLASS,DECLARES,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Buildinfo,CLASS,Buildinfo.Message,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Buildinfo,CLASS,Buildinfo.Committer,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Buildinfo,CLASS,Buildinfo.Buildfileinfolist,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Buildinfo,CLASS,Getmessage,METHOD,DECLARES,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Buildinfo,CLASS,Setmessage,METHOD,DECLARES,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Setmessage,METHOD,Setmessage.Message,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Buildinfo,CLASS,Getcommitter,METHOD,DECLARES,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Buildinfo,CLASS,Setcommitter,METHOD,DECLARES,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Setcommitter,METHOD,Setcommitter.Committer,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Buildinfo,CLASS,Getbuildfileinfolist,METHOD,DECLARES,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Buildinfo,CLASS,Setbuildfileinfolist,METHOD,DECLARES,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Setbuildfileinfolist,METHOD,Setbuildfileinfolist.Buildfileinfolist,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildInfo.java,UnifiedBolt
Unifiedbolt,PROJECT,Unifiedbolt.Applications,APPLICATIONS,CONTAINS,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Unifiedbolt/Buildsteps.Java,FILE,Buildsteps,CLASS,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Stepname,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Duration,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Result,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Startedtime,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Completedtime,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Getstepname,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Setstepname,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Setstepname,METHOD,Setstepname.Stepname,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Getduration,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Setduration,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Setduration,METHOD,Setduration.Duration,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Getresult,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Setresult,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Setresult,METHOD,Setresult.Result,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Getstartedtime,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Setstartedtime,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Setstartedtime,METHOD,Setstartedtime.Startedtime,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Getcompletedtime,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildsteps,CLASS,Setcompletedtime,METHOD,DECLARES,UnifiedBolt\core\model\BuildSteps.java,UnifiedBolt
Buildtool.Java,FILE,Buildtool,CLASS,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Collectoritemid,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Timestamp,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Timestring,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Name,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Jobname,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Url,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Version,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Buildtype,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Buildid,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Buildinfolist,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Metrics,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Stepslist,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Joblist,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Jobcount,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Createdby,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Branchname,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Reponame,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Groupname,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtool.Triggertype,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool.Java,FILE,Buildtool,CLASS,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Collectoritemid,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Timestamp,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Timestring,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Name,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Jobname,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Url,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Version,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildtype,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildid,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Buildinfolist,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Metrics,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Stepslist,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Joblist,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Jobcount,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Createdby,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Branchname,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Reponame,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Groupname,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Triggertype,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool,CLASS,Definitionid,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtool.Java,FILE,Buildtool,CLASS,DECLARES,UnifiedBolt\core\model\BuildTool.java,UnifiedBolt
Buildtoolmetric.Java,FILE,Buildtoolmetric,CLASS,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Name,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Valuebuildtool,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Formattedvaluebuildtool,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Statusmessagebuildtool,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Getname,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Setname,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Getvalue,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Setvalue,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Getformattedvalue,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Setformattedvalue,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Getstatusmessage,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Setstatusmessage,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Equals,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Buildtoolmetric,CLASS,Hashcode,METHOD,DECLARES,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Setname,METHOD,Setname.Name,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Setvalue,METHOD,Setvalue.Value,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Setformattedvalue,METHOD,Setformattedvalue.Formattedvalue,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Setstatusmessage,METHOD,Setstatusmessage.Statusmessage,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\BuildToolMetric.java,UnifiedBolt
Configurationsetting.Java,FILE,Configurationsetting,CLASS,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Configurationsetting.Timestamp,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Configurationsetting.Baseline,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Configurationsetting.Projectname,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Configurationsetting.Addflag,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Configurationsetting.Projecttype,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Configurationsetting.Manualdata,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Configurationsetting.Metric,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Ismanualdata,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Setmanualdata,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Setmanualdata,METHOD,Setmanualdata.Manualdata,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Gettimestamp,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Settimestamp,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Settimestamp,METHOD,Settimestamp.Timestamp,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Getprojectname,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Setprojectname,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Setprojectname,METHOD,Setprojectname.Projectname,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Getmetrics,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Isaddflag,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Setaddflag,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Isbaseline,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Setbaseline,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Getprojecttype,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationsetting,CLASS,Setprojecttype,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationSetting.java,UnifiedBolt
Configurationtoolinfometric.Java,FILE,Configurationtoolinfometric,CLASS,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Selected,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Id,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Toolname,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Url,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Username,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Password,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Tooltype,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Widgetname,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Jobname,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Projectcode,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Domain,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Host,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Port,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Dbtype,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Schema,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Reponame,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Secret,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Manualdata,VARIABLE,HAS_FIELD,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getid,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setid,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Gettoolname,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Settoolname,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Geturl,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Seturl,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getusername,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setusername,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getpassword,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setpassword,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Gettooltype,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Settooltype,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getwidgetname,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setwidgetname,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getjobname,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setjobname,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getprojectcode,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setprojectcode,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getselected,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setselected,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getdomain,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setdomain,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Gethost,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Sethost,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getport,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setport,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getdbtype,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setdbtype,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getschema,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setschema,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getreponame,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setreponame,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Getsecret,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Configurationtoolinfometric,CLASS,Setsecret,METHOD,DECLARES,UnifiedBolt\core\model\ConfigurationToolInfoMetric.java,UnifiedBolt
Buildfailurepatternforprojectrepo.Java,FILE,Buildfailurepatternforprojectrepo,INTERFACE,DECLARES,UnifiedBolt\core\repository\BuildFailurePatternForProjectRepo.java,UnifiedBolt
Buildfailurepatternforprojectrepo,INTERFACE,Findbyprojectname,METHOD,DECLARES,UnifiedBolt\core\repository\BuildFailurePatternForProjectRepo.java,UnifiedBolt
Buildfailurepatternforprojectrepo,INTERFACE,Findall,METHOD,DECLARES,UnifiedBolt\core\repository\BuildFailurePatternForProjectRepo.java,UnifiedBolt
Buildtoolrep.Java,FILE,Buildtoolrep,INTERFACE,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbybuildtype,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Countbybuildtype,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbybuildtypeandname,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbynameandjobname,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Getidbybuildtype,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbybuildtypeandnameandtimestampbetween,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbynameandtimestampbetween,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbyname,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findonebyname,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findonebynameorderbybuildiddesc,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbybuildtypeandreponameandgroupnameorderbybuildiddesc,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbynameandbuildtypeignorecase,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbynameandreponameandbranchname,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbynameanddefinitionid,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbybuildtypeandreponameandnameorderbybuildiddesc,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Buildtoolrep,INTERFACE,Findbybuildtypeandreponameandnameorderbytimestampdesc,METHOD,DECLARES,UnifiedBolt\core\repository\BuildToolRep.java,UnifiedBolt
Configurationsettingrep.Java,FILE,Configurationsettingrep,INTERFACE,DECLARES,UnifiedBolt\core\repository\ConfigurationSettingRep.java,UnifiedBolt
Configurationsettingrep,INTERFACE,Findbyprojectname,METHOD,DECLARES,UnifiedBolt\core\repository\ConfigurationSettingRep.java,UnifiedBolt
Configurationsettingrep,INTERFACE,Findbyprojectnamein,METHOD,DECLARES,UnifiedBolt\core\repository\ConfigurationSettingRep.java,UnifiedBolt
Configurationsettingrep,INTERFACE,Deletebyprojectname,METHOD,DECLARES,UnifiedBolt\core\repository\ConfigurationSettingRep.java,UnifiedBolt
Githubaction,FILE,Githubaction,INTERFACE,DECLARES,UnifiedBolt\githubaction\GithubAction.java,UnifiedBolt
Githubaction,INTERFACE,Getbuildtooldata,METHOD,DECLARES,UnifiedBolt\githubaction\GithubAction.java,UnifiedBolt
Getbuildtooldata,METHOD,Getbuildtooldata.Baseurl,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubAction.java,UnifiedBolt
Getbuildtooldata,METHOD,Getbuildtooldata.Repo,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubAction.java,UnifiedBolt
Getbuildtooldata,METHOD,Getbuildtooldata.Firstrun,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubAction.java,UnifiedBolt
Getbuildtooldata,METHOD,Getbuildtooldata.Branch,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubAction.java,UnifiedBolt
Getbuildtooldata,METHOD,Getbuildtooldata.Projectcode,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubAction.java,UnifiedBolt
Getbuildtooldata,METHOD,Getbuildtooldata.User,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubAction.java,UnifiedBolt
Getbuildtooldata,METHOD,Getbuildtooldata.Pass,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubAction.java,UnifiedBolt
Getbuildtooldata,METHOD,Getbuildtooldata.Projectname,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubAction.java,UnifiedBolt
Getbuildtooldata,METHOD,Getbuildtooldata.Reponame,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubAction.java,UnifiedBolt
Githubactionapplication.Java,FILE,Githubactionapplication,CLASS,DECLARES,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Logger,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Applicationcontext,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Repo,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Githubactionmetrics,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Result,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Buildtype,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Pagelimit,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Configurationrepo,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Configuration,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Metric,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Metric1,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Githubactionmain,METHOD,DECLARES,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,Githubactionmain.Applicationcontext,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,Githubactionmain.Repo,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,Githubactionmain.Instanceurl,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,Githubactionmain.Branch,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,Githubactionmain.Username,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,Githubactionmain.Password,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,Githubactionmain.Reponame,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication.Java,FILE,Githubactionapplication,CLASS,DECLARES,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Logger,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Applicationcontext,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Repo,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Githubactionmetrics,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Result,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Buildtype,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Pagelimit,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Configurationrepo,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Configuration,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Metric,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Metric1,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Githubactionmain,METHOD,DECLARES,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,Githubactionmain.Applicationcontext,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,Githubactionmain.Repo,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,Githubactionmain.Instanceurl,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,Githubactionmain.Branch,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,Githubactionmain.Username,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,Githubactionmain.Password,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,Githubactionmain.Reponame,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Unifiedbolt,PROJECT,Githubactionapplication,APPLICATIONS,CONTAINS,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication.Java,FILE,Githubactionapplication,CLASS,DECLARES,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Logger,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Applicationcontext,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Repo,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Githubactionmetrics,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Result,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Buildtype,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Pagelimit,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Configurationrepo,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Configuration,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Metric,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Metric1,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionapplication,CLASS,Githubactionmain,METHOD,DECLARES,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,Githubactionmain.Applicationcontext,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,Githubactionmain.Repo,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,Githubactionmain.Instanceurl,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,Githubactionmain.Branch,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,Githubactionmain.Username,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,Githubactionmain.Password,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionmain,METHOD,Githubactionmain.Reponame,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionApplication.java,UnifiedBolt
Githubactionimplementation.Java,FILE,Githubactionimplementation,CLASS,DECLARES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Logger,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Segment_Api,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Public_Github_Repo_Host,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Public_Github_Host_Name,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Ctx,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Projectname,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Username,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Password,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Time,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Buildrepo,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Jobcollection,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Pipelineurl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Singlepipelineurl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Githuburl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Jobsurl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Size,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Lastpage,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Lastbuildid,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Newbuildpipelinetimestamp,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation.Java,FILE,Githubactionimplementation,CLASS,DECLARES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Logger,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Segment_Api,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Public_Github_Repo_Host,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Public_Github_Host_Name,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Ctx,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Projectname,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Username,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Password,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Time,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Buildrepo,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Jobcollection,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Pipelineurl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Singlepipelineurl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Githuburl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Jobsurl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Size,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Lastpage,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Lastbuildid,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Newbuildpipelinetimestamp,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Timestamp,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Page,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Per_Page,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Totalpages,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Pipelineid,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Getbuildtooldata,METHOD,DECLARES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation.Java,FILE,Githubactionimplementation,CLASS,DECLARES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Logger,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Segment_Api,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Public_Github_Repo_Host,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Public_Github_Host_Name,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Ctx,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Projectname,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Username,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Password,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Time,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Buildrepo,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Jobcollection,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Pipelineurl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Singlepipelineurl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Githuburl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Jobsurl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Size,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Lastpage,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Lastbuildid,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githubactionimplementation.Newbuildpipelinetimestamp,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Processpipelinedata,METHOD,DECLARES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,Pipelinevalues,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,User,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,Pass,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,Reponame,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,Pipeline_Obj,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,Timestamp,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,Time,VARIABLE,USES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,Build,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,Githubactionimplementation.Pipelineid,VARIABLE,USES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,Id,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,Jobsurl,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,Durationmetric,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,Resultmetric,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,Timestampmetric,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,Singlejobsobj,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,Commitobj,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,Jobslist,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Processpipelinedata,METHOD,Tempsteps,VARIABLE,DECLARES_VARIABLE,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,FILE,Githubactionimplementation,CLASS,DECLARES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Logger,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Segment_Api,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Public_Github_Repo_Host,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Public_Github_Host_Name,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Ctx,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Projectname,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Username,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Password,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Time,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Buildrepo,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Jobcollection,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Pipelineurl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Singlepipelineurl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Githuburl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Jobsurl,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Size,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Lastpage,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Lastbuildid,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Newbuildpipelinetimestamp,VARIABLE,HAS_FIELD,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Completedate,VARIABLE,Tempdate,VARIABLE,TRANSFORMS_TO,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Completedate,VARIABLE,Tempdate,VARIABLE,TRANSFORMS_TO,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Tempsteps.Completed,VARIABLE,Tempsteps.Duration,VARIABLE,TRANSFORMS_TO,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Stepsobj.Conclusion,VARIABLE,Result,VARIABLE,TRANSFORMS_TO,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation.Java,FILE,Githubactionimplementation,CLASS,DECLARES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Makerestcall,METHOD,DECLARES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Createheaders,METHOD,DECLARES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Githubactionimplementation,CLASS,Get,METHOD,DECLARES,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Temp_Date,VARIABLE,Splitdate,VARIABLE,TRANSFORMS_TO,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Splitdate,VARIABLE,Temp,VARIABLE,TRANSFORMS_TO,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Temp,VARIABLE,Temptime,VARIABLE,TRANSFORMS_TO,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Temptime,VARIABLE,Createddate,VARIABLE,TRANSFORMS_TO,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Auth,VARIABLE,Encodedauth,VARIABLE,TRANSFORMS_TO,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
Encodedauth,VARIABLE,Authheader,VARIABLE,TRANSFORMS_TO,UnifiedBolt\githubaction\GithubActionImplementation.java,UnifiedBolt
