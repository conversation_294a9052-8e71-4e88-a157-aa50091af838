source_node,source_type,destination_node,destination_type,relationship,file_path,application
buildtoolcontroller.java,FILE,buildtoolcontroller,CLASS,DECLARES,ServiceBolt\api\BuildToolController.java,serviceBolt
buildtoolcontroller,CLASS,buildtoolcontrollerBuildtoolservice,VARIABLE,HAS_FIELD,ServiceBolt\api\BuildToolController.java,serviceBolt
buildtoolcontroller,CLASS,getonejob,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,serviceBolt
buildtoolcontroller,CLASS,getbuilddetailshome,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,serviceBolt
buildtoolcontroller,CLASS,builddata,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,serviceBolt
buildtoolcontroller,CLASS,joblist,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,serviceBolt
joblist,METHOD,joblistBuildtype,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,serviceBolt
buildtoolcontroller,CLASS,savebuildfailurepattern,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,serviceBolt
savebuildfailurepattern,METHOD,savebuildfailurepatternObj,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,serviceBolt
buildtoolcontroller,CLASS,fetchdata,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,serviceBolt
buildtoolcontroller,CLASS,fetchfailurepatterndata,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,serviceBolt
fetchfailurepatterndata,METHOD,fetchfailurepatterndataObj,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,serviceBolt
buildtoolcontroller,CLASS,fetchfailurepatterndatacopy,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,serviceBolt
fetchfailurepatterndatacopy,METHOD,fetchfailurepatterndatacopyObj,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,serviceBolt
buildtoolcontroller,CLASS,gitlabbuilddata,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,serviceBolt
buildtoolcontroller,CLASS,getbuildtype,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,serviceBolt
getbuildtype,METHOD,getbuildtypeBuildtype,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,serviceBolt
getbuildtype,METHOD,getbuildtypeContext,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,serviceBolt
getbuildtype,METHOD,getbuildtypeConfigurationrepo,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,serviceBolt
buildtoolcontroller,CLASS,requestmappingbuildjoblist,ENDPOINT,EXPOSES,ServiceBolt\api\BuildToolController.java,serviceBolt
buildtoolcontroller,CLASS,requestmappingbuilddetailshome,ENDPOINT,EXPOSES,ServiceBolt\api\BuildToolController.java,serviceBolt
buildtoolcontroller,CLASS,requestmappingbuilddetails,ENDPOINT,EXPOSES,ServiceBolt\api\BuildToolController.java,serviceBolt
buildtoolcontroller,CLASS,requestmappingjobslist,ENDPOINT,EXPOSES,ServiceBolt\api\BuildToolController.java,serviceBolt
buildtoolcontroller,CLASS,requestmappingjenkinsbuildfailure,ENDPOINT,EXPOSES,ServiceBolt\api\BuildToolController.java,serviceBolt
buildtoolcontroller,CLASS,requestmappingjenkinsbuildfailuredata,ENDPOINT,EXPOSES,ServiceBolt\api\BuildToolController.java,serviceBolt
buildtoolcontroller,CLASS,requestmappingjenkinsbuildfailurepatternfetchdata,ENDPOINT,EXPOSES,ServiceBolt\api\BuildToolController.java,serviceBolt
buildtoolcontroller,CLASS,requestmappingjenkinsbuildfailurepatternfetchdataconfig,ENDPOINT,EXPOSES,ServiceBolt\api\BuildToolController.java,serviceBolt
buildtoolcontroller,CLASS,requestmappinggetbuildvaluestream,ENDPOINT,EXPOSES,ServiceBolt\api\BuildToolController.java,serviceBolt
buildtoolcontroller,CLASS,requestmappinggetgitlabbuildvaluestream,ENDPOINT,EXPOSES,ServiceBolt\api\BuildToolController.java,serviceBolt
servicebolt,FOLDERS,buildtoolcontroller.java,FILE,CONTAINS,ServiceBolt\api\BuildToolController.java,serviceBolt
buildtoolcontroller.java,FILE,buildtoolcontroller,CLASS,DECLARES,ServiceBolt\api\BuildToolController.java,serviceBolt
buildtoolcontroller,CLASS,buildtoolcontrollerBuildtoolservice,VARIABLE,HAS_FIELD,ServiceBolt\api\BuildToolController.java,serviceBolt
buildtoolcontroller,CLASS,getonejob,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,serviceBolt
buildtoolcontroller,CLASS,getbuilddetailshome,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,serviceBolt
buildtoolcontroller,CLASS,builddata,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,serviceBolt
buildtoolcontroller,CLASS,joblist,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,serviceBolt
joblist,METHOD,joblistBuildtype,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,serviceBolt
buildtoolcontroller,CLASS,savebuildfailurepattern,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,serviceBolt
savebuildfailurepattern,METHOD,savebuildfailurepatternObj,VARIABLE,DECLARES_VARIABLE,ServiceBolt\api\BuildToolController.java,serviceBolt
buildtoolcontroller,CLASS,fetchdata,METHOD,DECLARES,ServiceBolt\api\BuildToolController.java,serviceBolt
buildtoolcontrollerFetchbuilddata,OPERATION,buildtoolcontrollerBuildtoolservice,VARIABLE,ASSIGNS_VALUE,ServiceBolt\api\BuildToolController.java,serviceBolt
buildtoolservice.java,FILE,buildtoolservice,INTERFACE,DECLARES,ServiceBolt\service\BuildToolService.java,serviceBolt
buildtoolservice,INTERFACE,searchfortest,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,serviceBolt
buildtoolservice,INTERFACE,search,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,serviceBolt
buildtoolservice,INTERFACE,searchjoblist,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,serviceBolt
buildtoolservice,INTERFACE,buildfailurepattern,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,serviceBolt
buildtoolservice,INTERFACE,fetchfailurepatterndata,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,serviceBolt
buildtoolservice,INTERFACE,fetchbuilddata,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,serviceBolt
buildtoolservice,INTERFACE,getonebyprojectname,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,serviceBolt
buildtoolservice,INTERFACE,getbuilddetailshome,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,serviceBolt
buildtoolservice,INTERFACE,getvaluestream,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,serviceBolt
buildtoolservice,INTERFACE,getgitlabvaluestream,METHOD,DECLARES,ServiceBolt\service\BuildToolService.java,serviceBolt
buildtoolserviceimplemantation.java,FILE,buildtoolserviceimplemantation,CLASS,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildtoolserviceimplemantationBuildtoolrepository,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildtoolserviceimplemantationBuildfailurepatternforprojectrepo,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildtoolserviceimplemantationLog,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildtoolserviceimplemantationConfigsettingrepo,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildtoolserviceimplemantationMetric,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildtoolserviceimplemantationNodataconst,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildtoolserviceimplemantationBuildconst,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,search,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
search,METHOD,searchLastupdate,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
search,METHOD,searchResult,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,searchjoblist,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
searchjoblist,METHOD,searchjoblistLastupdate,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
searchjoblist,METHOD,searchjoblistResult,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,searchfortest,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
searchfortest,METHOD,searchfortestResult,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation.java,FILE,buildtoolserviceimplemantation,CLASS,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildtoolserviceimplemantationBuildtoolrepository,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildtoolserviceimplemantationBuildfailurepatternforprojectrepo,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildtoolserviceimplemantationLog,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildtoolserviceimplemantationConfigsettingrepo,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildtoolserviceimplemantationMetric,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildtoolserviceimplemantationNodataconst,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildtoolserviceimplemantationBuildconst,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,search,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
search,METHOD,searchLastupdate,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
search,METHOD,searchResult,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,searchjoblist,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
searchjoblist,METHOD,searchjoblistLastupdate,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
searchjoblist,METHOD,searchjoblistResult,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,searchfortest,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
searchfortest,METHOD,searchfortestResult,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildtoolserviceimplemantationSearch,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildtoolserviceimplemantationSearchjoblist,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildtoolserviceimplemantationSearchfortest,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation.java,FILE,buildtoolserviceimplemantation,CLASS,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildtoolserviceimplemantationBuildtoolrepository,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildtoolserviceimplemantationBuildfailurepatternforprojectrepo,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildtoolserviceimplemantationLog,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildtoolserviceimplemantationConfigsettingrepo,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildtoolserviceimplemantationMetric,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildtoolserviceimplemantationNodataconst,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildtoolserviceimplemantationBuildconst,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildfailurepattern,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,fetchbuilddata,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,fetchfailurepatterndata,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,getonebyprojectname,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,getbuilddetailshome,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildfailurepattern,METHOD,buildfailurepatternToolname,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildfailurepattern,METHOD,buildfailurepatternFailurerequestlist,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildfailurepattern,METHOD,buildfailurepatternPatternforproject,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildfailurepattern,METHOD,buildfailurepatternBuildfailurepatternforprojectlist,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildfailurepattern,METHOD,buildfailurepatternBuildfailurepatternforproject,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
fetchbuilddata,METHOD,fetchbuilddataLastupdated,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
fetchbuilddata,METHOD,fetchbuilddataResult,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
fetchfailurepatterndata,METHOD,fetchfailurepatterndataProjectname,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
fetchfailurepatterndata,METHOD,fetchfailurepatterndataLastupdated,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
fetchfailurepatterndata,METHOD,fetchfailurepatterndataResponse,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
getonebyprojectname,METHOD,getonebyprojectnameProjname,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
getonebyprojectname,METHOD,getonebyprojectnameResult,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
getbuilddetailshome,METHOD,getbuilddetailshomeProjname,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
getbuilddetailshome,METHOD,getbuilddetailshomeAlmtype,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
getbuilddetailshome,METHOD,getbuilddetailshomeProjhomecalc,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
getbuilddetailshome,METHOD,getbuilddetailshomeResult,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation.java,FILE,buildtoolserviceimplemantation,CLASS,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildtoolserviceimplemantationBuildtoolrepository,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildtoolserviceimplemantationBuildfailurepatternforprojectrepo,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildtoolserviceimplemantationLog,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildtoolserviceimplemantationConfigsettingrepo,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildtoolserviceimplemantationMetric,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildtoolserviceimplemantationNodataconst,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,buildtoolserviceimplemantationBuildconst,VARIABLE,HAS_FIELD,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,search,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
search,METHOD,searchLastupdate,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
search,METHOD,searchResult,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,searchjoblist,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
searchjoblist,METHOD,searchjoblistLastupdate,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
searchjoblist,METHOD,searchjoblistResult,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,searchfortest,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
searchfortest,METHOD,searchfortestResult,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,getvaluestream,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
getvaluestream,METHOD,getvaluestreamProname,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
getvaluestream,METHOD,getvaluestreamConfig,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
getvaluestream,METHOD,getvaluestreamMetric,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
configsettingrepoFindbyprojectname,OPERATION,getvaluestreamConfig,VARIABLE,ASSIGNS_VALUE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
buildtoolserviceimplemantation,CLASS,getgitlabvaluestream,METHOD,DECLARES,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
getgitlabvaluestream,METHOD,getgitlabvaluestreamProname,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
getgitlabvaluestream,METHOD,getgitlabvaluestreamConfig,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
getgitlabvaluestream,METHOD,getgitlabvaluestreamMetric,VARIABLE,DECLARES_VARIABLE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
configsettingrepoFindbyprojectname,OPERATION,getgitlabvaluestreamConfig,VARIABLE,ASSIGNS_VALUE,ServiceBolt\service\BuildToolServiceImplemantation.java,serviceBolt
