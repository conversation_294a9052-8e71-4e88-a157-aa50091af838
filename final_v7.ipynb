# ========== CONFIGURATION AND IMPORTS ==========
import os
from pathlib import Path
from tqdm import tqdm
import pandas as pd
import re
from collections import defaultdict

from tree_sitter import Language, Parser
import tree_sitter_java as tsjava

from langchain_community.document_loaders import TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language
from langchain_google_genai import ChatG<PERSON>gleGenerativeA<PERSON>
from langchain_experimental.graph_transformers import LLMGraphTransformer
from langchain_community.graphs import Neo4jGraph
from langchain_openai import AzureChatOpenAI
# Configuration
BASE_PATH = Path(r"C:/Shaik/sample/OneInsights")
NEO4J_URI = "bolt://localhost:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "Test@7889"
NEO4J_DB = "oneinsights"


# Initialize components
graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)
JAVA_LANGUAGE = Language(tsjava.language())
parser = Parser(JAVA_LANGUAGE)

llm = AzureChatOpenAI(
    api_key="********************************",
    azure_endpoint="https://azureopenaibrsc.openai.azure.com/",
    azure_deployment="gpt-4o",
    api_version="2024-12-01-preview"
)

print("✅ Configuration and imports completed successfully")



# ========== STAGE 1: FOLDER + FILE HIERARCHY ==========
def extract_folder_file_hierarchy(base_path):
    folder_records, file_records = [], []
    base_path = os.path.abspath(base_path)
    base_folder_name = os.path.basename(base_path)
    processed_folders = set()

    for root, dirs, files in os.walk(base_path):
        rel_root = os.path.relpath(root, base_path)
        parent_folder = base_folder_name if rel_root == '.' else os.path.dirname(rel_root) or base_folder_name
        current_folder = base_folder_name if rel_root == '.' else os.path.basename(rel_root)

        folder_key = f'{parent_folder}->{current_folder}'
        if folder_key not in processed_folders and parent_folder != current_folder:
            folder_records.append({
                'source_node': parent_folder,
                'source_type': 'folder',
                'destination_node': current_folder,
                'destination_type': 'folder',
                'relationship': 'contains',
                'file_path': None
            })
            processed_folders.add(folder_key)

        for f in files:
            if f.endswith('.java'):
                file_rel_path = os.path.relpath(os.path.join(root, f), base_path)
                file_records.append({
                    'source_node': current_folder,
                    'source_type': 'folder',
                    'destination_node': f,
                    'destination_type': 'file',
                    'relationship': 'contains',
                    'file_path': file_rel_path
                })
    return pd.DataFrame(folder_records), pd.DataFrame(file_records)

df_folders, df_files = extract_folder_file_hierarchy(BASE_PATH)

# Normalize folder/file nodes
def normalize_nodes(df):
    for col in ["source_node", "destination_node"]:
        df[col] = df[col].astype(str).apply(lambda x: x.replace("\\", "/").split("/")[-1])
    return df

df_folders = normalize_nodes(df_folders)
df_files = normalize_nodes(df_files)

print(f"Stage 1 Complete: {len(df_folders)} folder rels, {len(df_files)} file rels")


df_folders

df_files

import re

# ========== UTILITY FUNCTIONS ==========

def read_source_code(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read()

def extract_package_and_imports(source_code_str):
    package_pattern = r'package\s+([\w\.]+);'
    import_pattern = r'import\s+(?:static\s+)?([\w\.]+);'
    package_match = re.search(package_pattern, source_code_str)
    package_name = package_match.group(1) if package_match else None
    import_matches = re.findall(import_pattern, source_code_str)
    return package_name, import_matches

def extract_path_from_annotation(annotation_str):
    match = re.search(r'(?:path|value)?\s*=\s*["\']([^"\']+)["\']', annotation_str)
    if match:
        return match.group(1)
    elif annotation_str:
        return annotation_str.strip(' "\'')
    return ''

def extract_api_endpoints(source_code_str):
    endpoints = []

    # Class-level base path
    class_mapping = re.search(r'@RequestMapping\s*\(([^)]*)\)', source_code_str)
    base_path = extract_path_from_annotation(class_mapping.group(1)) if class_mapping else ""

    # Method-level mappings
    method_pattern = r'@(?:GetMapping|PostMapping|PutMapping|DeleteMapping|PatchMapping|RequestMapping)\s*(?:\(([^)]*)\))?\s*(?:\n|\r\n)?\s*(?:public|private|protected)?\s*\w+\s+(\w+)\s*\('
    method_matches = re.findall(method_pattern, source_code_str)

    for annotation_str, method_name in method_matches:
        method_path = extract_path_from_annotation(annotation_str)
        full_path = '/'.join([base_path.strip('/'), method_path.strip('/')]).strip('/')
        full_path = '/' + full_path if full_path else '/'
        mapping_type = re.search(r'@(GetMapping|PostMapping|PutMapping|DeleteMapping|PatchMapping|RequestMapping)', annotation_str)
        mapping = mapping_type.group(1) if mapping_type else 'RequestMapping'

        endpoints.append({
            'type': mapping,
            'path': full_path,
            'method': mapping.replace('Mapping', '').upper() if mapping != 'RequestMapping' else 'GET',
            'method_name': method_name
        })

    # Deduplicate based on path + method_name
    seen = set()
    unique_endpoints = []
    for ep in endpoints:
        key = (ep['path'], ep['method_name'])
        if key not in seen:
            seen.add(key)
            unique_endpoints.append(ep)

    return unique_endpoints

def extract_database_entities(source_code_str):
    entities = []

    # Detect @Table name
    table_matches = re.findall(r'@Table\s*\(\s*name\s*=\s*["\']([^"\']+)["\']', source_code_str)
    for table_name in table_matches:
        entities.append({
            'type': 'table',
            'name': table_name.strip().lower()
        })

    # If @Entity exists but no @Table, fallback to class name
    if '@Entity' in source_code_str and not table_matches:
        class_matches = re.findall(r'public\s+class\s+(\w+)', source_code_str)
        for class_name in class_matches:
            table_name = re.sub('([a-z0-9])([A-Z])', r'\1_\2', class_name).lower()
            entities.append({
                'type': 'table',
                'name': table_name
            })

    # Extract tables from @Query
    query_patterns = [
        r'@Query\s*\(\s*["\']([^"\']*(?:FROM|from)\s+([\w]+)[^"\']*)["\']',
        r'@Query\s*\(\s*value\s*=\s*["\']([^"\']*(?:FROM|from)\s+([\w]+)[^"\']*)["\']',
        r'@Query\s*\(\s*nativeQuery\s*=\s*true\s*,\s*value\s*=\s*["\']([^"\']*(?:FROM|from)\s+([\w]+)[^"\']*)["\']'
    ]

    for pattern in query_patterns:
        query_matches = re.findall(pattern, source_code_str, re.IGNORECASE)
        for match in query_matches:
            if isinstance(match, tuple) and len(match) >= 2:
                table_name = match[1].strip()
                if table_name.lower() not in ['select', 'where', 'order', 'group']:
                    entities.append({
                        'type': 'table',
                        'name': table_name.lower()
                    })

    # Infer table names from repository interfaces
    repo_matches = re.findall(r'interface\s+(\w+)\s+extends\s+.*Repository', source_code_str)
    for repo_name in repo_matches:
        entity_name = repo_name.replace('Repository', '')
        if entity_name:
            entities.append({
                'type': 'table',
                'name': entity_name.lower()
            })

    # Deduplicate tables
    seen_tables = set()
    deduped = []
    for e in entities:
        if e['type'] == 'table' and e['name'] not in seen_tables:
            seen_tables.add(e['name'])
            deduped.append(e)
        elif e['type'] != 'table':
            deduped.append(e)

    return deduped


import re

def extract_interface_extends(source_code_str):
    extends_relationships = []

    # ========== Interface extends ==========
    interface_extends_pattern = r'\binterface\s+(\w+)\s+extends\s+([\w<>,\s]+)'
    matches = re.findall(interface_extends_pattern, source_code_str)
    for interface_name, extends_clause in matches:
        parent_interfaces = [re.sub(r'<.*?>', '', p).strip() for p in extends_clause.split(',')]
        for parent in parent_interfaces:
            if parent:
                extends_relationships.append({
                    'type': 'interface_extends',
                    'child': interface_name,
                    'parent': parent,
                    'full_extends': extends_clause.strip()
                })

    # ========== Class extends ==========
    class_extends_pattern = r'\bclass\s+(\w+)\s+extends\s+([\w<>\.]+)'
    class_matches = re.findall(class_extends_pattern, source_code_str)
    for class_name, parent_class in class_matches:
        parent_class = re.sub(r'<.*?>', '', parent_class).strip()
        if parent_class:
            extends_relationships.append({
                'type': 'class_extends',
                'child': class_name,
                'parent': parent_class,
                'full_extends': parent_class
            })

    # ========== Class implements ==========
    implements_pattern = r'\bclass\s+(\w+)(?:\s+extends\s+\w+(?:<.*?>)?)?\s+implements\s+([\w<>,\s]+)'
    impl_matches = re.findall(implements_pattern, source_code_str)
    for class_name, implements_clause in impl_matches:
        interfaces = [re.sub(r'<.*?>', '', p).strip() for p in implements_clause.split(',')]
        for interface in interfaces:
            if interface:
                extends_relationships.append({
                    'type': 'class_implements',
                    'child': class_name,
                    'parent': interface,
                    'full_extends': implements_clause.strip()
                })

    # ========== Deduplication (optional) ==========
    seen = set()
    deduped = []
    for r in extends_relationships:
        key = (r['type'], r['child'], r['parent'])
        if key not in seen:
            seen.add(key)
            deduped.append(r)

    return deduped


import os

def build_enhanced_class_registry(base_path):
    class_registry = {}

    for root, _, files in os.walk(base_path):
        for file in files:
            if not file.endswith('.java'):
                continue

            file_path = os.path.join(root, file)

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    source_code_str = f.read()

                # Extract class name and package
                class_name = os.path.splitext(file)[0]
                package_name, imports = extract_package_and_imports(source_code_str)

                # Fully qualified class name
                fqcn = f"{package_name}.{class_name}" if package_name else class_name

                # Run extractors
                endpoints = extract_api_endpoints(source_code_str)
                db_entities = extract_database_entities(source_code_str)
                interface_extends = extract_interface_extends(source_code_str)

                # Build class metadata
                class_registry[fqcn] = {
                    'fqcn': fqcn,
                    'class_name': class_name,
                    'package': package_name,
                    'file_path': file_path,
                    'imports': imports,
                    'endpoints': endpoints,
                    'db_entities': db_entities,
                    'interface_extends': interface_extends
                }

                # Optional: Debug prints
                if endpoints:
                    print(f"✅ Found {len(endpoints)} endpoints in {fqcn}")
                if db_entities:
                    print(f"✅ Found {len(db_entities)} DB entities in {fqcn}")

            except Exception as e:
                print(f"❌ Error processing {file_path}: {e}")
                continue

    print(f"📦 Class registry built with {len(class_registry)} entries.")
    return class_registry


class_registry = build_enhanced_class_registry(BASE_PATH)




# =========================
# Helper Functions
# =========================

def read_source_code(file_path):
    with open(file_path, 'rb') as f:
        return f.read()

def clean_node_name(name):
    """Clean node names by stripping prefixes, suffixes, and file extensions."""
    if not name:
        return name
    prefixes = ['method:', 'class:', 'variable:', 'field:']
    for prefix in prefixes:
        if name.lower().startswith(prefix):
            name = name[len(prefix):]
    return re.sub(r'\.(java|class)$', '', name, flags=re.IGNORECASE).strip()

# class_registry should already be built earlier
# from build_enhanced_class_registry() and available globally

# =========================
# AST Extraction Function
# =========================

def extract_ast_structure(file_path):
    records = []
    source_code = read_source_code(file_path)
    tree = parser.parse(source_code)
    root_node = tree.root_node
    file_name = os.path.basename(file_path)

    def traverse(node, parent_type=None, parent_name=None):
        # ---- CLASS Declaration ----
        if node.type == 'class_declaration':
            class_name = None
            for child in node.children:
                if child.type == 'identifier':
                    class_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    records.append({
                        'source_node': file_name,
                        'source_type': 'file',
                        'destination_node': class_name,
                        'destination_type': 'class',
                        'relationship': 'declares',
                        'file_path': file_path
                    })

                    class_info = class_registry.get(class_name, {})
                    for ep in class_info.get('endpoints', []):
                        records.append({
                            'source_node': class_name,
                            'source_type': 'class',
                            'destination_node': f"{ep['method']} {ep['path']}",
                            'destination_type': 'endpoint',
                            'relationship': 'declares',
                            'file_path': file_path
                        })
                    for entity in class_info.get('db_entities', []):
                        records.append({
                            'source_node': class_name,
                            'source_type': 'class',
                            'destination_node': entity['name'],
                            'destination_type': 'table',
                            'relationship': 'maps_to',
                            'file_path': file_path
                        })
                    for ext_rel in class_info.get('interface_extends', []):
                        rel_type = 'extends' if ext_rel.get('type') == 'class_extends' else 'implements'
                        records.append({
                            'source_node': class_name,
                            'source_type': 'class',
                            'destination_node': ext_rel['parent_interface'],
                            'destination_type': 'interface' if rel_type == 'implements' else 'class',
                            'relationship': rel_type,
                            'file_path': file_path
                        })
                    break
            for child in node.children:
                traverse(child, 'class', class_name)

        # ---- INTERFACE Declaration ----
        elif node.type == 'interface_declaration':
            interface_name = None
            for child in node.children:
                if child.type == 'identifier':
                    interface_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    records.append({
                        'source_node': file_name,
                        'source_type': 'file',
                        'destination_node': interface_name,
                        'destination_type': 'interface',
                        'relationship': 'declares',
                        'file_path': file_path
                    })
                    break
            for child in node.children:
                traverse(child, 'interface', interface_name)

        # ---- METHOD Declaration ----
        elif node.type == 'method_declaration':
            method_name = None
            for child in node.children:
                if child.type == 'identifier':
                    method_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    if parent_name and parent_type in ['class', 'interface']:
                        records.append({
                            'source_node': parent_name,
                            'source_type': parent_type,
                            'destination_node': method_name,
                            'destination_type': 'method',
                            'relationship': 'declares',
                            'file_path': file_path
                        })
                    break
            for child in node.children:
                traverse(child, 'method', method_name)

        # ---- FIELD Declaration ----
        elif node.type == 'field_declaration':
            for child in node.children:
                if child.type == 'variable_declarator':
                    for grandchild in child.children:
                        if grandchild.type == 'identifier':
                            field_name = clean_node_name(source_code[grandchild.start_byte:grandchild.end_byte].decode('utf-8'))
                            if parent_name and parent_type == 'class':
                                records.append({
                                    'source_node': parent_name,
                                    'source_type': 'class',
                                    'destination_node': field_name,
                                    'destination_type': 'variable',
                                    'relationship': 'has_field',
                                    'file_path': file_path
                                })

        # ---- VARIABLE USE (in method) ----
        elif node.type in ['assignment_expression', 'variable_declarator'] and parent_type == 'method':
            for child in node.children:
                if child.type == 'identifier':
                    var_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    if var_name and var_name != 'this':
                        records.append({
                            'source_node': parent_name,
                            'source_type': 'method',
                            'destination_node': var_name,
                            'destination_type': 'variable',
                            'relationship': 'uses',
                            'file_path': file_path
                        })

        # ---- FIELD ACCESS ----
        elif node.type == 'field_access' and parent_type == 'method':
            for child in node.children:
                if child.type == 'identifier':
                    field_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    if field_name and field_name != 'this':
                        records.append({
                            'source_node': parent_name,
                            'source_type': 'method',
                            'destination_node': field_name,
                            'destination_type': 'variable',
                            'relationship': 'uses',
                            'file_path': file_path
                        })

        # ---- RETURN variable ----
        elif node.type == 'return_statement' and parent_type == 'method':
            for child in node.children:
                if child.type == 'identifier':
                    var_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    if var_name:
                        records.append({
                            'source_node': parent_name,
                            'source_type': 'method',
                            'destination_node': var_name,
                            'destination_type': 'variable',
                            'relationship': 'uses',
                            'file_path': file_path
                        })

        # ---- Recurse for all children ----
        for child in node.children:
            traverse(child, parent_type, parent_name)

    traverse(root_node)
    return records

# =========================
# Process All Files
# =========================

ast_records = []
for root, _, files in os.walk(BASE_PATH):
    for file in files:
        if file.endswith('.java'):
            file_path = os.path.join(root, file)
            try:
                ast_records.extend(extract_ast_structure(file_path))
            except Exception as e:
                print(f'[Error] Failed to parse {file}: {e}')

df_ast = pd.DataFrame(ast_records)
print(f'Stage 2 Complete ✅ — {len(df_ast)} AST relationships extracted')


df_ast.sample(5)


splitter = RecursiveCharacterTextSplitter.from_language(
    language=LC_Language.JAVA,
    chunk_size=4000,
    chunk_overlap=200
)

java_docs, split_docs = [], []
for root, _, files in os.walk(BASE_PATH):
    for file in files:
        if file.endswith('.java'):
            try:
                loader = TextLoader(os.path.join(root, file))
                java_docs.extend(loader.load())
            except Exception as e:
                print(f" Error loading {file}: {e}")
                continue

for doc in java_docs:
    split_docs.extend(splitter.split_documents([doc]))

def build_enhanced_system_prompt(file_path, ast_df, class_registry):
    """
    Builds an enhanced prompt for LLM-based Java code lineage extraction using AST and class registry context.
    """

    # Extract AST context for the given file
    ast_subset = ast_df[ast_df['file_path'] == file_path] if not ast_df.empty else pd.DataFrame()
    ast_context_lines = []
    for _, row in ast_subset.iterrows():
        src_type = row.get('source_type', 'unknown')
        src_node = row.get('source_node', 'unknown')
        dst_type = row.get('destination_type', 'unknown')
        dst_node = row.get('destination_node', 'unknown')
        rel = row.get('relationship', 'unknown')
        ast_context_lines.append(f"{src_type}:{src_node} -[{rel}]-> {dst_type}:{dst_node}")
    ast_context = '\n'.join(ast_context_lines)

    # Extract registry context
    registry_context_lines = ['Known Classes:']
    for class_name, info in class_registry.items():
        fqcn = info.get('fqcn', class_name)
        registry_context_lines.append(f"- {class_name} (FQCN: {fqcn})")
        if info.get('endpoints'):
            registry_context_lines.append(f"  * {len(info['endpoints'])} API endpoint(s)")
        if info.get('db_entities'):
            registry_context_lines.append(f"  * {len(info['db_entities'])} DB entity/entities")
    registry_context = '\n'.join(registry_context_lines)

    # Final system prompt
    prompt = f"""You are a Java code lineage extraction engine. Extract relationships between code entities with STRICT focus on:

CONTEXT:
{registry_context}

AST RELATIONSHIPS (FOLLOW THESE PATTERNS EXACTLY):
{ast_context}

CRITICAL RULES - FOLLOW EXACTLY:
1. Use SIMPLE names only (remove prefixes like "method:", "class:", etc.)
2. MANDATORY RELATIONSHIP DIRECTIONS (DO NOT REVERSE):
   - file -[declares]-> class
   - class -[declares]-> method  
   - class -[has_field]-> variable
   - method -[uses]-> variable
   - class -[declares]-> endpoint
   - class -[maps_to]-> table
3. Extract REST API endpoints as 'endpoint' nodes (e.g., GET /api/users, POST /api/data)
4. Extract database tables from @Entity, @Table, @Query annotations
5. Extract interface extends and class implements relationships
6. NEVER create reverse relationships (method->class, variable->method, etc.)
7. Follow the AST RELATIONSHIPS above for correct structure
8. Clean node names (remove "method:", "class:" prefixes)

Extract triples in format:
[SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName

Return ONLY the triples, no explanations.
"""
    return prompt



all_llm_lineage = []

def normalize_entity(entity_name, entity_type):
    """Normalize node names for consistency."""
    if not entity_name:
        return entity_name

    # Remove known prefixes
    prefixes = ['method:', 'class:', 'variable:', 'field:', 'table:', 'endpoint:']
    for prefix in prefixes:
        if entity_name.lower().startswith(prefix):
            entity_name = entity_name[len(prefix):]

    # Remove file extensions
    entity_name = re.sub(r'\.(java|class)$', '', entity_name, flags=re.IGNORECASE)

    # Clean dots for class/method names
    if entity_type in ['class', 'method'] and '.' in entity_name:
        entity_name = entity_name.split('.')[-1]

    # Match with class registry
    if entity_type == 'class':
        for class_name in class_registry.keys():
            if entity_name.lower() == class_name.lower():
                return class_name.lower()

    return entity_name.strip().lower()

# Define valid directions for filtering
valid_directions = {
    ('file', 'declares', 'class'),
    ('file', 'declares', 'interface'),
    ('class', 'declares', 'method'),
    ('interface', 'declares', 'method'),
    ('class', 'declares', 'endpoint'),
    ('class', 'has_field', 'variable'),
    ('method', 'uses', 'variable'),
    ('class', 'maps_to', 'table'),
    ('class', 'extends', 'class'),
    ('class', 'implements', 'interface'),
    ('interface', 'extends', 'interface'),
    ('method', 'calls', 'method'),
    ('method', 'reads_from', 'table'),
    ('method', 'writes_to', 'table')
}

# Loop through each chunk
for chunk in tqdm(split_docs, desc='Stage 3: Enhanced LLM Extraction'):
    file_path = chunk.metadata.get('source')
    try:
        system_prompt = build_enhanced_system_prompt(file_path, df_ast, class_registry)

        transformer = LLMGraphTransformer(
            llm=llm,
            additional_instructions=system_prompt,
            allowed_nodes=['file', 'class', 'interface', 'method', 'variable', 'table', 'endpoint'],
            allowed_relationships=[
                ('file', 'declares', 'class'),
                ('file', 'declares', 'interface'),
                ('class', 'declares', 'method'),
                ('interface', 'declares', 'method'),
                ('class', 'declares', 'endpoint'),
                ('method', 'calls', 'method'),
                ('class', 'has_field', 'variable'),
                ('method', 'uses', 'variable'),
                ('class', 'uses', 'class'),
                ('interface', 'extends', 'interface'),
                ('class', 'extends', 'class'),
                ('class', 'implements', 'interface'),
                ('class', 'maps_to', 'table'),
                ('method', 'reads_from', 'table'),
                ('method', 'writes_to', 'table'),
            ],
            strict_mode=True,
            node_properties=False,
            relationship_properties=False,
        )

        graph_docs = transformer.convert_to_graph_documents([chunk])

        for gd in graph_docs:
            for rel in gd.relationships:
                s_node = normalize_entity(rel.source.id.strip(), rel.source.type.strip().lower())
                s_type = rel.source.type.strip().lower()
                t_node = normalize_entity(rel.target.id.strip(), rel.target.type.strip().lower())
                t_type = rel.target.type.strip().lower()
                rel_type = rel.type.strip().lower()

                # Skip invalid or duplicate relationships
                if not s_node or not t_node or (s_node == t_node and s_type == t_type):
                    continue
                if (s_type, rel_type, t_type) not in valid_directions:
                    continue

                all_llm_lineage.append({
                    'source_node': s_node,
                    'source_type': s_type,
                    'destination_node': t_node,
                    'destination_type': t_type,
                    'relationship': rel_type,
                    'file_path': file_path
                })

    except Exception as e:
        print(f"⚠️ Error processing chunk from {file_path}: {e}")
        continue

df_llm_lineage = pd.DataFrame(all_llm_lineage)
print(f'✅ Stage 3 Complete: {len(df_llm_lineage)} LLM relationships extracted.')



df_llm_lineage.head(5)

# ========== STAGE 4: DATA NORMALIZATION AND CLEANING ==========

import pandas as pd
import re

def clean_node_name(name: str) -> str:
    """Cleans and standardizes node names."""
    if not isinstance(name, str):
        return ''
    name = re.sub(r'^(method|class|variable|field|table|endpoint):', '', name, flags=re.IGNORECASE)
    name = re.sub(r'\.(java|class)$', '', name, flags=re.IGNORECASE)
    return name.strip().lower()

def clean_dataframe_names(df: pd.DataFrame) -> pd.DataFrame:
    """Apply node name cleaning on relevant columns of the DataFrame."""
    if df is None or len(df) == 0:
        return pd.DataFrame(columns=["source_node", "source_type", "destination_node", "destination_type", "relationship"])

    df = df.copy()
    for col in ["source_node", "destination_node"]:
        if col in df.columns:
            df[col] = df[col].astype(str).apply(clean_node_name)
    
    return df

def normalize_dataframe_types(df: pd.DataFrame) -> pd.DataFrame:
    """Lowercase and strip whitespace from type and relationship columns."""
    df = df.copy()
    for col in ["source_type", "destination_type", "relationship"]:
        if col in df.columns:
            df[col] = df[col].astype(str).str.strip().str.lower()
    return df

# Clean names
df_folders = clean_dataframe_names(df_folders)
df_files = clean_dataframe_names(df_files)
df_llm_lineage = clean_dataframe_names(df_llm_lineage)

# Normalize types
df_folders = normalize_dataframe_types(df_folders)
df_files = normalize_dataframe_types(df_files)
df_llm_lineage = normalize_dataframe_types(df_llm_lineage)

# Combine dataframes (excluding df_ast to reduce duplication/noise)
df_combined = pd.concat([df_folders, df_files, df_llm_lineage], ignore_index=True)

# Remove duplicates based on all relationship-defining columns
df_combined.drop_duplicates(
    subset=["source_node", "source_type", "destination_node", "destination_type", "relationship"],
    inplace=True
)

# Remove self-referencing relationships
df_combined = df_combined[
    ~(
        (df_combined['source_node'] == df_combined['destination_node']) &
        (df_combined['source_type'] == df_combined['destination_type'])
    )
]

# Remove empty or invalid entries
valid_entries = (
    df_combined['source_node'].notna() & df_combined['destination_node'].notna() &
    (df_combined['source_node'] != '') & (df_combined['destination_node'] != '') &
    (df_combined['source_node'].str.lower() != 'none') & (df_combined['destination_node'].str.lower() != 'none')
)
df_combined = df_combined[valid_entries].reset_index(drop=True)

print(f'✅ Combined data: {len(df_combined)} relationships after deduplication and cleaning')


# ========== STAGE 5: FILE VS CLASS CORRECTION ==========

def correct_file_and_class_nodes(df: pd.DataFrame, df_files: pd.DataFrame, class_registry: dict) -> pd.DataFrame:
    """Corrects file and class node names for consistency using reference data."""
    df_corrected = df.copy()

    # Prepare lowercase mappings for fast lookup
    file_node_map = {
        node.replace('.java', '').lower(): node
        for node in df_files['destination_node'].dropna().unique()
    }

    class_node_map = {
        class_name.lower(): class_name
        for class_name in class_registry.keys()
    }

    for idx, row in df_corrected.iterrows():
        source_node = str(row.get("source_node", "")).strip()
        dest_node = str(row.get("destination_node", "")).strip()
        source_type = str(row.get("source_type", "")).strip().lower()
        dest_type = str(row.get("destination_type", "")).strip().lower()

        # --- File Node Correction ---
        if source_type == "file":
            clean_source = source_node.replace('.java', '').lower()
            if clean_source in file_node_map:
                df_corrected.at[idx, "source_node"] = file_node_map[clean_source]

        if dest_type == "file":
            clean_dest = dest_node.replace('.java', '').lower()
            if clean_dest in file_node_map:
                df_corrected.at[idx, "destination_node"] = file_node_map[clean_dest]

        # --- Class Node Correction ---
        if source_type == "class":
            clean_source = source_node.replace('.java', '').lower()
            if clean_source in class_node_map:
                df_corrected.at[idx, "source_node"] = class_node_map[clean_source]

        if dest_type == "class":
            clean_dest = dest_node.replace('.java', '').lower()
            if clean_dest in class_node_map:
                df_corrected.at[idx, "destination_node"] = class_node_map[clean_dest]

    return df_corrected

# Apply correction
df_corrected = correct_file_and_class_nodes(df_combined, df_files, class_registry)

print(f'✅ Stage 5 Complete: {len(df_corrected)} corrected relationships ready for Neo4j')


df_combined.sample(5)

df_combined.to_csv('combined_data_v7.csv', index=False)

# ========== STAGE 6: NEO4J INSERTION (ENHANCED) ==========

from tqdm import tqdm

# ⚠️ Clear existing graph (optional and destructive!)
graph.query("MATCH (n) DETACH DELETE n")

# Track failed rows
failed_rows = []

# ----------------------------
# 💡 Helper: Create MERGE Query
# ----------------------------
def create_node_merge(label, name, props=None):
    props_str = ", ".join([f"{k}: ${k}" for k in props]) if props else "name: $name"
    return f"MERGE (:{label} {{{props_str}}})"

def create_relationship_query(source_type, dest_type, relationship):
    return f"""
    MERGE (s:{source_type} {{name: $source_node}})
    MERGE (t:{dest_type} {{name: $destination_node}})
    MERGE (s)-[:{relationship}]->(t)
    """

# ----------------------------
# 🚀 Main Relationship Insertion
# ----------------------------
for idx, row in tqdm(df_corrected.iterrows(), total=len(df_corrected), desc="🚀 Inserting Neo4j Data"):
    try:
        source_node = str(row["source_node"]).strip()
        dest_node = str(row["destination_node"]).strip()
        source_type = str(row["source_type"]).strip().capitalize()
        dest_type = str(row["destination_type"]).strip().capitalize()
        relationship = str(row["relationship"]).strip().upper()

        if not all([source_node, dest_node, source_type, dest_type, relationship]):
            continue  # Skip incomplete rows

        # Standard relationship insertion
        query = create_relationship_query(source_type, dest_type, relationship)
        graph.query(query, {
            "source_node": source_node,
            "destination_node": dest_node
        })

    except Exception as e:
        failed_rows.append((idx, str(e)))
        continue

# ----------------------------
# 🔁 Handle Variable Transformations (Optional, if available)
# ----------------------------
if "df_variable_transformations" in globals():
    for _, row in tqdm(df_variable_transformations.iterrows(), desc="🔄 Inserting Variable Transformations"):
        try:
            method = row["method"]
            variable = row["variable"]
            var_type = row.get("type", "Unknown")
            op_name = row["operation"]
            expression = row["value"]

            # MERGE Method
            graph.query("""
            MERGE (m:Method {name: $method})
            """, {"method": method})

            # MERGE Variable
            graph.query("""
            MERGE (v:Variable {name: $variable, type: $var_type})
            """, {"variable": variable, "var_type": var_type})

            # Link Method -> Variable
            graph.query("""
            MATCH (m:Method {name: $method}), (v:Variable {name: $variable})
            MERGE (m)-[:USES]->(v)
            MERGE (m)-[:TRANSFORMS]->(v)
            """, {"method": method, "variable": variable})

            # Operation node
            graph.query("""
            MERGE (op:Operation {name: $op_name})
            MERGE (v:Variable {name: $variable})-[:UPDATED_BY]->(op)
            """, {"op_name": op_name, "variable": variable})

            # Value node
            graph.query("""
            MERGE (val:Value {expression: $expression})
            MERGE (op:Operation {name: $op_name})-[:ASSIGNS_VALUE]->(val)
            """, {"op_name": op_name, "expression": expression})

        except Exception as e:
            failed_rows.append(("var_transform", str(e)))

# ✅ Final Status
print(f"\n✅ Neo4j Insertion Complete: {len(df_corrected)} base relationships.")
if "df_variable_transformations" in globals():
    print(f"✅ Variable Transformations Inserted: {len(df_variable_transformations)}")

if failed_rows:
    print(f"⚠️ Failures: {len(failed_rows)} rows failed. You may review them.")



# ========== VALIDATION ==========
def validate_graph_statistics():
    node_count = graph.query("MATCH (n) RETURN count(n) as count")[0]["count"]
    rel_count = graph.query("MATCH ()-[r]->() RETURN count(r) as count")[0]["count"]

    print(f"Total Nodes in Neo4j: {node_count}")
    print(f"Total Relationships in Neo4j: {rel_count}")
    
    # Show breakdown by node type
    node_types = graph.query("MATCH (n) RETURN labels(n)[0] as type, count(n) as count ORDER BY count DESC")
    print('\n Node Types:')
    for row in node_types:
        print(f'  {row["type"]}: {row["count"]}')
    
    # Show breakdown by relationship type
    rel_types = graph.query("MATCH ()-[r]->() RETURN type(r) as type, count(r) as count ORDER BY count DESC")
    print('\n Relationship Types:')
    for row in rel_types:
        print(f'  {row["type"]}: {row["count"]}')
    
    # Show sample endpoints and tables
    endpoints = graph.query("MATCH (n:Endpoint) RETURN n.name as name LIMIT 5")
    if endpoints:
        print('\n Sample API Endpoints:')
        for ep in endpoints:
            print(f'  {ep["name"]}')
    
    tables = graph.query("MATCH (n:Table) RETURN n.name as name LIMIT 5")
    if tables:
        print('\nSample Database Tables:')
        for table in tables:
            print(f'  {table["name"]}')

validate_graph_statistics()


import pandas as pd
df = pd.read_csv('combined_data.csv')

df.sample(5)

filtered_df = df[(df['source_type'] == 'variable') | (df['destination_type'] == 'variable')]

filtered_df

import pandas as pd

def normalize_nodes(df):
    """
    Normalize the source_node and destination_node by taking only the last segment 
    if path-like strings are present.
    """
    df = df.copy()
    for col in ["source_node", "destination_node"]:
        df[col] = df[col].astype(str).apply(lambda x: x.replace("\\", "/").split("/")[-1])
    return df


from tqdm import tqdm

def push_to_neo4j(df_cleaned, graph):
    """
    Push cleaned DataFrame to Neo4j after clearing existing data.
    """
    # Clear existing data
    graph.query("MATCH (n) DETACH DELETE n")

    for idx, row in tqdm(df_cleaned.iterrows(), total=len(df_cleaned), desc="Final Neo4j Insertion"):
        try:
            source_node = str(row["source_node"]).strip()
            dest_node = str(row["destination_node"]).strip()
            source_type = str(row["source_type"]).strip().capitalize()
            dest_type = str(row["destination_type"]).strip().capitalize()
            relationship = str(row["relationship"]).strip().upper()

            if not all([source_node, dest_node, source_type, dest_type, relationship]):
                continue

            query = f"""
            MERGE (s:{source_type} {{name: $source_node}})
            MERGE (t:{dest_type} {{name: $destination_node}})
            MERGE (s)-[:{relationship}]->(t)
            """
            graph.query(query, {
                "source_node": source_node,
                "destination_node": dest_node
            })
        except Exception as e:
            pass  # You may want to log this for debugging

    print("✅ Final consistent data pushed to Neo4j.")


# Step 1: Normalize the CSV DataFrame
df_corrected = normalize_nodes(df)

# Step 2: Push to Neo4j
push_to_neo4j(df_corrected, graph)


filtered_df = df_corrected[(df_corrected['source_type'] == 'folder')]

filtered_df