package com.bolt.dashboard.core.model;

import java.util.HashSet;
import java.util.Set;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "Configuration")
public class ConfigurationSetting extends BaseModel {

    private long timestamp;
    private boolean baseline;
    private String projectName;
    private boolean addFlag;
    private String projectType;
    private boolean manualData;
    private Set<ConfigurationToolInfoMetric> metric = new HashSet<>();
    
    public boolean isManualData() {
		return manualData;
	}


	public void setManualData(boolean manualData) {
		this.manualData = manualData;
	}


	public ConfigurationSetting(){
    	this.manualData = false;
    }
    

    public long getTimestamp() {

        return timestamp;
    }

    public void setTimestamp(long timestamp) {

        this.timestamp = timestamp;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public Set<ConfigurationToolInfoMetric> getMetrics() {
        return metric;
    }

    public boolean isAddFlag() {
        return addFlag;
    }

    public void setAddFlag(boolean addFlag) {
        this.addFlag = addFlag;
    }

    public boolean isBaseline() {
        return baseline;
    }

    public void setBaseline(boolean baseline) {
        this.baseline = baseline;
    }

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType;
    }

}
